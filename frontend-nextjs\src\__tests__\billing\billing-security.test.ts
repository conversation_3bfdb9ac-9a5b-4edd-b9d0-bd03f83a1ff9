// Security tests for billing system
// Tests authentication, authorization, input validation, and security measures

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { DataEncryption, <PERSON>tLogger, SecurityValidator } from '../../lib/billing-security';
import { BillingValidator } from '../../lib/billing-validation';
import { Billing<PERSON>rror<PERSON><PERSON>ler, BillingErrorCode } from '../../lib/billing-error-handler';

// Mock environment variables
const originalEnv = process.env;

describe('Billing Security Tests', () => {
  beforeEach(() => {
    jest.resetModules();
    process.env = {
      ...originalEnv,
      BILLING_ENCRYPTION_KEY: '0123456789abcdef0123456789abcdef0123456789abcdef0123456789abcdef',
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('Data Encryption', () => {
    it('should encrypt and decrypt data correctly', () => {
      const sensitiveData = '****************'; // Test credit card number
      
      const encrypted = DataEncryption.encrypt(sensitiveData);
      expect(encrypted.encrypted).toBeDefined();
      expect(encrypted.iv).toBeDefined();
      expect(encrypted.tag).toBeDefined();
      expect(encrypted.encrypted).not.toBe(sensitiveData);

      const decrypted = DataEncryption.decrypt(encrypted);
      expect(decrypted).toBe(sensitiveData);
    });

    it('should produce different encrypted values for same input', () => {
      const data = 'sensitive information';
      
      const encrypted1 = DataEncryption.encrypt(data);
      const encrypted2 = DataEncryption.encrypt(data);
      
      // Should be different due to random IV
      expect(encrypted1.encrypted).not.toBe(encrypted2.encrypted);
      expect(encrypted1.iv).not.toBe(encrypted2.iv);
      
      // But both should decrypt to same value
      expect(DataEncryption.decrypt(encrypted1)).toBe(data);
      expect(DataEncryption.decrypt(encrypted2)).toBe(data);
    });

    it('should fail decryption with tampered data', () => {
      const data = 'sensitive information';
      const encrypted = DataEncryption.encrypt(data);
      
      // Tamper with encrypted data
      const tamperedEncrypted = {
        ...encrypted,
        encrypted: encrypted.encrypted.slice(0, -2) + 'xx',
      };
      
      expect(() => {
        DataEncryption.decrypt(tamperedEncrypted);
      }).toThrow();
    });

    it('should handle encryption key validation', () => {
      delete process.env.BILLING_ENCRYPTION_KEY;
      
      expect(() => {
        DataEncryption.encrypt('test data');
      }).toThrow('BILLING_ENCRYPTION_KEY environment variable is required');
    });

    it('should encrypt financial data fields', () => {
      const financialData = {
        cardNumber: '****************',
        cvv: '123',
        bankAccount: '**********',
        amount: 1000,
        notes: 'Regular payment',
      };

      const encrypted = DataEncryption.encryptFinancialData(financialData);
      
      expect(encrypted.cardNumber).not.toBe(financialData.cardNumber);
      expect(encrypted.cvv).not.toBe(financialData.cvv);
      expect(encrypted.bankAccount).not.toBe(financialData.bankAccount);
      expect(encrypted.amount).toBe(financialData.amount); // Amount should not be encrypted
      expect(encrypted.notes).toBe(financialData.notes); // Notes should not be encrypted
    });
  });

  describe('Input Validation Security', () => {
    it('should prevent SQL injection attempts', () => {
      const maliciousInputs = [
        "'; DROP TABLE bills; --",
        "1' OR '1'='1",
        "admin'/*",
        "1; DELETE FROM payments WHERE 1=1; --",
      ];

      maliciousInputs.forEach(input => {
        expect(() => {
          BillingValidator.validateSearchParams({ patientId: input });
        }).toThrow();
      });
    });

    it('should prevent XSS attacks', () => {
      const xssPayloads = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("xss")',
        '<svg onload="alert(1)">',
        '"><script>alert("xss")</script>',
      ];

      xssPayloads.forEach(payload => {
        const result = BillingValidator.validateBillData({
          patient: 'patient-123',
          billType: 'treatment',
          subtotal: 1000,
          totalAmount: 1000,
          notes: payload,
        });

        expect(result.notes).not.toContain('<script>');
        expect(result.notes).not.toContain('javascript:');
        expect(result.notes).not.toContain('onerror');
        expect(result.notes).not.toContain('onload');
      });
    });

    it('should validate amount limits to prevent overflow attacks', () => {
      const maliciousAmounts = [
        Number.MAX_SAFE_INTEGER,
        Number.POSITIVE_INFINITY,
        -Number.MAX_SAFE_INTEGER,
        Number.NaN,
      ];

      maliciousAmounts.forEach(amount => {
        expect(() => {
          BillingValidator.validatePaymentData({
            billId: 'bill-123',
            amount: amount,
            paymentMethod: 'cash',
          });
        }).toThrow();
      });
    });

    it('should prevent path traversal attacks', () => {
      const pathTraversalInputs = [
        '../../../etc/passwd',
        '..\\..\\..\\windows\\system32\\config\\sam',
        '/etc/shadow',
        'C:\\Windows\\System32\\config\\SAM',
      ];

      pathTraversalInputs.forEach(input => {
        expect(() => {
          BillingValidator.validateSearchParams({ patientId: input });
        }).toThrow();
      });
    });

    it('should limit input length to prevent DoS attacks', () => {
      const longString = 'a'.repeat(10000);
      
      expect(() => {
        BillingValidator.validateBillData({
          patient: 'patient-123',
          billType: 'treatment',
          subtotal: 1000,
          totalAmount: 1000,
          notes: longString,
        });
      }).toThrow();
    });
  });

  describe('Audit Logging', () => {
    let auditLogger: AuditLogger;

    beforeEach(() => {
      auditLogger = new AuditLogger();
    });

    it('should log financial operations', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation();
      
      auditLogger.logFinancialOperation({
        operation: 'CREATE_BILL',
        userId: 'user-123',
        resourceId: 'bill-456',
        amount: 1000,
        details: { billType: 'treatment' },
      });

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('AUDIT'),
        expect.objectContaining({
          operation: 'CREATE_BILL',
          userId: 'user-123',
          resourceId: 'bill-456',
        })
      );

      logSpy.mockRestore();
    });

    it('should sanitize sensitive data in logs', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation();
      
      auditLogger.logFinancialOperation({
        operation: 'PROCESS_PAYMENT',
        userId: 'user-123',
        resourceId: 'payment-456',
        amount: 1000,
        details: {
          cardNumber: '****************',
          cvv: '123',
          notes: 'Payment processed',
        },
      });

      const logCall = logSpy.mock.calls[0];
      const loggedData = JSON.stringify(logCall);
      
      expect(loggedData).not.toContain('****************');
      expect(loggedData).not.toContain('123');
      expect(loggedData).toContain('[REDACTED]');

      logSpy.mockRestore();
    });

    it('should include security context in logs', () => {
      const logSpy = jest.spyOn(console, 'log').mockImplementation();
      
      auditLogger.logSecurityEvent({
        event: 'SUSPICIOUS_ACTIVITY',
        userId: 'user-123',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
        details: { reason: 'Multiple failed login attempts' },
      });

      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('SECURITY'),
        expect.objectContaining({
          event: 'SUSPICIOUS_ACTIVITY',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
        })
      );

      logSpy.mockRestore();
    });
  });

  describe('Rate Limiting', () => {
    it('should track request rates per user', () => {
      const rateLimiter = new SecurityValidator();
      const userId = 'user-123';
      
      // Make multiple requests rapidly
      for (let i = 0; i < 5; i++) {
        const allowed = rateLimiter.checkRateLimit(userId, 'payment');
        if (i < 3) {
          expect(allowed).toBe(true);
        } else {
          expect(allowed).toBe(false);
        }
      }
    });

    it('should reset rate limits after time window', async () => {
      const rateLimiter = new SecurityValidator();
      const userId = 'user-123';
      
      // Exhaust rate limit
      for (let i = 0; i < 5; i++) {
        rateLimiter.checkRateLimit(userId, 'payment');
      }
      
      expect(rateLimiter.checkRateLimit(userId, 'payment')).toBe(false);
      
      // Wait for rate limit reset (mock time passage)
      jest.advanceTimersByTime(60000); // 1 minute
      
      expect(rateLimiter.checkRateLimit(userId, 'payment')).toBe(true);
    });
  });

  describe('Session Security', () => {
    it('should validate session tokens', () => {
      const validator = new SecurityValidator();
      
      const validToken = 'valid-jwt-token';
      const invalidToken = 'invalid-token';
      const expiredToken = 'expired-jwt-token';
      
      expect(validator.validateSession(validToken)).toBe(true);
      expect(validator.validateSession(invalidToken)).toBe(false);
      expect(validator.validateSession(expiredToken)).toBe(false);
    });

    it('should detect session hijacking attempts', () => {
      const validator = new SecurityValidator();
      
      const sessionData = {
        userId: 'user-123',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0...',
      };
      
      // Normal session
      expect(validator.validateSessionSecurity(sessionData)).toBe(true);
      
      // Suspicious IP change
      const suspiciousSession = {
        ...sessionData,
        ipAddress: '********', // Different IP
      };
      
      expect(validator.validateSessionSecurity(suspiciousSession)).toBe(false);
    });
  });

  describe('Permission Validation', () => {
    it('should enforce role-based access control', () => {
      const validator = new SecurityValidator();
      
      const adminUser = { role: 'admin', permissions: ['all'] };
      const doctorUser = { role: 'doctor', permissions: ['view_bills'] };
      const frontDeskUser = { role: 'front-desk', permissions: ['create_bills', 'process_payments'] };
      
      expect(validator.hasPermission(adminUser, 'delete_bills')).toBe(true);
      expect(validator.hasPermission(doctorUser, 'delete_bills')).toBe(false);
      expect(validator.hasPermission(frontDeskUser, 'process_payments')).toBe(true);
      expect(validator.hasPermission(frontDeskUser, 'view_sensitive_reports')).toBe(false);
    });

    it('should validate resource ownership', () => {
      const validator = new SecurityValidator();
      
      const user = { id: 'user-123', role: 'doctor' };
      const ownedResource = { createdBy: 'user-123' };
      const otherResource = { createdBy: 'user-456' };
      
      expect(validator.canAccessResource(user, ownedResource)).toBe(true);
      expect(validator.canAccessResource(user, otherResource)).toBe(false);
    });
  });

  describe('Data Integrity', () => {
    it('should validate financial calculations', () => {
      const validator = new SecurityValidator();
      
      const validBill = {
        subtotal: 1000,
        discountAmount: 100,
        taxAmount: 50,
        totalAmount: 950,
      };
      
      const invalidBill = {
        subtotal: 1000,
        discountAmount: 100,
        taxAmount: 50,
        totalAmount: 1000, // Incorrect calculation
      };
      
      expect(validator.validateFinancialCalculation(validBill)).toBe(true);
      expect(validator.validateFinancialCalculation(invalidBill)).toBe(false);
    });

    it('should detect suspicious transaction patterns', () => {
      const validator = new SecurityValidator();
      
      const normalTransactions = [
        { amount: 100, timestamp: Date.now() },
        { amount: 200, timestamp: Date.now() + 60000 },
        { amount: 150, timestamp: Date.now() + 120000 },
      ];
      
      const suspiciousTransactions = [
        { amount: 10000, timestamp: Date.now() }, // Large amount
        { amount: 10000, timestamp: Date.now() + 1000 }, // Rapid large amounts
        { amount: 10000, timestamp: Date.now() + 2000 },
      ];
      
      expect(validator.detectSuspiciousPattern(normalTransactions)).toBe(false);
      expect(validator.detectSuspiciousPattern(suspiciousTransactions)).toBe(true);
    });
  });

  describe('Error Handling Security', () => {
    it('should not leak sensitive information in error messages', () => {
      const sensitiveError = new Error('Database connection failed: password=secret123');
      
      const sanitizedError = BillingErrorHandler.sanitizeError(sensitiveError);
      
      expect(sanitizedError.message).not.toContain('password=secret123');
      expect(sanitizedError.message).toContain('Database connection failed');
    });

    it('should log security errors appropriately', () => {
      const logSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const securityError = BillingErrorHandler.createError(
        BillingErrorCode.SUSPICIOUS_ACTIVITY,
        { reason: 'Multiple failed attempts' },
        { userId: 'user-123', ipAddress: '*************' }
      );
      
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('Billing Error'),
        expect.stringContaining('SUSPICIOUS_ACTIVITY')
      );

      logSpy.mockRestore();
    });
  });

  describe('Compliance and Privacy', () => {
    it('should handle PII data according to privacy requirements', () => {
      const piiData = {
        patientName: 'John Doe',
        ssn: '***********',
        email: '<EMAIL>',
        phone: '555-1234',
        medicalNotes: 'Confidential medical information',
      };
      
      const anonymized = DataEncryption.anonymizePII(piiData);
      
      expect(anonymized.patientName).not.toBe(piiData.patientName);
      expect(anonymized.ssn).toBe('[REDACTED]');
      expect(anonymized.email).toMatch(/\*+@example\.com/);
      expect(anonymized.medicalNotes).toBe('[REDACTED]');
    });

    it('should support data retention policies', () => {
      const auditLogger = new AuditLogger();
      
      // Mock old audit entries
      const oldEntries = [
        { timestamp: Date.now() - (400 * 24 * 60 * 60 * 1000) }, // 400 days old
        { timestamp: Date.now() - (200 * 24 * 60 * 60 * 1000) }, // 200 days old
      ];
      
      const retainedEntries = auditLogger.applyRetentionPolicy(oldEntries, 365);
      
      expect(retainedEntries).toHaveLength(1); // Only entries within 365 days
    });
  });
});
