// Unit tests for billing validation system
// Tests input validation, sanitization, and security measures

import { describe, it, expect, beforeEach } from '@jest/globals';
import { BillingValidator, BILLING_SCHEMAS } from '../../lib/billing-validation';
import { Billing<PERSON><PERSON>r<PERSON><PERSON><PERSON>, BillingErrorCode } from '../../lib/billing-error-handler';

describe('BillingValidator', () => {
  describe('validateBillData', () => {
    it('should validate valid bill data', () => {
      const validBillData = {
        patient: 'patient-123',
        billType: 'treatment',
        subtotal: 1000,
        discountAmount: 100,
        taxAmount: 50,
        totalAmount: 950,
        notes: '面部护理治疗',
      };

      const result = BillingValidator.validateBillData(validBillData);
      expect(result).toEqual(validBillData);
    });

    it('should throw error for invalid amount', () => {
      const invalidBillData = {
        patient: 'patient-123',
        billType: 'treatment',
        subtotal: -100, // Invalid negative amount
        totalAmount: -100,
      };

      expect(() => {
        BillingValidator.validateBillData(invalidBillData);
      }).toThrow();
    });

    it('should throw error for mismatched total amount', () => {
      const invalidBillData = {
        patient: 'patient-123',
        billType: 'treatment',
        subtotal: 1000,
        discountAmount: 100,
        taxAmount: 50,
        totalAmount: 1000, // Should be 950
      };

      expect(() => {
        BillingValidator.validateBillData(invalidBillData);
      }).toThrow();
    });

    it('should sanitize HTML in notes', () => {
      const billDataWithHtml = {
        patient: 'patient-123',
        billType: 'treatment',
        subtotal: 1000,
        totalAmount: 1000,
        notes: '<script>alert("xss")</script>正常备注',
      };

      const result = BillingValidator.validateBillData(billDataWithHtml);
      expect(result.notes).not.toContain('<script>');
      expect(result.notes).toContain('正常备注');
    });

    it('should validate required fields', () => {
      const incompleteBillData = {
        billType: 'treatment',
        subtotal: 1000,
        // Missing required patient field
      };

      expect(() => {
        BillingValidator.validateBillData(incompleteBillData);
      }).toThrow();
    });
  });

  describe('validatePaymentData', () => {
    it('should validate valid payment data', () => {
      const validPaymentData = {
        billId: 'bill-123',
        amount: 500.50,
        paymentMethod: 'cash',
        transactionId: 'TXN-123456789',
        notes: '现金支付',
      };

      const result = BillingValidator.validatePaymentData(validPaymentData);
      expect(result.amount).toBe(500.50);
      expect(result.paymentMethod).toBe('cash');
    });

    it('should throw error for invalid payment method', () => {
      const invalidPaymentData = {
        billId: 'bill-123',
        amount: 500,
        paymentMethod: 'invalid-method',
      };

      expect(() => {
        BillingValidator.validatePaymentData(invalidPaymentData);
      }).toThrow();
    });

    it('should validate amount precision', () => {
      const paymentDataWithPrecision = {
        billId: 'bill-123',
        amount: 500.123, // More than 2 decimal places
        paymentMethod: 'cash',
      };

      expect(() => {
        BillingValidator.validatePaymentData(paymentDataWithPrecision);
      }).toThrow();
    });

    it('should validate transaction ID format', () => {
      const paymentDataWithInvalidTxnId = {
        billId: 'bill-123',
        amount: 500,
        paymentMethod: 'card',
        transactionId: 'invalid-format',
      };

      expect(() => {
        BillingValidator.validatePaymentData(paymentDataWithInvalidTxnId);
      }).toThrow();
    });
  });

  describe('validateDepositData', () => {
    it('should validate valid deposit data', () => {
      const validDepositData = {
        patient: 'patient-123',
        amount: 2000,
        purpose: '治疗预付款',
        expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
      };

      const result = BillingValidator.validateDepositData(validDepositData);
      expect(result.amount).toBe(2000);
      expect(result.purpose).toBe('治疗预付款');
    });

    it('should throw error for excessive deposit amount', () => {
      const invalidDepositData = {
        patient: 'patient-123',
        amount: 25000, // Exceeds maximum
        purpose: '治疗预付款',
      };

      expect(() => {
        BillingValidator.validateDepositData(invalidDepositData);
      }).toThrow();
    });

    it('should validate purpose length', () => {
      const invalidDepositData = {
        patient: 'patient-123',
        amount: 2000,
        purpose: 'a'.repeat(201), // Exceeds maximum length
      };

      expect(() => {
        BillingValidator.validateDepositData(invalidDepositData);
      }).toThrow();
    });
  });

  describe('validateSearchParams', () => {
    it('should validate valid search parameters', () => {
      const validSearchParams = {
        patientId: 'patient-123',
        status: 'pending',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
        minAmount: 100,
        maxAmount: 5000,
        limit: 50,
        page: 1,
      };

      const result = BillingValidator.validateSearchParams(validSearchParams);
      expect(result.limit).toBe(50);
      expect(result.page).toBe(1);
    });

    it('should throw error for invalid date range', () => {
      const invalidSearchParams = {
        startDate: '2024-12-31',
        endDate: '2024-01-01', // End date before start date
      };

      expect(() => {
        BillingValidator.validateSearchParams(invalidSearchParams);
      }).toThrow();
    });

    it('should validate pagination limits', () => {
      const invalidSearchParams = {
        limit: 1500, // Exceeds maximum
        page: 0, // Invalid page number
      };

      expect(() => {
        BillingValidator.validateSearchParams(invalidSearchParams);
      }).toThrow();
    });
  });

  describe('Field validation methods', () => {
    it('should validate string fields correctly', () => {
      const schema = { name: { required: true, type: 'string' as const, minLength: 2, maxLength: 50 } };
      
      // Valid string
      const validResult = BillingValidator.validate({ name: '张三' }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.name).toBe('张三');

      // Too short
      const shortResult = BillingValidator.validate({ name: 'a' }, schema);
      expect(shortResult.isValid).toBe(false);
      expect(shortResult.errors).toContain('name 长度不能少于 2 个字符');

      // Too long
      const longResult = BillingValidator.validate({ name: 'a'.repeat(51) }, schema);
      expect(longResult.isValid).toBe(false);
      expect(longResult.errors).toContain('name 长度不能超过 50 个字符');
    });

    it('should validate number fields correctly', () => {
      const schema = { amount: { required: true, type: 'number' as const, min: 0, max: 10000 } };
      
      // Valid number
      const validResult = BillingValidator.validate({ amount: 500 }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.amount).toBe(500);

      // Below minimum
      const belowMinResult = BillingValidator.validate({ amount: -100 }, schema);
      expect(belowMinResult.isValid).toBe(false);
      expect(belowMinResult.errors).toContain('amount 不能小于 0');

      // Above maximum
      const aboveMaxResult = BillingValidator.validate({ amount: 15000 }, schema);
      expect(aboveMaxResult.isValid).toBe(false);
      expect(aboveMaxResult.errors).toContain('amount 不能大于 10000');
    });

    it('should validate currency fields correctly', () => {
      const schema = { price: { required: true, type: 'currency' as const, min: 0.01, max: 1000 } };
      
      // Valid currency
      const validResult = BillingValidator.validate({ price: 99.99 }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.price).toBe(99.99);

      // Too many decimal places
      const precisionResult = BillingValidator.validate({ price: 99.999 }, schema);
      expect(precisionResult.isValid).toBe(false);
      expect(precisionResult.errors).toContain('price 最多只能有两位小数');
    });

    it('should validate email fields correctly', () => {
      const schema = { email: { required: true, type: 'email' as const } };
      
      // Valid email
      const validResult = BillingValidator.validate({ email: '<EMAIL>' }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.email).toBe('<EMAIL>');

      // Invalid email
      const invalidResult = BillingValidator.validate({ email: 'invalid-email' }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('email 邮箱格式不正确');
    });

    it('should validate phone fields correctly', () => {
      const schema = { phone: { required: true, type: 'phone' as const } };
      
      // Valid phone
      const validResult = BillingValidator.validate({ phone: '13800138000' }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.phone).toBe('13800138000');

      // Invalid phone
      const invalidResult = BillingValidator.validate({ phone: 'abc123' }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('phone 电话号码格式不正确');
    });

    it('should validate date fields correctly', () => {
      const schema = { date: { required: true, type: 'date' as const } };
      
      // Valid date
      const validResult = BillingValidator.validate({ date: '2024-01-01' }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.date).toBe(new Date('2024-01-01').toISOString());

      // Invalid date
      const invalidResult = BillingValidator.validate({ date: 'invalid-date' }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('date 日期格式不正确');
    });

    it('should validate ID fields correctly', () => {
      const schema = { id: { required: true, type: 'id' as const } };
      
      // Valid ID
      const validResult = BillingValidator.validate({ id: 'user-123' }, schema);
      expect(validResult.isValid).toBe(true);
      expect(validResult.sanitizedData.id).toBe('user-123');

      // Invalid ID with special characters
      const invalidResult = BillingValidator.validate({ id: 'user@123!' }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('id ID格式不正确');
    });
  });

  describe('HTML sanitization', () => {
    it('should sanitize HTML tags', () => {
      const schema = { content: { required: true, type: 'string' as const } };
      
      const result = BillingValidator.validate({ 
        content: '<script>alert("xss")</script><p>正常内容</p>' 
      }, schema);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.content).not.toContain('<script>');
      expect(result.sanitizedData.content).not.toContain('<p>');
      expect(result.sanitizedData.content).toContain('正常内容');
    });

    it('should sanitize quotes and special characters', () => {
      const schema = { content: { required: true, type: 'string' as const } };
      
      const result = BillingValidator.validate({ 
        content: 'Test "quotes" and \'apostrophes\' and /slashes/' 
      }, schema);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.content).toContain('&quot;');
      expect(result.sanitizedData.content).toContain('&#x27;');
      expect(result.sanitizedData.content).toContain('&#x2F;');
    });
  });

  describe('Pattern validation', () => {
    it('should validate custom patterns', () => {
      const schema = { 
        billNumber: { 
          required: true, 
          type: 'string' as const, 
          pattern: /^BILL-\d{8}-\d{6}$/ 
        } 
      };
      
      // Valid pattern
      const validResult = BillingValidator.validate({ 
        billNumber: 'BILL-20240101-123456' 
      }, schema);
      expect(validResult.isValid).toBe(true);

      // Invalid pattern
      const invalidResult = BillingValidator.validate({ 
        billNumber: 'INVALID-FORMAT' 
      }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('billNumber 格式不正确');
    });
  });

  describe('Custom validators', () => {
    it('should use custom validation functions', () => {
      const schema = { 
        evenNumber: { 
          required: true, 
          type: 'number' as const,
          customValidator: (value: number) => value % 2 === 0
        } 
      };
      
      // Valid even number
      const validResult = BillingValidator.validate({ evenNumber: 4 }, schema);
      expect(validResult.isValid).toBe(true);

      // Invalid odd number
      const invalidResult = BillingValidator.validate({ evenNumber: 3 }, schema);
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toContain('evenNumber 自定义验证失败');
    });
  });
});
