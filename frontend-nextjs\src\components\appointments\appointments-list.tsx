'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useRole, PermissionGate } from '@/lib/role-context';
import { IconPlus, IconCalendar, IconEdit, IconTrash, IconCheck, IconX, IconList, IconTable } from '@tabler/icons-react';
import { appointmentsApi } from '@/lib/api';
import { Appointment } from '@/types/clinic';
import { DataTable } from '@/components/ui/table/data-table';
import { DataTableToolbar } from '@/components/ui/table/data-table-toolbar';
import { ColumnDef } from '@tanstack/react-table';
import { useDataTable } from '@/hooks/use-data-table';
import { AppointmentFormDialog } from './appointment-form-dialog';
import { AppointmentCalendar } from './appointment-calendar';
import { ConfirmationDialog } from '@/components/ui/confirmation-dialog';
import { AppointmentFiltersComponent, AppointmentFilters } from './appointment-filters';
import { toast } from 'sonner';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { t } from '@/lib/translations';

// Status badge component
const StatusBadge = ({ status }: { status: string }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status.toLowerCase()) {
      case 'scheduled':
        return t('appointments.status.scheduled');
      case 'completed':
        return t('appointments.status.completed');
      case 'cancelled':
        return t('appointments.status.cancelled');
      default:
        return status;
    }
  };

  return (
    <Badge className={getStatusColor(status)}>
      {getStatusText(status)}
    </Badge>
  );
};

// Table columns definition - will be created inside component to access handlers
const createColumns = (
  onEdit: (appointment: Appointment) => void,
  onDelete: (appointment: Appointment) => void,
  onStatusChange: (appointment: Appointment, status: 'completed' | 'cancelled') => void
): ColumnDef<Appointment>[] => [
  {
    accessorKey: 'patient',
    header: '患者',
    cell: ({ row }) => {
      const patient = row.getValue('patient') as any;
      return patient?.fullName || '未知患者';
    },
  },
  {
    accessorKey: 'treatment',
    header: '治疗项目',
    cell: ({ row }) => {
      const treatment = row.getValue('treatment') as any;
      return treatment?.name || '未知治疗';
    },
  },
  {
    accessorKey: 'appointmentDate',
    header: '日期时间',
    cell: ({ row }) => {
      const date = row.getValue('appointmentDate') as string;
      const dateObj = new Date(date);
      return (
        <div>
          <div>{dateObj.toLocaleDateString()}</div>
          <div className="text-sm text-muted-foreground">
            {dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'price',
    header: '价格',
    cell: ({ row }) => {
      const price = row.getValue('price') as number;
      return `¥${price}`;
    },
  },
  {
    accessorKey: 'status',
    header: '状态',
    cell: ({ row }) => {
      const status = row.getValue('status') as string;
      return <StatusBadge status={status} />;
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: ({ row }) => {
      const appointment = row.original;

      return (
        <div className="flex items-center gap-2">
          {appointment.status === 'scheduled' && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(appointment, 'completed')}
                className="text-green-600 hover:text-green-700"
              >
                <IconCheck className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onStatusChange(appointment, 'cancelled')}
                className="text-red-600 hover:text-red-700"
              >
                <IconX className="h-4 w-4" />
              </Button>
            </>
          )}

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <IconEdit className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => onEdit(appointment)}>
                <IconEdit className="h-4 w-4 mr-2" />
                {t('common.actions.edit')}
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete(appointment)}
                className="text-red-600"
              >
                <IconTrash className="h-4 w-4 mr-2" />
                {t('common.actions.delete')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      );
    },
  },
];

export function AppointmentsList() {
  const { hasPermission, user } = useRole();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formDialogOpen, setFormDialogOpen] = useState(false);
  const [editingAppointment, setEditingAppointment] = useState<Appointment | undefined>();
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [appointmentToDelete, setAppointmentToDelete] = useState<Appointment | undefined>();
  const [actionLoading, setActionLoading] = useState(false);
  const [filters, setFilters] = useState<AppointmentFilters>({
    search: '',
    status: 'all',
    dateFrom: undefined,
    dateTo: undefined,
  });
  const [currentView, setCurrentView] = useState<'table' | 'calendar'>('table');

  const fetchAppointments = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await appointmentsApi.getAll({ limit: 100 });
      setAppointments(response.docs);
    } catch (err) {
      console.error('Failed to fetch appointments:', err);
      setError('Failed to load appointments. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Filter appointments based on current filters
  useEffect(() => {
    let filtered = [...appointments];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(appointment =>
        appointment.patient.fullName.toLowerCase().includes(searchLower) ||
        appointment.treatment.name.toLowerCase().includes(searchLower)
      );
    }

    // Status filter
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(appointment => appointment.status === filters.status);
    }

    // Date range filter
    if (filters.dateFrom) {
      filtered = filtered.filter(appointment =>
        new Date(appointment.appointmentDate) >= filters.dateFrom!
      );
    }

    if (filters.dateTo) {
      const endOfDay = new Date(filters.dateTo);
      endOfDay.setHours(23, 59, 59, 999);
      filtered = filtered.filter(appointment =>
        new Date(appointment.appointmentDate) <= endOfDay
      );
    }

    setFilteredAppointments(filtered);
  }, [appointments, filters]);

  useEffect(() => {
    fetchAppointments();
  }, []);

  const handleEdit = (appointment: Appointment) => {
    setEditingAppointment(appointment);
    setFormDialogOpen(true);
  };

  const handleDelete = (appointment: Appointment) => {
    setAppointmentToDelete(appointment);
    setDeleteDialogOpen(true);
  };

  const handleStatusChange = async (appointment: Appointment, status: 'completed' | 'cancelled') => {
    setActionLoading(true);
    try {
      await appointmentsApi.update(appointment.id, { status });
      toast.success(`Appointment ${status} successfully`);
      fetchAppointments(); // Refresh the list
    } catch (error) {
      console.error('Failed to update appointment status:', error);
      toast.error('Failed to update appointment status');
    } finally {
      setActionLoading(false);
    }
  };

  const handleDeleteConfirm = async () => {
    if (!appointmentToDelete) return;

    setActionLoading(true);
    try {
      await appointmentsApi.delete(appointmentToDelete.id);
      toast.success('Appointment deleted successfully');
      setDeleteDialogOpen(false);
      setAppointmentToDelete(undefined);
      fetchAppointments(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete appointment:', error);
      toast.error('Failed to delete appointment');
    } finally {
      setActionLoading(false);
    }
  };

  const handleFormSuccess = () => {
    setFormDialogOpen(false);
    setEditingAppointment(undefined);
    fetchAppointments(); // Refresh the list
  };

  const handleNewAppointment = () => {
    setEditingAppointment(undefined);
    setFormDialogOpen(true);
  };

  const columns = createColumns(handleEdit, handleDelete, handleStatusChange);

  // Create table using useDataTable hook
  const { table } = useDataTable({
    data: filteredAppointments,
    columns,
    pageCount: Math.ceil(filteredAppointments.length / 10), // Assuming 10 items per page
    shallow: true, // Use shallow routing to prevent infinite loops
    debounceMs: 500
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t('appointments.loadingAppointments')}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={fetchAppointments} variant="outline">
            重试
          </Button>
        </div>
      </div>
    );
  }

  if (appointments.length === 0) {
    return (
      <>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <IconCalendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">{t('appointments.noAppointments')}</h3>
            <p className="text-muted-foreground mb-4">
              开始安排您的第一个预约。
            </p>
            <PermissionGate permission="canCreateAppointments">
              <Button onClick={handleNewAppointment}>
                <IconPlus className="h-4 w-4 mr-2" />
                {t('appointments.newAppointment')}
              </Button>
            </PermissionGate>
          </div>
        </div>

        <AppointmentFormDialog
          open={formDialogOpen}
          onOpenChange={setFormDialogOpen}
          appointment={editingAppointment}
          onSuccess={handleFormSuccess}
        />
      </>
    );
  }

  return (
    <>
      <div className="space-y-4">
        {/* Header with New Appointment button */}
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">
              {filteredAppointments.length} / {appointments.length} {t('appointments.appointmentsCount')}
            </h3>
          </div>
          <PermissionGate permission="canCreateAppointments">
            <Button onClick={handleNewAppointment}>
              <IconPlus className="h-4 w-4 mr-2" />
              {t('appointments.newAppointment')}
            </Button>
          </PermissionGate>
        </div>

        {/* Filters */}
        <AppointmentFiltersComponent
          filters={filters}
          onFiltersChange={setFilters}
        />

        {/* Data Table */}
        <DataTable table={table}>
          <DataTableToolbar table={table} />
        </DataTable>

        {filteredAppointments.length === 0 && appointments.length > 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">没有预约符合当前筛选条件。</p>
          </div>
        )}
      </div>

      <AppointmentFormDialog
        open={formDialogOpen}
        onOpenChange={setFormDialogOpen}
        appointment={editingAppointment}
        onSuccess={handleFormSuccess}
      />

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="删除预约"
        description={`您确定要删除 ${appointmentToDelete?.patient.fullName} 的预约吗？此操作无法撤销。`}
        confirmText="删除"
        variant="destructive"
        onConfirm={handleDeleteConfirm}
        loading={actionLoading}
      />
    </>
  );
}
