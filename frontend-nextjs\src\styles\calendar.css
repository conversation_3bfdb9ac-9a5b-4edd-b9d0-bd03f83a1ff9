/* Custom styles for react-big-calendar to match Shadcn/ui theme */

/* Calendar container */
.rbc-calendar {
  @apply bg-background text-foreground;
  font-family: inherit;
}

/* Header styles */
.rbc-header {
  @apply bg-muted/50 text-muted-foreground font-medium text-sm border-b border-border;
  padding: 8px 4px;
}

.rbc-header + .rbc-header {
  @apply border-l border-border;
}

/* Toolbar styles */
.rbc-toolbar {
  @apply flex items-center justify-between mb-4 p-2 bg-muted/30 rounded-lg;
}

.rbc-toolbar button {
  @apply px-3 py-1.5 text-sm font-medium rounded-md transition-colors;
  @apply hover:bg-accent hover:text-accent-foreground;
  @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  border: 1px solid transparent;
}

.rbc-toolbar button:hover {
  @apply bg-accent text-accent-foreground;
}

.rbc-toolbar button.rbc-active {
  @apply bg-primary text-primary-foreground;
}

.rbc-toolbar-label {
  @apply text-lg font-semibold text-foreground;
}

/* Month view styles */
.rbc-month-view {
  @apply border border-border rounded-lg overflow-hidden;
}

.rbc-month-row {
  @apply border-b border-border last:border-b-0;
}

.rbc-date-cell {
  @apply text-right p-1;
}

.rbc-date-cell > a {
  @apply text-sm text-muted-foreground hover:text-foreground;
}

.rbc-date-cell.rbc-off-range {
  @apply text-muted-foreground/50;
}

.rbc-date-cell.rbc-now {
  @apply font-semibold;
}

.rbc-date-cell.rbc-now > a {
  @apply text-primary;
}

/* Day cell styles */
.rbc-day-bg {
  @apply bg-background hover:bg-muted/30 transition-colors;
}

.rbc-day-bg.rbc-off-range-bg {
  @apply bg-muted/20;
}

.rbc-day-bg.rbc-today {
  @apply bg-primary/10;
}

.rbc-day-bg + .rbc-day-bg {
  @apply border-l border-border;
}

/* Event styles */
.rbc-event {
  @apply rounded-sm text-xs font-medium;
  padding: 2px 4px;
  margin: 1px 2px;
  border-radius: 4px;
}

.rbc-event:hover {
  @apply opacity-80 cursor-pointer;
}

.rbc-event.rbc-selected {
  @apply ring-2 ring-ring ring-offset-1;
}

.rbc-event-label {
  @apply text-xs font-medium;
}

.rbc-event-overlaps {
  @apply shadow-sm;
}

/* Week and day view styles */
.rbc-time-view {
  @apply border border-border rounded-lg overflow-hidden;
}

.rbc-time-header {
  @apply border-b border-border bg-muted/30;
}

.rbc-time-content {
  @apply bg-background;
}

.rbc-time-gutter {
  @apply bg-muted/20 border-r border-border;
}

.rbc-time-gutter .rbc-timeslot-group {
  @apply border-b border-border/50;
}

.rbc-time-slot {
  @apply border-b border-border/30;
}

.rbc-time-slot.rbc-now {
  @apply bg-primary/5;
}

.rbc-current-time-indicator {
  @apply bg-primary;
  height: 2px;
  z-index: 10;
}

/* Agenda view styles */
.rbc-agenda-view {
  @apply border border-border rounded-lg overflow-hidden;
}

.rbc-agenda-view table {
  @apply w-full;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  @apply bg-muted/30 p-3 text-sm font-medium border-b border-border;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  @apply p-3 text-sm text-muted-foreground border-b border-border;
}

.rbc-agenda-view .rbc-agenda-event-cell {
  @apply p-3 border-b border-border;
}

/* Popup styles */
.rbc-overlay {
  @apply bg-popover text-popover-foreground border border-border rounded-lg shadow-lg;
  z-index: 50;
}

.rbc-overlay-header {
  @apply p-3 border-b border-border font-medium;
}

/* Show more link */
.rbc-show-more {
  @apply text-primary hover:text-primary/80 text-xs font-medium cursor-pointer;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    @apply flex-col gap-2;
  }
  
  .rbc-toolbar button {
    @apply text-xs px-2 py-1;
  }
  
  .rbc-toolbar-label {
    @apply text-base;
  }
  
  .rbc-event {
    @apply text-xs;
    padding: 1px 2px;
  }
  
  .rbc-header {
    @apply text-xs;
    padding: 4px 2px;
  }
}

/* Custom status colors - these will be overridden by eventStyleGetter */
.rbc-event.status-scheduled {
  @apply bg-blue-500 text-white;
}

.rbc-event.status-confirmed {
  @apply bg-green-500 text-white;
}

.rbc-event.status-completed {
  @apply bg-gray-500 text-white;
}

.rbc-event.status-cancelled {
  @apply bg-red-500 text-white;
}

.rbc-event.status-no-show {
  @apply bg-amber-500 text-white;
}

/* Appointment type indicators */
.rbc-event.type-consultation {
  border-left: 4px solid theme('colors.purple.500');
}

.rbc-event.type-treatment {
  border-left: 4px solid theme('colors.cyan.500');
}

/* Loading state */
.calendar-loading {
  @apply animate-pulse;
}

/* Conflict warning styles */
.rbc-event.has-conflict {
  @apply ring-2 ring-red-500 ring-offset-1;
}

.rbc-event.has-conflict::after {
  content: '⚠️';
  @apply absolute -top-1 -right-1 text-xs;
}
