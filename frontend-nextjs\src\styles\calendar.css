/* Custom styles for react-big-calendar to match Shadcn/ui theme */

/* Calendar container */
.rbc-calendar {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: inherit;
}

/* Header styles */
.rbc-header {
  background-color: hsl(var(--muted) / 0.5);
  color: hsl(var(--muted-foreground));
  font-weight: 500;
  font-size: 0.875rem;
  border-bottom: 1px solid hsl(var(--border));
  padding: 8px 4px;
}

.rbc-header + .rbc-header {
  border-left: 1px solid hsl(var(--border));
}

/* Toolbar styles */
.rbc-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
  padding: 0.5rem;
  background-color: hsl(var(--muted) / 0.3);
  border-radius: 0.5rem;
}

.rbc-toolbar button {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: colors 0.2s;
  border: 1px solid transparent;
  background-color: transparent;
  color: hsl(var(--foreground));
}

.rbc-toolbar button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rbc-toolbar button:focus {
  outline: none;
  box-shadow: 0 0 0 2px hsl(var(--ring));
}

.rbc-toolbar button.rbc-active {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rbc-toolbar-label {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

/* Month view styles */
.rbc-month-view {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  text-align: right;
  padding: 0.25rem;
}

.rbc-date-cell > a {
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  transition: color 0.2s;
}

.rbc-date-cell > a:hover {
  color: hsl(var(--foreground));
}

.rbc-date-cell.rbc-off-range {
  color: hsl(var(--muted-foreground) / 0.5);
}

.rbc-date-cell.rbc-now {
  font-weight: 600;
}

.rbc-date-cell.rbc-now > a {
  color: hsl(var(--primary));
}

/* Day cell styles */
.rbc-day-bg {
  background-color: hsl(var(--background));
  transition: background-color 0.2s;
}

.rbc-day-bg:hover {
  background-color: hsl(var(--muted) / 0.3);
}

.rbc-day-bg.rbc-off-range-bg {
  background-color: hsl(var(--muted) / 0.2);
}

.rbc-day-bg.rbc-today {
  background-color: hsl(var(--primary) / 0.1);
}

.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid hsl(var(--border));
}

/* Event styles */
.rbc-event {
  border-radius: 0.125rem;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 2px 4px;
  margin: 1px 2px;
  border-radius: 4px;
  cursor: pointer;
}

.rbc-event:hover {
  opacity: 0.8;
}

.rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px hsl(var(--ring)), 0 0 0 4px transparent;
}

.rbc-event-label {
  font-size: 0.75rem;
  font-weight: 500;
}

.rbc-event-overlaps {
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

/* Week and day view styles */
.rbc-time-view {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.rbc-time-header {
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / 0.3);
}

.rbc-time-content {
  background-color: hsl(var(--background));
}

.rbc-time-gutter {
  background-color: hsl(var(--muted) / 0.2);
  border-right: 1px solid hsl(var(--border));
}

.rbc-time-gutter .rbc-timeslot-group {
  border-bottom: 1px solid hsl(var(--border) / 0.5);
}

.rbc-time-slot {
  border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.rbc-time-slot.rbc-now {
  background-color: hsl(var(--primary) / 0.05);
}

.rbc-current-time-indicator {
  background-color: hsl(var(--primary));
  height: 2px;
  z-index: 10;
}

/* Agenda view styles */
.rbc-agenda-view {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  overflow: hidden;
}

.rbc-agenda-view table {
  width: 100%;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  background-color: hsl(var(--muted) / 0.3);
  padding: 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-agenda-view .rbc-agenda-time-cell {
  padding: 0.75rem;
  font-size: 0.875rem;
  color: hsl(var(--muted-foreground));
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-agenda-view .rbc-agenda-event-cell {
  padding: 0.75rem;
  border-bottom: 1px solid hsl(var(--border));
}

/* Popup styles */
.rbc-overlay {
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  z-index: 50;
}

.rbc-overlay-header {
  padding: 0.75rem;
  border-bottom: 1px solid hsl(var(--border));
  font-weight: 500;
}

/* Show more link */
.rbc-show-more {
  color: hsl(var(--primary));
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s;
}

.rbc-show-more:hover {
  color: hsl(var(--primary) / 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: 0.5rem;
  }

  .rbc-toolbar button {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .rbc-toolbar-label {
    font-size: 1rem;
  }

  .rbc-event {
    font-size: 0.75rem;
    padding: 1px 2px;
  }

  .rbc-header {
    font-size: 0.75rem;
    padding: 4px 2px;
  }
}

/* Custom status colors - these will be overridden by eventStyleGetter */
.rbc-event.status-scheduled {
  background-color: #3b82f6;
  color: white;
}

.rbc-event.status-confirmed {
  background-color: #10b981;
  color: white;
}

.rbc-event.status-completed {
  background-color: #6b7280;
  color: white;
}

.rbc-event.status-cancelled {
  background-color: #ef4444;
  color: white;
}

.rbc-event.status-no-show {
  background-color: #f59e0b;
  color: white;
}

/* Appointment type indicators */
.rbc-event.type-consultation {
  border-left: 4px solid #8b5cf6;
}

.rbc-event.type-treatment {
  border-left: 4px solid #06b6d4;
}

/* Loading state */
.calendar-loading {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Conflict warning styles */
.rbc-event.has-conflict {
  box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px transparent;
}

.rbc-event.has-conflict::after {
  content: '⚠️';
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  font-size: 0.75rem;
}
