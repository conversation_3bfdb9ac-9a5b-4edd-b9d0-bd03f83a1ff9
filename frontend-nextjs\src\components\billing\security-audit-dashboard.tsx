'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  IconShield, 
  IconAlertTriangle, 
  IconEye, 
  IconRefresh,
  IconDownload,
  IconFilter,
  IconSearch,
  IconLock,
  IconUserCheck,
  IconClock,
  IconActivity
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { useRole, PermissionGate } from '@/lib/role-context';

interface AuditLogEntry {
  id: string;
  timestamp: string;
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  resourceId?: string;
  details: any;
  ipAddress?: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
}

interface SecurityMetrics {
  totalActions: number;
  failedActions: number;
  uniqueUsers: number;
  suspiciousActivities: number;
  topActions: Array<{ action: string; count: number }>;
  topUsers: Array<{ userEmail: string; count: number }>;
  recentFailures: AuditLogEntry[];
}

export function SecurityAuditDashboard() {
  const { hasPermission } = useRole();
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
    action: '',
    userId: '',
    success: 'all',
    search: '',
  });

  // Check permissions
  if (!hasPermission('canAccessAuditLogs')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconLock className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">访问受限</h3>
          <p className="text-muted-foreground">
            您没有权限查看安全审计日志
          </p>
        </div>
      </div>
    );
  }

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);
      
      // In a real implementation, this would call the audit logs API
      // For now, we'll simulate the data
      const mockLogs: AuditLogEntry[] = [
        {
          id: '1',
          timestamp: new Date().toISOString(),
          userId: 'user1',
          userEmail: '<EMAIL>',
          action: 'CREATE_BILL',
          resource: 'bills',
          resourceId: 'bill-123',
          details: { billNumber: 'BILL-20250109-123456', amount: 500 },
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0...',
          success: true,
        },
        {
          id: '2',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          userId: 'user2',
          userEmail: '<EMAIL>',
          action: 'PROCESS_PAYMENT',
          resource: 'payments',
          resourceId: 'payment-456',
          details: { amount: 200, method: 'cash' },
          ipAddress: '*************',
          success: true,
        },
        {
          id: '3',
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          userId: 'user3',
          userEmail: '<EMAIL>',
          action: 'VIEW_FINANCIAL_DATA',
          resource: 'bills',
          details: { attempted: 'sensitive_financial_report' },
          ipAddress: '*************',
          success: false,
          errorMessage: 'Insufficient permissions',
        },
      ];

      setAuditLogs(mockLogs);

      // Calculate metrics
      const totalActions = mockLogs.length;
      const failedActions = mockLogs.filter(log => !log.success).length;
      const uniqueUsers = new Set(mockLogs.map(log => log.userId)).size;
      const suspiciousActivities = mockLogs.filter(log => 
        !log.success || log.action.includes('DELETE') || log.action.includes('OVERRIDE')
      ).length;

      const actionCounts = mockLogs.reduce((acc, log) => {
        acc[log.action] = (acc[log.action] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const userCounts = mockLogs.reduce((acc, log) => {
        acc[log.userEmail] = (acc[log.userEmail] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      setMetrics({
        totalActions,
        failedActions,
        uniqueUsers,
        suspiciousActivities,
        topActions: Object.entries(actionCounts)
          .map(([action, count]) => ({ action, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5),
        topUsers: Object.entries(userCounts)
          .map(([userEmail, count]) => ({ userEmail, count }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 5),
        recentFailures: mockLogs.filter(log => !log.success).slice(0, 10),
      });

    } catch (error) {
      console.error('Error fetching audit logs:', error);
      toast.error('获取审计日志失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAuditLogs();
  }, [filters.startDate, filters.endDate]);

  const exportAuditLogs = () => {
    try {
      const exportData = {
        logs: auditLogs,
        metrics,
        filters,
        exportedAt: new Date().toISOString(),
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `audit-logs-${filters.startDate}-to-${filters.endDate}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('审计日志导出成功');
    } catch (error) {
      console.error('Error exporting audit logs:', error);
      toast.error('导出审计日志失败');
    }
  };

  const getActionBadgeVariant = (action: string) => {
    if (action.includes('DELETE') || action.includes('REFUND')) return 'destructive';
    if (action.includes('CREATE') || action.includes('PROCESS')) return 'default';
    if (action.includes('VIEW') || action.includes('READ')) return 'secondary';
    return 'outline';
  };

  const getSuccessBadgeVariant = (success: boolean) => {
    return success ? 'default' : 'destructive';
  };

  const filteredLogs = auditLogs.filter(log => {
    if (filters.action && !log.action.toLowerCase().includes(filters.action.toLowerCase())) return false;
    if (filters.userId && !log.userEmail.toLowerCase().includes(filters.userId.toLowerCase())) return false;
    if (filters.success !== 'all' && log.success !== (filters.success === 'true')) return false;
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      return (
        log.action.toLowerCase().includes(searchLower) ||
        log.userEmail.toLowerCase().includes(searchLower) ||
        log.resource.toLowerCase().includes(searchLower) ||
        (log.resourceId && log.resourceId.toLowerCase().includes(searchLower))
      );
    }
    return true;
  });

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">安全审计</h2>
          <Button disabled>
            <IconRefresh className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">加载中...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">--</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight flex items-center gap-2">
            <IconShield className="h-6 w-6" />
            安全审计仪表板
          </h2>
          <p className="text-muted-foreground">
            监控系统安全活动和用户操作
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={fetchAuditLogs} size="sm">
            <IconRefresh className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button onClick={exportAuditLogs} size="sm" variant="outline">
            <IconDownload className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      {/* Security Metrics */}
      {metrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总操作数</CardTitle>
              <IconActivity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.totalActions}</div>
              <p className="text-xs text-muted-foreground">
                过去7天
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">失败操作</CardTitle>
              <IconAlertTriangle className="h-4 w-4 text-red-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{metrics.failedActions}</div>
              <p className="text-xs text-muted-foreground">
                失败率: {((metrics.failedActions / metrics.totalActions) * 100).toFixed(1)}%
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃用户</CardTitle>
              <IconUserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{metrics.uniqueUsers}</div>
              <p className="text-xs text-muted-foreground">
                不同用户数
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">可疑活动</CardTitle>
              <IconEye className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{metrics.suspiciousActivities}</div>
              <p className="text-xs text-muted-foreground">
                需要关注
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IconFilter className="h-5 w-5" />
            筛选条件
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div>
              <Label htmlFor="start-date">开始日期</Label>
              <Input
                id="start-date"
                type="date"
                value={filters.startDate}
                onChange={(e) => setFilters(prev => ({ ...prev, startDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="end-date">结束日期</Label>
              <Input
                id="end-date"
                type="date"
                value={filters.endDate}
                onChange={(e) => setFilters(prev => ({ ...prev, endDate: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="action">操作类型</Label>
              <Input
                id="action"
                placeholder="操作类型..."
                value={filters.action}
                onChange={(e) => setFilters(prev => ({ ...prev, action: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="user">用户</Label>
              <Input
                id="user"
                placeholder="用户邮箱..."
                value={filters.userId}
                onChange={(e) => setFilters(prev => ({ ...prev, userId: e.target.value }))}
              />
            </div>
            <div>
              <Label htmlFor="success">状态</Label>
              <Select value={filters.success} onValueChange={(value) => setFilters(prev => ({ ...prev, success: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部</SelectItem>
                  <SelectItem value="true">成功</SelectItem>
                  <SelectItem value="false">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="search">搜索</Label>
              <Input
                id="search"
                placeholder="搜索..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle>审计日志</CardTitle>
          <CardDescription>
            显示 {filteredLogs.length} 条记录，共 {auditLogs.length} 条
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>时间</TableHead>
                  <TableHead>用户</TableHead>
                  <TableHead>操作</TableHead>
                  <TableHead>资源</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>IP地址</TableHead>
                  <TableHead>详情</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <IconClock className="h-4 w-4 text-muted-foreground" />
                        {new Date(log.timestamp).toLocaleString('zh-CN')}
                      </div>
                    </TableCell>
                    <TableCell>{log.userEmail}</TableCell>
                    <TableCell>
                      <Badge variant={getActionBadgeVariant(log.action)}>
                        {log.action}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {log.resource}
                      {log.resourceId && (
                        <div className="text-xs text-muted-foreground">
                          {log.resourceId}
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge variant={getSuccessBadgeVariant(log.success)}>
                        {log.success ? '成功' : '失败'}
                      </Badge>
                      {log.errorMessage && (
                        <div className="text-xs text-red-600 mt-1">
                          {log.errorMessage}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-xs">{log.ipAddress}</TableCell>
                    <TableCell>
                      <details className="text-xs">
                        <summary className="cursor-pointer">查看详情</summary>
                        <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </details>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
