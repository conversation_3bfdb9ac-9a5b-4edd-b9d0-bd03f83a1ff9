'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { paymentFormSchema, PaymentFormData } from '@/lib/validation/billing-schemas';
import { FormValidator, validateBusinessRules } from '@/lib/validation/validation-utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  IconCreditCard, 
  IconCash, 
  IconDeviceMobile, 
  IconBrandAlipay,
  IconBuildingBank,
  IconCalendar,
  IconReceipt,
  IconX
} from '@tabler/icons-react';
import { Bill, Payment } from '@/types/clinic';
import { paymentsAPI, BillingAPIError, billingUtils } from '@/lib/api/billing';
import { toast } from 'sonner';
import { ReceiptDialog } from './receipt-dialog';
import { billingNotifications } from '@/lib/billing-notifications';

// Remove the local schema since we're now using the centralized one

interface PaymentFormProps {
  bill: Bill;
  onSuccess?: (payment: Payment) => void;
  onCancel?: () => void;
  isOpen?: boolean;
}

// Payment method options with icons and labels
const paymentMethods = [
  {
    value: 'cash',
    label: '现金',
    icon: IconCash,
    description: '现金支付',
    requiresTransactionId: false,
  },
  {
    value: 'card',
    label: '银行卡',
    icon: IconCreditCard,
    description: '银行卡刷卡支付',
    requiresTransactionId: true,
  },
  {
    value: 'wechat',
    label: '微信支付',
    icon: IconDeviceMobile,
    description: '微信扫码支付',
    requiresTransactionId: true,
  },
  {
    value: 'alipay',
    label: '支付宝',
    icon: IconBrandAlipay,
    description: '支付宝扫码支付',
    requiresTransactionId: true,
  },
  {
    value: 'transfer',
    label: '银行转账',
    icon: IconBuildingBank,
    description: '银行转账支付',
    requiresTransactionId: true,
  },
  {
    value: 'installment',
    label: '分期付款',
    icon: IconCalendar,
    description: '分期付款',
    requiresTransactionId: false,
  },
];

export function PaymentForm({ bill, onSuccess, onCancel, isOpen = true }: PaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [completedPayment, setCompletedPayment] = useState<Payment | null>(null);
  const [showReceipt, setShowReceipt] = useState(false);

  const form = useForm<PaymentFormData>({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      amount: bill.remainingAmount || 0,
      paymentMethod: 'cash',
      transactionId: '',
      notes: '',
    },
  });

  const selectedPaymentMethod = paymentMethods.find(method => method.value === selectedMethod);
  const remainingAmount = bill.remainingAmount || 0;
  const maxPaymentAmount = remainingAmount;

  const onSubmit = async (data: PaymentFormData) => {
    try {
      setIsSubmitting(true);

      // Business rule validation
      const amountValidation = validateBusinessRules.isValidPaymentAmount(data.amount, bill);
      if (!amountValidation.valid) {
        billingNotifications.payment.validationError(amountValidation.reason || '支付金额无效');
        return;
      }

      // Process payment
      const payment = await paymentsAPI.processPayment({
        bill: bill.id,
        patient: typeof bill.patient === 'object' ? bill.patient.id : bill.patientId,
        amount: data.amount,
        paymentMethod: data.paymentMethod,
        transactionId: data.transactionId || undefined,
        notes: data.notes || undefined,
      });

      billingNotifications.payment.processed(payment);

      // Store payment for receipt display
      setCompletedPayment(payment);
      setShowReceipt(true);

      if (onSuccess) {
        onSuccess(payment);
      }

      // Reset form
      form.reset();
      
    } catch (error) {
      console.error('Payment processing failed:', error);
      const errorMessage = error instanceof BillingAPIError
        ? error.message
        : undefined;
      billingNotifications.payment.processError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) {
    return null;
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <IconReceipt className="h-5 w-5" />
              处理支付
            </CardTitle>
            <CardDescription>
              为账单 {bill.billNumber} 处理支付
            </CardDescription>
          </div>
          {onCancel && (
            <Button variant="ghost" size="sm" onClick={onCancel}>
              <IconX className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Bill Summary */}
        <div className="bg-muted/50 rounded-lg p-4 space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">患者:</span>
            <span className="text-sm">
              {typeof bill.patient === 'object' ? bill.patient.fullName : '未知患者'}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">账单总额:</span>
            <span className="text-sm font-semibold">
              {billingUtils.formatCurrency(bill.totalAmount)}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">已支付:</span>
            <span className="text-sm text-green-600">
              {billingUtils.formatCurrency(bill.paidAmount || 0)}
            </span>
          </div>
          <Separator />
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium">待支付:</span>
            <span className="text-lg font-bold text-red-600">
              {billingUtils.formatCurrency(remainingAmount)}
            </span>
          </div>
        </div>

        {/* Payment Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Payment Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>支付金额</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                        $
                      </span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0.01"
                        max={maxPaymentAmount}
                        placeholder="0.00"
                        className="pl-8"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    最大支付金额: {billingUtils.formatCurrency(maxPaymentAmount)}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Payment Method */}
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>支付方式</FormLabel>
                  <FormControl>
                    <Select 
                      value={field.value} 
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedMethod(value);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="选择支付方式" />
                      </SelectTrigger>
                      <SelectContent>
                        {paymentMethods.map((method) => {
                          const Icon = method.icon;
                          return (
                            <SelectItem key={method.value} value={method.value}>
                              <div className="flex items-center gap-2">
                                <Icon className="h-4 w-4" />
                                <div>
                                  <div className="font-medium">{method.label}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {method.description}
                                  </div>
                                </div>
                              </div>
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Transaction ID (conditional) */}
            {selectedPaymentMethod?.requiresTransactionId && (
              <FormField
                control={form.control}
                name="transactionId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>交易ID</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="输入第三方支付平台的交易ID"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      请输入{selectedPaymentMethod.label}的交易ID或流水号
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>备注 (可选)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="支付相关备注信息..."
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1"
              >
                {isSubmitting ? '处理中...' : '确认支付'}
              </Button>
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  取消
                </Button>
              )}
            </div>
          </form>
        </Form>
      </CardContent>

      {/* Receipt Dialog */}
      <ReceiptDialog
        payment={completedPayment}
        bill={bill}
        isOpen={showReceipt}
        onClose={() => {
          setShowReceipt(false);
          setCompletedPayment(null);
        }}
      />
    </Card>
  );
}
