'use client';

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { 
  IconReceipt, 
  IconPrinter, 
  IconDownload,
  IconEye,
  IconRefresh,
  IconSearch,
  IconFileText,
  IconCash,
  IconCreditCard,
  IconPigMoney,
  IconCalendar,
  IconUser,
  IconBuilding
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { useRole, PermissionGate } from '@/lib/role-context';
import { Bill, Payment, Deposit } from '@/types/clinic';

interface ReceiptData {
  id: string;
  receiptNumber: string;
  type: 'payment' | 'deposit' | 'refund';
  date: string;
  patient: {
    fullName: string;
    phone: string;
    email?: string;
  };
  clinic: {
    name: string;
    address: string;
    phone: string;
    email: string;
    license: string;
  };
  bill?: {
    billNumber: string;
    description: string;
    totalAmount: number;
    remainingAmount: number;
  };
  payment?: {
    amount: number;
    method: string;
    transactionId?: string;
    notes?: string;
  };
  deposit?: {
    depositNumber: string;
    amount: number;
    purpose: string;
    expiryDate?: string;
  };
  refund?: {
    originalReceiptNumber: string;
    amount: number;
    reason: string;
    method: string;
  };
  items?: Array<{
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
  }>;
  totals: {
    subtotal: number;
    discount: number;
    tax: number;
    total: number;
  };
  staff: {
    name: string;
    role: string;
  };
}

interface AdvancedReceiptSystemProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  paymentId?: string;
  depositId?: string;
  receiptType?: 'payment' | 'deposit' | 'refund';
}

export function AdvancedReceiptSystem({ 
  open, 
  onOpenChange, 
  paymentId, 
  depositId, 
  receiptType = 'payment' 
}: AdvancedReceiptSystemProps) {
  const { hasPermission } = useRole();
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [receipts, setReceipts] = useState<ReceiptData[]>([]);
  const [selectedReceipt, setSelectedReceipt] = useState<ReceiptData | null>(null);
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const printRef = useRef<HTMLDivElement>(null);

  // Check permissions
  if (!hasPermission('canGenerateReceipts')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconFileText className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">权限不足</h3>
          <p className="text-muted-foreground">
            您没有权限生成收据
          </p>
        </div>
      </div>
    );
  }

  const fetchReceiptData = async (id: string, type: 'payment' | 'deposit') => {
    try {
      setLoading(true);
      
      // Simulate receipt data based on type
      const mockReceiptData: ReceiptData = {
        id: id,
        receiptNumber: `REC-${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
        type: type,
        date: new Date().toISOString(),
        patient: {
          fullName: '张三',
          phone: '13800138000',
          email: '<EMAIL>',
        },
        clinic: {
          name: '北海医美诊所',
          address: '北京市朝阳区建国路88号',
          phone: '010-12345678',
          email: '<EMAIL>',
          license: '医疗机构执业许可证：京朝卫医证字第123456号',
        },
        bill: type === 'payment' ? {
          billNumber: 'BILL-20250109-123456',
          description: '面部护理治疗',
          totalAmount: 1500,
          remainingAmount: 0,
        } : undefined,
        payment: type === 'payment' ? {
          amount: 1500,
          method: 'cash',
          transactionId: 'TXN-123456789',
          notes: '现金支付',
        } : undefined,
        deposit: type === 'deposit' ? {
          depositNumber: 'DEP-20250109-123456',
          amount: 2000,
          purpose: '治疗预付款',
          expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
        } : undefined,
        items: [
          {
            description: '面部深层清洁',
            quantity: 1,
            unitPrice: 800,
            total: 800,
          },
          {
            description: '补水面膜',
            quantity: 1,
            unitPrice: 500,
            total: 500,
          },
          {
            description: '护理费',
            quantity: 1,
            unitPrice: 200,
            total: 200,
          },
        ],
        totals: {
          subtotal: 1500,
          discount: 0,
          tax: 0,
          total: 1500,
        },
        staff: {
          name: '李医生',
          role: '主治医师',
        },
      };

      setReceiptData(mockReceiptData);
    } catch (error) {
      console.error('Error fetching receipt data:', error);
      toast.error('获取收据数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchReceipts = async () => {
    try {
      setLoading(true);
      
      // Simulate receipts list
      const mockReceipts: ReceiptData[] = [
        {
          id: '1',
          receiptNumber: 'REC-20250109-001',
          type: 'payment',
          date: new Date().toISOString(),
          patient: { fullName: '张三', phone: '13800138000' },
          clinic: { name: '北海医美诊所', address: '', phone: '', email: '', license: '' },
          totals: { subtotal: 1500, discount: 0, tax: 0, total: 1500 },
          staff: { name: '李医生', role: '主治医师' },
        },
        {
          id: '2',
          receiptNumber: 'REC-20250109-002',
          type: 'deposit',
          date: new Date(Date.now() - 3600000).toISOString(),
          patient: { fullName: '李四', phone: '13800138001' },
          clinic: { name: '北海医美诊所', address: '', phone: '', email: '', license: '' },
          totals: { subtotal: 2000, discount: 0, tax: 0, total: 2000 },
          staff: { name: '王护士', role: '前台' },
        },
      ];

      setReceipts(mockReceipts);
    } catch (error) {
      console.error('Error fetching receipts:', error);
      toast.error('获取收据列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (open) {
      if (paymentId) {
        fetchReceiptData(paymentId, 'payment');
      } else if (depositId) {
        fetchReceiptData(depositId, 'deposit');
      } else {
        fetchReceipts();
      }
    }
  }, [open, paymentId, depositId]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const handlePrint = () => {
    if (printRef.current) {
      const printContent = printRef.current.innerHTML;
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>收据打印</title>
              <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .receipt { max-width: 400px; margin: 0 auto; }
                .header { text-align: center; margin-bottom: 20px; }
                .clinic-name { font-size: 18px; font-weight: bold; }
                .clinic-info { font-size: 12px; color: #666; }
                .receipt-title { font-size: 16px; font-weight: bold; margin: 20px 0; text-align: center; }
                .info-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .items-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
                .items-table th, .items-table td { border-bottom: 1px solid #ddd; padding: 5px; text-align: left; }
                .total-row { font-weight: bold; }
                .footer { margin-top: 20px; text-align: center; font-size: 12px; }
                @media print { body { margin: 0; } }
              </style>
            </head>
            <body>
              ${printContent}
            </body>
          </html>
        `);
        printWindow.document.close();
        printWindow.print();
        printWindow.close();
      }
    }
  };

  const handleDownloadPDF = async () => {
    try {
      // In a real implementation, this would generate a PDF
      toast.success('PDF下载功能开发中');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      toast.error('PDF下载失败');
    }
  };

  const getReceiptTypeIcon = (type: string) => {
    switch (type) {
      case 'payment': return IconCash;
      case 'deposit': return IconPigMoney;
      case 'refund': return IconCreditCard;
      default: return IconReceipt;
    }
  };

  const getReceiptTypeName = (type: string) => {
    switch (type) {
      case 'payment': return '支付收据';
      case 'deposit': return '押金收据';
      case 'refund': return '退款收据';
      default: return '收据';
    }
  };

  const filteredReceipts = receipts.filter(receipt =>
    receipt.receiptNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    receipt.patient.fullName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconReceipt className="h-5 w-5" />
            高级收据系统
          </DialogTitle>
          <DialogDescription>
            生成、查看和管理各种类型的收据
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Receipt Data Display or Receipt List */}
          {receiptData ? (
            <div className="space-y-4">
              {/* Receipt Preview */}
              <div ref={printRef} className="receipt bg-white p-6 border rounded-lg">
                <div className="header text-center mb-6">
                  <div className="clinic-name text-xl font-bold">{receiptData.clinic.name}</div>
                  <div className="clinic-info text-sm text-muted-foreground mt-2">
                    <div>{receiptData.clinic.address}</div>
                    <div>电话: {receiptData.clinic.phone} | 邮箱: {receiptData.clinic.email}</div>
                    <div className="text-xs mt-1">{receiptData.clinic.license}</div>
                  </div>
                </div>

                <div className="receipt-title text-lg font-bold text-center mb-4">
                  {getReceiptTypeName(receiptData.type)}
                </div>

                <div className="receipt-info space-y-2 mb-6">
                  <div className="flex justify-between">
                    <span>收据编号:</span>
                    <span className="font-mono">{receiptData.receiptNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>日期:</span>
                    <span>{new Date(receiptData.date).toLocaleString('zh-CN')}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>患者姓名:</span>
                    <span>{receiptData.patient.fullName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>联系电话:</span>
                    <span>{receiptData.patient.phone}</span>
                  </div>
                  {receiptData.bill && (
                    <div className="flex justify-between">
                      <span>账单编号:</span>
                      <span className="font-mono">{receiptData.bill.billNumber}</span>
                    </div>
                  )}
                </div>

                <Separator className="my-4" />

                {/* Items */}
                {receiptData.items && receiptData.items.length > 0 && (
                  <div className="items mb-6">
                    <table className="items-table w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left">项目</th>
                          <th className="text-center">数量</th>
                          <th className="text-right">单价</th>
                          <th className="text-right">小计</th>
                        </tr>
                      </thead>
                      <tbody>
                        {receiptData.items.map((item, index) => (
                          <tr key={index}>
                            <td>{item.description}</td>
                            <td className="text-center">{item.quantity}</td>
                            <td className="text-right">{formatCurrency(item.unitPrice)}</td>
                            <td className="text-right">{formatCurrency(item.total)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}

                {/* Payment/Deposit/Refund Details */}
                {receiptData.payment && (
                  <div className="payment-details mb-6">
                    <h4 className="font-medium mb-2">支付详情</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>支付方式:</span>
                        <span>{receiptData.payment.method}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>支付金额:</span>
                        <span className="font-bold">{formatCurrency(receiptData.payment.amount)}</span>
                      </div>
                      {receiptData.payment.transactionId && (
                        <div className="flex justify-between">
                          <span>交易编号:</span>
                          <span className="font-mono text-xs">{receiptData.payment.transactionId}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {receiptData.deposit && (
                  <div className="deposit-details mb-6">
                    <h4 className="font-medium mb-2">押金详情</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>押金编号:</span>
                        <span className="font-mono">{receiptData.deposit.depositNumber}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>押金金额:</span>
                        <span className="font-bold">{formatCurrency(receiptData.deposit.amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>用途:</span>
                        <span>{receiptData.deposit.purpose}</span>
                      </div>
                      {receiptData.deposit.expiryDate && (
                        <div className="flex justify-between">
                          <span>有效期至:</span>
                          <span>{new Date(receiptData.deposit.expiryDate).toLocaleDateString('zh-CN')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Totals */}
                <div className="totals border-t pt-4 mb-6">
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>小计:</span>
                      <span>{formatCurrency(receiptData.totals.subtotal)}</span>
                    </div>
                    {receiptData.totals.discount > 0 && (
                      <div className="flex justify-between">
                        <span>折扣:</span>
                        <span>-{formatCurrency(receiptData.totals.discount)}</span>
                      </div>
                    )}
                    {receiptData.totals.tax > 0 && (
                      <div className="flex justify-between">
                        <span>税费:</span>
                        <span>{formatCurrency(receiptData.totals.tax)}</span>
                      </div>
                    )}
                    <div className="flex justify-between total-row text-lg font-bold border-t pt-2">
                      <span>总计:</span>
                      <span>{formatCurrency(receiptData.totals.total)}</span>
                    </div>
                  </div>
                </div>

                {/* Footer */}
                <div className="footer text-center text-sm text-muted-foreground">
                  <div>服务人员: {receiptData.staff.name} ({receiptData.staff.role})</div>
                  <div className="mt-2">感谢您的信任，祝您健康美丽！</div>
                  <div className="mt-1 text-xs">如有疑问，请联系我们: {receiptData.clinic.phone}</div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-center gap-2">
                <Button onClick={handlePrint} className="flex items-center gap-2">
                  <IconPrinter className="h-4 w-4" />
                  打印收据
                </Button>
                <Button onClick={handleDownloadPDF} variant="outline" className="flex items-center gap-2">
                  <IconDownload className="h-4 w-4" />
                  下载PDF
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Search and Filter */}
              <div className="flex items-center gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="搜索收据编号或患者姓名..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button onClick={fetchReceipts} size="sm">
                  <IconRefresh className="h-4 w-4 mr-2" />
                  刷新
                </Button>
              </div>

              {/* Receipts List */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {filteredReceipts.map((receipt) => {
                  const TypeIcon = getReceiptTypeIcon(receipt.type);
                  return (
                    <Card key={receipt.id} className="cursor-pointer hover:bg-muted/50" onClick={() => setSelectedReceipt(receipt)}>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <TypeIcon className="h-5 w-5 text-muted-foreground" />
                            <div>
                              <div className="font-medium">{receipt.receiptNumber}</div>
                              <div className="text-sm text-muted-foreground">
                                {receipt.patient.fullName} • {new Date(receipt.date).toLocaleDateString('zh-CN')}
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-bold">{formatCurrency(receipt.totals.total)}</div>
                            <Badge variant="outline">{getReceiptTypeName(receipt.type)}</Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            关闭
          </Button>
          {selectedReceipt && (
            <Button onClick={() => setReceiptData(selectedReceipt)}>
              <IconEye className="mr-2 h-4 w-4" />
              查看收据
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
