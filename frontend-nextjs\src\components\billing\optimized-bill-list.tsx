'use client';

// Optimized billing list component with virtual scrolling, caching, and performance monitoring
// Handles large datasets efficiently with minimal memory footprint

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  IconSearch, 
  IconRefresh, 
  IconEye,
  IconClock,
  IconTrendingUp,
  IconActivity
} from '@tabler/icons-react';
import { 
  billingCache, 
  performanceMonitor, 
  debouncedSearch,
  paginateArray,
  type PaginatedResult
} from '@/lib/billing-performance';
import { billingAPI } from '@/lib/api/billing';
import type { Bill } from '@/types/clinic';

interface OptimizedBillListProps {
  className?: string;
  pageSize?: number;
  enableVirtualScrolling?: boolean;
  showPerformanceStats?: boolean;
}

export function OptimizedBillList({
  className = '',
  pageSize = 20,
  enableVirtualScrolling = true,
  showPerformanceStats = false
}: OptimizedBillListProps) {
  const [bills, setBills] = useState<Bill[]>([]);
  const [filteredBills, setFilteredBills] = useState<Bill[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [performanceStats, setPerformanceStats] = useState<any>(null);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerHeight, setContainerHeight] = useState(600);
  const itemHeight = 120; // Estimated height of each bill item

  // Memoized pagination result
  const paginatedResult = useMemo((): PaginatedResult<Bill> => {
    return paginateArray(filteredBills, {
      page: currentPage,
      pageSize,
      total: filteredBills.length
    });
  }, [filteredBills, currentPage, pageSize]);

  // Virtual scrolling implementation
  const [scrollTop, setScrollTop] = useState(0);
  const visibleRange = useMemo(() => {
    if (!enableVirtualScrolling) {
      return { start: 0, end: paginatedResult.data.length };
    }

    const start = Math.floor(scrollTop / itemHeight);
    const end = Math.min(
      start + Math.ceil(containerHeight / itemHeight) + 2,
      paginatedResult.data.length
    );

    return { start, end };
  }, [scrollTop, containerHeight, itemHeight, paginatedResult.data.length, enableVirtualScrolling]);

  const visibleBills = useMemo(() => {
    return paginatedResult.data.slice(visibleRange.start, visibleRange.end);
  }, [paginatedResult.data, visibleRange]);

  // Load bills with caching and performance monitoring
  const loadBills = useCallback(async (useCache: boolean = true) => {
    const cacheKey = `bills_${searchQuery}_${currentPage}_${pageSize}`;
    
    // Check cache first
    if (useCache) {
      const cachedData = billingCache.get<Bill[]>(cacheKey);
      if (cachedData) {
        setBills(cachedData);
        setFilteredBills(cachedData);
        setIsLoading(false);
        return;
      }
    }

    const endTiming = performanceMonitor.startTiming('load_bills');
    setIsLoading(true);
    setError(null);

    try {
      const response = await billingAPI.fetchBills({
        page: currentPage,
        limit: pageSize,
        search: searchQuery,
      });

      const billsData = response.docs || [];
      setBills(billsData);
      setFilteredBills(billsData);

      // Cache the result
      billingCache.set(cacheKey, billsData, 5 * 60 * 1000); // 5 minutes TTL

      // Invalidate related cache entries when new data is loaded
      if (!useCache) {
        billingCache.invalidatePattern(`bills_${searchQuery}_.*`);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bills');
      console.error('Error loading bills:', err);
    } finally {
      setIsLoading(false);
      endTiming();
    }
  }, [searchQuery, currentPage, pageSize]);

  // Debounced search handler
  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
    setCurrentPage(1); // Reset to first page on search
  }, []);

  const debouncedSearchHandler = useCallback(
    (query: string) => debouncedSearch(query, handleSearch),
    [handleSearch]
  );

  // Handle scroll for virtual scrolling
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    if (enableVirtualScrolling) {
      setScrollTop(e.currentTarget.scrollTop);
    }
  }, [enableVirtualScrolling]);

  // Update container height on resize
  useEffect(() => {
    const updateHeight = () => {
      if (containerRef.current) {
        setContainerHeight(containerRef.current.clientHeight);
      }
    };

    updateHeight();
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, []);

  // Load bills on component mount and when dependencies change
  useEffect(() => {
    loadBills();
  }, [loadBills]);

  // Update performance stats periodically
  useEffect(() => {
    if (showPerformanceStats) {
      const interval = setInterval(() => {
        setPerformanceStats({
          cache: billingCache.getStats(),
          performance: performanceMonitor.getStats(),
          memory: performanceMonitor.getMemoryStats(),
        });
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [showPerformanceStats]);

  // Get bill status badge color
  const getBillStatusColor = (status: string): string => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Render bill item
  const renderBillItem = (bill: Bill, index: number) => (
    <div
      key={bill.id}
      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
      style={enableVirtualScrolling ? {
        position: 'absolute',
        top: (visibleRange.start + index) * itemHeight,
        left: 0,
        right: 0,
        height: itemHeight - 8, // Account for margin
      } : undefined}
    >
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-3">
          <Badge className={getBillStatusColor(bill.status)}>
            {bill.status === 'paid' ? '已支付' : 
             bill.status === 'pending' ? '待支付' :
             bill.status === 'overdue' ? '逾期' : '已取消'}
          </Badge>
          <span className="font-medium">账单 #{bill.billNumber}</span>
        </div>
        <span className="text-lg font-bold text-green-600">
          {formatCurrency(bill.totalAmount)}
        </span>
      </div>

      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
        <div>
          <span className="font-medium">患者:</span> {bill.patient?.fullName || 'N/A'}
        </div>
        <div>
          <span className="font-medium">类型:</span> {bill.billType}
        </div>
        <div>
          <span className="font-medium">创建日期:</span> {new Date(bill.createdAt).toLocaleDateString()}
        </div>
        <div>
          <span className="font-medium">到期日期:</span> {bill.dueDate ? new Date(bill.dueDate).toLocaleDateString() : 'N/A'}
        </div>
      </div>

      <div className="flex items-center justify-between mt-3">
        <div className="text-sm text-gray-500">
          {bill.items?.length || 0} 个项目
        </div>
        <Button variant="outline" size="sm">
          <IconEye className="h-4 w-4 mr-1" />
          查看详情
        </Button>
      </div>
    </div>
  );

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with search and controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="搜索账单..."
              className="pl-10 w-64"
              onChange={(e) => debouncedSearchHandler(e.target.value)}
            />
          </div>
          
          <Button
            variant="outline"
            onClick={() => loadBills(false)}
            disabled={isLoading}
          >
            <IconRefresh className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            刷新
          </Button>
        </div>

        {/* Performance stats */}
        {showPerformanceStats && performanceStats && (
          <div className="flex items-center space-x-4 text-sm text-gray-500">
            <div className="flex items-center space-x-1">
              <IconActivity className="h-4 w-4" />
              <span>缓存命中率: {(performanceStats.cache.hitRate * 100).toFixed(1)}%</span>
            </div>
            <div className="flex items-center space-x-1">
              <IconTrendingUp className="h-4 w-4" />
              <span>平均加载时间: {performanceStats.performance.load_bills?.average?.toFixed(0) || 0}ms</span>
            </div>
            {performanceStats.memory && (
              <div className="flex items-center space-x-1">
                <IconClock className="h-4 w-4" />
                <span>内存使用: {(performanceStats.memory.current / 1024 / 1024).toFixed(1)}MB</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bills list */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>账单列表</span>
            <Badge variant="outline">
              {paginatedResult.pagination.total} 个账单
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8 text-red-600">
              错误: {error}
            </div>
          ) : isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2">加载中...</span>
            </div>
          ) : visibleBills.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchQuery ? '未找到匹配的账单' : '暂无账单'}
            </div>
          ) : (
            <div
              ref={containerRef}
              className="space-y-4"
              style={enableVirtualScrolling ? {
                height: containerHeight,
                overflowY: 'auto',
                position: 'relative',
              } : undefined}
              onScroll={handleScroll}
            >
              {enableVirtualScrolling && (
                <div style={{ height: paginatedResult.data.length * itemHeight }} />
              )}
              
              {visibleBills.map((bill, index) => renderBillItem(bill, index))}
            </div>
          )}

          {/* Pagination */}
          {!enableVirtualScrolling && paginatedResult.pagination.totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <Button
                variant="outline"
                disabled={!paginatedResult.pagination.hasPrev}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                上一页
              </Button>
              
              <span className="text-sm text-gray-600">
                第 {paginatedResult.pagination.page} 页，共 {paginatedResult.pagination.totalPages} 页
              </span>
              
              <Button
                variant="outline"
                disabled={!paginatedResult.pagination.hasNext}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
