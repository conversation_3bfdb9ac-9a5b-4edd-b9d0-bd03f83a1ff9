// Validation utilities for real-time form validation and error handling
// Provides consistent validation feedback across all billing forms

import { z } from 'zod';
import { billingNotifications } from '@/lib/billing-notifications';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error' | 'warning';
}

export interface ValidationWarning {
  field: string;
  message: string;
  suggestion?: string;
}

// Real-time validation debounce utility
export class ValidationDebouncer {
  private timeouts: Map<string, NodeJS.Timeout> = new Map();
  private readonly delay: number;

  constructor(delay: number = 500) {
    this.delay = delay;
  }

  debounce<T extends any[]>(
    key: string,
    callback: (...args: T) => void,
    ...args: T
  ): void {
    // Clear existing timeout for this key
    const existingTimeout = this.timeouts.get(key);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      callback(...args);
      this.timeouts.delete(key);
    }, this.delay);

    this.timeouts.set(key, timeout);
  }

  clear(key?: string): void {
    if (key) {
      const timeout = this.timeouts.get(key);
      if (timeout) {
        clearTimeout(timeout);
        this.timeouts.delete(key);
      }
    } else {
      // Clear all timeouts
      this.timeouts.forEach(timeout => clearTimeout(timeout));
      this.timeouts.clear();
    }
  }
}

// Field-level validation utility
export class FieldValidator {
  private schema: z.ZodSchema;
  private debouncer: ValidationDebouncer;

  constructor(schema: z.ZodSchema, debounceDelay: number = 300) {
    this.schema = schema;
    this.debouncer = new ValidationDebouncer(debounceDelay);
  }

  validateField(
    fieldPath: string,
    value: any,
    fullData: any,
    onValidation?: (result: ValidationResult) => void
  ): void {
    this.debouncer.debounce(
      fieldPath,
      this.performFieldValidation.bind(this),
      fieldPath,
      value,
      fullData,
      onValidation
    );
  }

  private performFieldValidation(
    fieldPath: string,
    value: any,
    fullData: any,
    onValidation?: (result: ValidationResult) => void
  ): void {
    try {
      // Create a partial object with just this field
      const fieldData = this.setNestedValue({}, fieldPath, value);
      
      // Merge with existing data
      const testData = { ...fullData, ...fieldData };
      
      // Validate the full object but focus on this field
      const result = this.schema.safeParse(testData);
      
      const fieldErrors = result.success 
        ? []
        : result.error.errors
            .filter(error => error.path.join('.') === fieldPath)
            .map(error => ({
              field: fieldPath,
              message: error.message,
              code: error.code,
              severity: 'error' as const,
            }));

      const warnings = this.generateWarnings(fieldPath, value, fullData);

      const validationResult: ValidationResult = {
        isValid: fieldErrors.length === 0,
        errors: fieldErrors,
        warnings,
      };

      if (onValidation) {
        onValidation(validationResult);
      }
    } catch (error) {
      console.error('Field validation error:', error);
      if (onValidation) {
        onValidation({
          isValid: false,
          errors: [{
            field: fieldPath,
            message: '验证过程中发生错误',
            code: 'VALIDATION_ERROR',
            severity: 'error',
          }],
          warnings: [],
        });
      }
    }
  }

  private setNestedValue(obj: any, path: string, value: any): any {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      const key = keys[i];
      if (!(key in current)) {
        current[key] = {};
      }
      current = current[key];
    }
    
    current[keys[keys.length - 1]] = value;
    return obj;
  }

  private generateWarnings(fieldPath: string, value: any, fullData: any): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    // Generate context-specific warnings
    switch (fieldPath) {
      case 'amount':
        if (typeof value === 'number' && value > 10000) {
          warnings.push({
            field: fieldPath,
            message: '金额较大，请确认是否正确',
            suggestion: '检查金额是否输入正确',
          });
        }
        break;

      case 'dueDate':
        if (value) {
          const dueDate = new Date(value);
          const today = new Date();
          const daysDiff = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
          
          if (daysDiff > 365) {
            warnings.push({
              field: fieldPath,
              message: '到期日期距离现在超过一年',
              suggestion: '考虑设置更近的到期日期',
            });
          } else if (daysDiff < 7) {
            warnings.push({
              field: fieldPath,
              message: '到期日期较近',
              suggestion: '确保有足够时间处理账单',
            });
          }
        }
        break;

      case 'discountAmount':
        if (typeof value === 'number' && value > 0 && fullData.items) {
          const subtotal = fullData.items.reduce((sum: number, item: any) => {
            return sum + (item.quantity || 0) * (item.unitPrice || 0);
          }, 0);
          
          if (value > subtotal * 0.5) {
            warnings.push({
              field: fieldPath,
              message: '折扣金额超过小计的50%',
              suggestion: '确认折扣金额是否正确',
            });
          }
        }
        break;

      case 'unitPrice':
        if (typeof value === 'number' && value === 0) {
          warnings.push({
            field: fieldPath,
            message: '单价为0，确认是否为免费项目',
            suggestion: '如果不是免费项目，请输入正确单价',
          });
        }
        break;
    }

    return warnings;
  }

  cleanup(): void {
    this.debouncer.clear();
  }
}

// Form-level validation utility
export class FormValidator {
  private schema: z.ZodSchema;
  private fieldValidators: Map<string, FieldValidator> = new Map();

  constructor(schema: z.ZodSchema) {
    this.schema = schema;
  }

  validateForm(data: any): ValidationResult {
    try {
      const result = this.schema.safeParse(data);
      
      if (result.success) {
        return {
          isValid: true,
          errors: [],
          warnings: this.generateFormWarnings(data),
        };
      }

      const errors = result.error.errors.map(error => ({
        field: error.path.join('.'),
        message: error.message,
        code: error.code,
        severity: 'error' as const,
      }));

      return {
        isValid: false,
        errors,
        warnings: this.generateFormWarnings(data),
      };
    } catch (error) {
      console.error('Form validation error:', error);
      return {
        isValid: false,
        errors: [{
          field: 'form',
          message: '表单验证过程中发生错误',
          code: 'FORM_VALIDATION_ERROR',
          severity: 'error',
        }],
        warnings: [],
      };
    }
  }

  private generateFormWarnings(data: any): ValidationWarning[] {
    const warnings: ValidationWarning[] = [];

    // Generate form-level warnings
    if (data.items && Array.isArray(data.items)) {
      const totalItems = data.items.length;
      if (totalItems > 20) {
        warnings.push({
          field: 'items',
          message: `账单包含${totalItems}个项目，较多`,
          suggestion: '考虑合并相似项目或分拆为多个账单',
        });
      }

      const totalAmount = data.items.reduce((sum: number, item: any) => {
        return sum + (item.quantity || 0) * (item.unitPrice || 0);
      }, 0);

      if (totalAmount > 50000) {
        warnings.push({
          field: 'form',
          message: '账单总金额较大',
          suggestion: '确认金额计算是否正确',
        });
      }
    }

    return warnings;
  }

  getFieldValidator(fieldPath: string): FieldValidator {
    if (!this.fieldValidators.has(fieldPath)) {
      this.fieldValidators.set(fieldPath, new FieldValidator(this.schema));
    }
    return this.fieldValidators.get(fieldPath)!;
  }

  cleanup(): void {
    this.fieldValidators.forEach(validator => validator.cleanup());
    this.fieldValidators.clear();
  }
}

// Validation error display utility
export const displayValidationErrors = (errors: ValidationError[]): void => {
  errors.forEach(error => {
    if (error.severity === 'error') {
      billingNotifications.validation.requiredField(error.field);
    }
  });
};

// Business logic validation
export const validateBusinessRules = {
  // Check if bill can be marked as paid
  canMarkAsPaid: (bill: any): { valid: boolean; reason?: string } => {
    if ((bill.remainingAmount || 0) > 0) {
      return {
        valid: false,
        reason: '账单还有未支付金额，无法标记为已支付',
      };
    }
    return { valid: true };
  },

  // Check if payment amount is valid for bill
  isValidPaymentAmount: (amount: number, bill: any): { valid: boolean; reason?: string } => {
    const remainingAmount = bill.remainingAmount || 0;
    
    if (amount > remainingAmount) {
      return {
        valid: false,
        reason: `支付金额不能超过待付金额 $${remainingAmount.toFixed(2)}`,
      };
    }
    
    if (amount <= 0) {
      return {
        valid: false,
        reason: '支付金额必须大于0',
      };
    }
    
    return { valid: true };
  },

  // Check if bill can be deleted
  canDeleteBill: (bill: any): { valid: boolean; reason?: string } => {
    if (bill.status === 'paid') {
      return {
        valid: false,
        reason: '已支付的账单不能删除',
      };
    }
    
    if ((bill.paidAmount || 0) > 0) {
      return {
        valid: false,
        reason: '已有支付记录的账单不能删除',
      };
    }
    
    return { valid: true };
  },

  // Check if bill status transition is valid
  isValidStatusTransition: (fromStatus: string, toStatus: string): { valid: boolean; reason?: string } => {
    const validTransitions: Record<string, string[]> = {
      draft: ['sent', 'cancelled'],
      sent: ['confirmed', 'cancelled'],
      confirmed: ['paid', 'cancelled'],
      paid: [], // No transitions from paid
      cancelled: [], // No transitions from cancelled
    };

    const allowedTransitions = validTransitions[fromStatus] || [];
    
    if (!allowedTransitions.includes(toStatus)) {
      return {
        valid: false,
        reason: `不能从"${fromStatus}"状态转换到"${toStatus}"状态`,
      };
    }
    
    return { valid: true };
  },
};

// Export validation constants
export const VALIDATION_CONSTANTS = {
  MAX_BILL_AMOUNT: 999999.99,
  MAX_ITEMS_PER_BILL: 50,
  MAX_DESCRIPTION_LENGTH: 200,
  MAX_NOTES_LENGTH: 1000,
  MIN_PAYMENT_AMOUNT: 0.01,
  MAX_DISCOUNT_RATE: 100,
  DEBOUNCE_DELAY: 300,
} as const;
