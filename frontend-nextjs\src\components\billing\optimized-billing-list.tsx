'use client';

import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  IconSearch, 
  IconFilter, 
  IconRefresh,
  IconChevronDown,
  IconChevronUp,
  IconReceipt,
  IconCash,
  IconCreditCard,
  IconCalendar,
  IconUser
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { Bill } from '@/types/clinic';
import { billsAPI } from '@/lib/api/billing';
import { BillingCache, PerformanceMonitor, useDebounce, useVirtualScrolling } from '@/lib/billing-performance';
import { useRole } from '@/lib/role-context';

interface OptimizedBillingListProps {
  onBillSelect?: (bill: Bill) => void;
  onBillEdit?: (bill: Bill) => void;
  onPaymentProcess?: (bill: Bill) => void;
  filters?: {
    status?: string;
    patientId?: string;
    dateRange?: { start: string; end: string };
  };
}

interface BillListItem extends Bill {
  patientName: string;
  formattedDate: string;
  statusColor: string;
}

const ITEM_HEIGHT = 80; // Height of each bill item in pixels
const CONTAINER_HEIGHT = 600; // Height of the scrollable container

export function OptimizedBillingList({
  onBillSelect,
  onBillEdit,
  onPaymentProcess,
  filters = {},
}: OptimizedBillingListProps) {
  const { hasPermission } = useRole();
  const [bills, setBills] = useState<BillListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'status' | 'patient'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  
  const cache = BillingCache.getInstance();
  const containerRef = useRef<HTMLDivElement>(null);
  const loadingRef = useRef(false);

  // Debounced search to avoid excessive API calls
  const debouncedSearch = useDebounce(searchTerm, 300);

  // Performance monitoring
  const performanceTimer = useCallback(() => {
    return PerformanceMonitor.startTimer('billing-list-render');
  }, []);

  // Memoized filter function
  const filteredBills = useMemo(() => {
    const timer = performanceTimer();
    
    let filtered = bills;

    // Apply search filter
    if (debouncedSearch.trim()) {
      const searchLower = debouncedSearch.toLowerCase();
      filtered = filtered.filter(bill =>
        bill.billNumber.toLowerCase().includes(searchLower) ||
        bill.patientName.toLowerCase().includes(searchLower) ||
        bill.notes?.toLowerCase().includes(searchLower)
      );
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(bill => bill.status === statusFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'amount':
          comparison = a.totalAmount - b.totalAmount;
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'patient':
          comparison = a.patientName.localeCompare(b.patientName);
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });

    timer();
    return filtered;
  }, [bills, debouncedSearch, statusFilter, sortBy, sortOrder, performanceTimer]);

  // Virtual scrolling implementation
  const {
    visibleItems,
    startIndex,
    endIndex,
    totalHeight,
    offsetY,
    handleScroll,
  } = useVirtualScrolling(filteredBills, ITEM_HEIGHT, CONTAINER_HEIGHT);

  // Load bills with caching and performance optimization
  const loadBills = useCallback(async (pageNum: number = 1, append: boolean = false) => {
    if (loadingRef.current) return;
    
    try {
      loadingRef.current = true;
      setLoading(true);

      const cacheKey = `bills:${JSON.stringify({ 
        page: pageNum, 
        search: debouncedSearch, 
        status: statusFilter,
        sort: sortBy,
        order: sortOrder,
        ...filters 
      })}`;

      // Check cache first
      const cachedData = cache.get(cacheKey);
      if (cachedData && !append) {
        setBills(cachedData.bills);
        setTotalCount(cachedData.totalCount);
        setHasMore(cachedData.hasMore);
        setLoading(false);
        loadingRef.current = false;
        return;
      }

      const timer = PerformanceMonitor.startTimer('bills-api-call');
      
      const response = await billsAPI.getBills({
        page: pageNum,
        limit: 50,
        search: debouncedSearch,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        sort: `${sortOrder === 'desc' ? '-' : ''}${sortBy === 'date' ? 'createdAt' : sortBy}`,
        ...filters,
      });

      timer();

      // Transform bills for optimized rendering
      const transformedBills: BillListItem[] = response.docs.map(bill => ({
        ...bill,
        patientName: typeof bill.patient === 'object' ? bill.patient.fullName : 'Unknown',
        formattedDate: new Date(bill.createdAt).toLocaleDateString('zh-CN'),
        statusColor: getStatusColor(bill.status),
      }));

      const newBills = append ? [...bills, ...transformedBills] : transformedBills;
      setBills(newBills);
      setTotalCount(response.totalDocs);
      setHasMore(response.hasNextPage);

      // Cache the results
      cache.set(cacheKey, {
        bills: newBills,
        totalCount: response.totalDocs,
        hasMore: response.hasNextPage,
      });

    } catch (error) {
      console.error('Error loading bills:', error);
      toast.error('加载账单失败');
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, [debouncedSearch, statusFilter, sortBy, sortOrder, filters, bills, cache]);

  // Load more bills for infinite scrolling
  const loadMore = useCallback(() => {
    if (hasMore && !loading) {
      setPage(prev => prev + 1);
      loadBills(page + 1, true);
    }
  }, [hasMore, loading, page, loadBills]);

  // Intersection observer for infinite scrolling
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMore && !loading) {
          loadMore();
        }
      },
      { threshold: 0.1 }
    );

    const sentinel = document.getElementById('bills-sentinel');
    if (sentinel) {
      observer.observe(sentinel);
    }

    return () => observer.disconnect();
  }, [hasMore, loading, loadMore]);

  // Initial load and reload on filter changes
  useEffect(() => {
    setPage(1);
    setBills([]);
    loadBills(1, false);
  }, [debouncedSearch, statusFilter, sortBy, sortOrder, filters]);

  // Refresh function
  const handleRefresh = useCallback(() => {
    cache.clear();
    setPage(1);
    setBills([]);
    loadBills(1, false);
  }, [cache, loadBills]);

  // Status color mapping
  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'overdue': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-gray-100 text-gray-800';
      default: return 'bg-blue-100 text-blue-800';
    }
  };

  // Status display mapping
  const getStatusDisplay = (status: string): string => {
    switch (status) {
      case 'paid': return '已支付';
      case 'pending': return '待支付';
      case 'overdue': return '逾期';
      case 'cancelled': return '已取消';
      case 'draft': return '草稿';
      default: return status;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  return (
    <div className="space-y-4">
      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>账单列表</span>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                共 {totalCount} 条记录
              </span>
              <Button onClick={handleRefresh} size="sm" variant="outline">
                <IconRefresh className="h-4 w-4 mr-2" />
                刷新
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <IconSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索账单编号、患者姓名..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">全部状态</SelectItem>
                <SelectItem value="pending">待支付</SelectItem>
                <SelectItem value="paid">已支付</SelectItem>
                <SelectItem value="overdue">逾期</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">按日期</SelectItem>
                <SelectItem value="amount">按金额</SelectItem>
                <SelectItem value="status">按状态</SelectItem>
                <SelectItem value="patient">按患者</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort Order */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
            >
              {sortOrder === 'desc' ? <IconChevronDown className="h-4 w-4" /> : <IconChevronUp className="h-4 w-4" />}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Virtual Scrolled Bill List */}
      <Card>
        <CardContent className="p-0">
          <div
            ref={containerRef}
            className="h-[600px] overflow-auto"
            onScroll={handleScroll}
          >
            <div style={{ height: totalHeight, position: 'relative' }}>
              <div style={{ transform: `translateY(${offsetY}px)` }}>
                {visibleItems.map((bill, index) => (
                  <BillListItem
                    key={bill.id}
                    bill={bill}
                    onSelect={() => onBillSelect?.(bill)}
                    onEdit={() => onBillEdit?.(bill)}
                    onPayment={() => onPaymentProcess?.(bill)}
                    formatCurrency={formatCurrency}
                    getStatusDisplay={getStatusDisplay}
                    hasPermission={hasPermission}
                  />
                ))}
              </div>
            </div>

            {/* Loading indicator for infinite scroll */}
            {hasMore && (
              <div id="bills-sentinel" className="p-4 text-center">
                {loading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                ) : (
                  <Button onClick={loadMore} variant="outline" size="sm">
                    加载更多
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Memoized bill item component for performance
const BillListItem = React.memo(({ 
  bill, 
  onSelect, 
  onEdit, 
  onPayment, 
  formatCurrency, 
  getStatusDisplay, 
  hasPermission 
}: {
  bill: BillListItem;
  onSelect: () => void;
  onEdit: () => void;
  onPayment: () => void;
  formatCurrency: (amount: number) => string;
  getStatusDisplay: (status: string) => string;
  hasPermission: (permission: string) => boolean;
}) => (
  <div 
    className="flex items-center justify-between p-4 border-b hover:bg-muted/50 cursor-pointer"
    style={{ height: ITEM_HEIGHT }}
    onClick={onSelect}
  >
    <div className="flex items-center gap-4 flex-1">
      <div className="flex items-center gap-2">
        <IconReceipt className="h-5 w-5 text-muted-foreground" />
        <div>
          <div className="font-medium">{bill.billNumber}</div>
          <div className="text-sm text-muted-foreground flex items-center gap-1">
            <IconUser className="h-3 w-3" />
            {bill.patientName}
          </div>
        </div>
      </div>
      
      <div className="flex items-center gap-2">
        <IconCalendar className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm">{bill.formattedDate}</span>
      </div>
    </div>

    <div className="flex items-center gap-4">
      <div className="text-right">
        <div className="font-bold">{formatCurrency(bill.totalAmount)}</div>
        {bill.remainingAmount > 0 && (
          <div className="text-sm text-muted-foreground">
            余额: {formatCurrency(bill.remainingAmount)}
          </div>
        )}
      </div>

      <Badge className={bill.statusColor}>
        {getStatusDisplay(bill.status)}
      </Badge>

      <div className="flex items-center gap-1">
        {hasPermission('canEditBills') && (
          <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); onEdit(); }}>
            编辑
          </Button>
        )}
        {hasPermission('canProcessPayments') && bill.status !== 'paid' && (
          <Button size="sm" onClick={(e) => { e.stopPropagation(); onPayment(); }}>
            <IconCash className="h-4 w-4 mr-1" />
            支付
          </Button>
        )}
      </div>
    </div>
  </div>
));
