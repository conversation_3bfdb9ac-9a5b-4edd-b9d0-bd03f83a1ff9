#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules/msw/cli/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules/msw/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules/msw/cli/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules/msw/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/msw@2.10.3_@types+node@22.10.2_typescript@5.7.2/node_modules:/mnt/c/Users/<USER>/Desktop/nord-coast/frontend-nextjs/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../msw/cli/index.js" "$@"
else
  exec node  "$basedir/../msw/cli/index.js" "$@"
fi
