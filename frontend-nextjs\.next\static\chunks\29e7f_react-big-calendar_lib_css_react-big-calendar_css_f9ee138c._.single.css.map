{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/node_modules/.pnpm/react-big-calendar@1.19.4_r_e977bf1f6581a80000fdaab547ddf0cf/node_modules/react-big-calendar/lib/css/react-big-calendar.css"], "sourcesContent": ["@charset \"UTF-8\";\n.rbc-btn {\n  color: inherit;\n  font: inherit;\n  margin: 0;\n}\n\nbutton.rbc-btn {\n  overflow: visible;\n  text-transform: none;\n  -webkit-appearance: button;\n     -moz-appearance: button;\n          appearance: button;\n  cursor: pointer;\n}\n\nbutton[disabled].rbc-btn {\n  cursor: not-allowed;\n}\n\nbutton.rbc-input::-moz-focus-inner {\n  border: 0;\n  padding: 0;\n}\n\n.rbc-calendar {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  height: 100%;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-align: stretch;\n      -ms-flex-align: stretch;\n          align-items: stretch;\n}\n\n.rbc-m-b-negative-3 {\n  margin-bottom: -3px;\n}\n\n.rbc-h-full {\n  height: 100%;\n}\n\n.rbc-calendar *,\n.rbc-calendar *:before,\n.rbc-calendar *:after {\n  -webkit-box-sizing: inherit;\n          box-sizing: inherit;\n}\n\n.rbc-abs-full, .rbc-row-bg {\n  overflow: hidden;\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n}\n\n.rbc-ellipsis, .rbc-show-more, .rbc-row-segment .rbc-event-content, .rbc-event-label {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n.rbc-rtl {\n  direction: rtl;\n}\n\n.rbc-off-range {\n  color: #999999;\n}\n\n.rbc-off-range-bg {\n  background: #e6e6e6;\n}\n\n.rbc-header {\n  overflow: hidden;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  padding: 0 3px;\n  text-align: center;\n  vertical-align: middle;\n  font-weight: bold;\n  font-size: 90%;\n  min-height: 0;\n  border-bottom: 1px solid #ddd;\n}\n.rbc-header + .rbc-header {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-header + .rbc-header {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-header > a, .rbc-header > a:active, .rbc-header > a:visited {\n  color: inherit;\n  text-decoration: none;\n}\n\n.rbc-button-link {\n  color: inherit;\n  background: none;\n  margin: 0;\n  padding: 0;\n  border: none;\n  cursor: pointer;\n  -webkit-user-select: text;\n     -moz-user-select: text;\n      -ms-user-select: text;\n          user-select: text;\n}\n\n.rbc-row-content {\n  position: relative;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n  z-index: 4;\n}\n\n.rbc-row-content-scrollable {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  height: 100%;\n}\n.rbc-row-content-scrollable .rbc-row-content-scroll-container {\n  height: 100%;\n  overflow-y: scroll;\n  -ms-overflow-style: none; /* IE and Edge */\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n  scrollbar-width: none; /* Firefox */\n  /* Hide scrollbar for Chrome, Safari and Opera */\n}\n.rbc-row-content-scrollable .rbc-row-content-scroll-container::-webkit-scrollbar {\n  display: none;\n}\n\n.rbc-today {\n  background-color: #eaf6ff;\n}\n\n.rbc-toolbar {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -ms-flex-wrap: wrap;\n      flex-wrap: wrap;\n  -webkit-box-pack: center;\n      -ms-flex-pack: center;\n          justify-content: center;\n  -webkit-box-align: center;\n      -ms-flex-align: center;\n          align-items: center;\n  margin-bottom: 10px;\n  font-size: 16px;\n}\n.rbc-toolbar .rbc-toolbar-label {\n  -webkit-box-flex: 1;\n      -ms-flex-positive: 1;\n          flex-grow: 1;\n  padding: 0 10px;\n  text-align: center;\n}\n.rbc-toolbar button {\n  color: #373a3c;\n  display: inline-block;\n  margin: 0;\n  text-align: center;\n  vertical-align: middle;\n  background: none;\n  background-image: none;\n  border: 1px solid #ccc;\n  padding: 0.375rem 1rem;\n  border-radius: 4px;\n  line-height: normal;\n  white-space: nowrap;\n}\n.rbc-toolbar button:active, .rbc-toolbar button.rbc-active {\n  background-image: none;\n  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n          box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n.rbc-toolbar button:active:hover, .rbc-toolbar button:active:focus, .rbc-toolbar button.rbc-active:hover, .rbc-toolbar button.rbc-active:focus {\n  color: #373a3c;\n  background-color: #d4d4d4;\n  border-color: #8c8c8c;\n}\n.rbc-toolbar button:focus {\n  color: #373a3c;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n.rbc-toolbar button:hover {\n  color: #373a3c;\n  cursor: pointer;\n  background-color: #e6e6e6;\n  border-color: #adadad;\n}\n\n.rbc-btn-group {\n  display: inline-block;\n  white-space: nowrap;\n}\n.rbc-btn-group > button:first-child:not(:last-child) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.rbc-btn-group > button:last-child:not(:first-child) {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.rbc-rtl .rbc-btn-group > button:first-child:not(:last-child) {\n  border-radius: 4px;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.rbc-rtl .rbc-btn-group > button:last-child:not(:first-child) {\n  border-radius: 4px;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.rbc-btn-group > button:not(:first-child):not(:last-child) {\n  border-radius: 0;\n}\n.rbc-btn-group button + button {\n  margin-left: -1px;\n}\n.rbc-rtl .rbc-btn-group button + button {\n  margin-left: 0;\n  margin-right: -1px;\n}\n.rbc-btn-group + .rbc-btn-group, .rbc-btn-group + button {\n  margin-left: 10px;\n}\n\n@media (max-width: 767px) {\n  .rbc-toolbar {\n    -webkit-box-orient: vertical;\n    -webkit-box-direction: normal;\n        -ms-flex-direction: column;\n            flex-direction: column;\n  }\n}\n.rbc-event, .rbc-day-slot .rbc-background-event {\n  border: none;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  -webkit-box-shadow: none;\n          box-shadow: none;\n  margin: 0;\n  padding: 2px 5px;\n  background-color: #3174ad;\n  border-radius: 5px;\n  color: #fff;\n  cursor: pointer;\n  width: 100%;\n  text-align: left;\n}\n.rbc-slot-selecting .rbc-event, .rbc-slot-selecting .rbc-day-slot .rbc-background-event, .rbc-day-slot .rbc-slot-selecting .rbc-background-event {\n  cursor: inherit;\n  pointer-events: none;\n}\n.rbc-event.rbc-selected, .rbc-day-slot .rbc-selected.rbc-background-event {\n  background-color: #265985;\n}\n.rbc-event:focus, .rbc-day-slot .rbc-background-event:focus {\n  outline: 5px auto #3b99fc;\n}\n\n.rbc-event-label {\n  font-size: 80%;\n}\n\n.rbc-event-overlaps {\n  -webkit-box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);\n          box-shadow: -1px 1px 5px 0px rgba(51, 51, 51, 0.5);\n}\n\n.rbc-event-continues-prior {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n.rbc-event-continues-after {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rbc-event-continues-earlier {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n\n.rbc-event-continues-later {\n  border-bottom-left-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n.rbc-row {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n\n.rbc-row-segment {\n  padding: 0 1px 1px 1px;\n}\n.rbc-selected-cell {\n  background-color: rgba(0, 0, 0, 0.1);\n}\n\n.rbc-show-more {\n  background-color: rgba(255, 255, 255, 0.3);\n  z-index: 4;\n  font-weight: bold;\n  font-size: 85%;\n  height: auto;\n  line-height: normal;\n  color: #3174ad;\n}\n.rbc-show-more:hover, .rbc-show-more:focus {\n  color: #265985;\n}\n\n.rbc-month-view {\n  position: relative;\n  border: 1px solid #ddd;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  width: 100%;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n  height: 100%;\n}\n\n.rbc-month-header {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n\n.rbc-month-row {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  position: relative;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  -ms-flex-preferred-size: 0px;\n      flex-basis: 0px;\n  overflow: hidden;\n  height: 100%;\n}\n.rbc-month-row + .rbc-month-row {\n  border-top: 1px solid #ddd;\n}\n\n.rbc-date-cell {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  min-width: 0;\n  padding-right: 5px;\n  text-align: right;\n}\n.rbc-date-cell.rbc-now {\n  font-weight: bold;\n}\n.rbc-date-cell > a, .rbc-date-cell > a:active, .rbc-date-cell > a:visited {\n  color: inherit;\n  text-decoration: none;\n}\n\n.rbc-row-bg {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  overflow: hidden;\n  right: 1px;\n}\n\n.rbc-day-bg {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n}\n.rbc-day-bg + .rbc-day-bg {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-day-bg + .rbc-day-bg {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n\n.rbc-overlay {\n  position: absolute;\n  z-index: 5;\n  border: 1px solid #e5e5e5;\n  background-color: #fff;\n  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.25);\n  padding: 10px;\n}\n.rbc-overlay > * + * {\n  margin-top: 1px;\n}\n\n.rbc-overlay-header {\n  border-bottom: 1px solid #e5e5e5;\n  margin: -10px -10px 5px -10px;\n  padding: 2px 10px;\n}\n\n.rbc-agenda-view {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  overflow: auto;\n}\n.rbc-agenda-view table.rbc-agenda-table {\n  width: 100%;\n  border: 1px solid #ddd;\n  border-spacing: 0;\n  border-collapse: collapse;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr > td {\n  padding: 5px 10px;\n  vertical-align: top;\n}\n.rbc-agenda-view table.rbc-agenda-table .rbc-agenda-time-cell {\n  padding-left: 15px;\n  padding-right: 15px;\n  text-transform: lowercase;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-agenda-view table.rbc-agenda-table tbody > tr > td + td {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-agenda-view table.rbc-agenda-table tbody > tr + tr {\n  border-top: 1px solid #ddd;\n}\n.rbc-agenda-view table.rbc-agenda-table thead > tr > th {\n  padding: 3px 5px;\n  text-align: left;\n  border-bottom: 1px solid #ddd;\n}\n.rbc-rtl .rbc-agenda-view table.rbc-agenda-table thead > tr > th {\n  text-align: right;\n}\n\n.rbc-agenda-time-cell {\n  text-transform: lowercase;\n}\n.rbc-agenda-time-cell .rbc-continues-after:after {\n  content: \" »\";\n}\n.rbc-agenda-time-cell .rbc-continues-prior:before {\n  content: \"« \";\n}\n\n.rbc-agenda-date-cell,\n.rbc-agenda-time-cell {\n  white-space: nowrap;\n}\n\n.rbc-agenda-event-cell {\n  width: 100%;\n}\n\n.rbc-time-column {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  min-height: 100%;\n}\n.rbc-time-column .rbc-timeslot-group {\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n}\n\n.rbc-timeslot-group {\n  border-bottom: 1px solid #ddd;\n  min-height: 40px;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-flow: column nowrap;\n          flex-flow: column nowrap;\n}\n\n.rbc-time-gutter,\n.rbc-header-gutter {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n}\n\n.rbc-label {\n  padding: 0 5px;\n}\n\n.rbc-day-slot {\n  position: relative;\n}\n.rbc-day-slot .rbc-events-container {\n  bottom: 0;\n  left: 0;\n  position: absolute;\n  right: 0;\n  margin-right: 10px;\n  top: 0;\n}\n.rbc-day-slot .rbc-events-container.rbc-rtl {\n  left: 10px;\n  right: 0;\n}\n.rbc-day-slot .rbc-event, .rbc-day-slot .rbc-background-event {\n  border: 1px solid #265985;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  max-height: 100%;\n  min-height: 20px;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-flow: column wrap;\n          flex-flow: column wrap;\n  -webkit-box-align: start;\n      -ms-flex-align: start;\n          align-items: flex-start;\n  overflow: hidden;\n  position: absolute;\n}\n.rbc-day-slot .rbc-background-event {\n  opacity: 0.75;\n}\n.rbc-day-slot .rbc-event-label {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n  padding-right: 5px;\n  width: auto;\n}\n.rbc-day-slot .rbc-event-content {\n  width: 100%;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  word-wrap: break-word;\n  line-height: 1;\n  height: 100%;\n  min-height: 1em;\n}\n.rbc-day-slot .rbc-time-slot {\n  border-top: 1px solid #f7f7f7;\n}\n\n.rbc-time-view-resources .rbc-time-gutter,\n.rbc-time-view-resources .rbc-time-header-gutter {\n  position: sticky;\n  left: 0;\n  background-color: white;\n  border-right: 1px solid #ddd;\n  z-index: 10;\n  margin-right: -1px;\n}\n.rbc-time-view-resources .rbc-time-header {\n  overflow: hidden;\n}\n.rbc-time-view-resources .rbc-time-header-content {\n  min-width: auto;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n  -ms-flex-preferred-size: 0px;\n      flex-basis: 0px;\n}\n.rbc-time-view-resources .rbc-time-header-cell-single-day {\n  display: none;\n}\n.rbc-time-view-resources .rbc-day-slot {\n  min-width: 140px;\n}\n.rbc-time-view-resources .rbc-header,\n.rbc-time-view-resources .rbc-day-bg {\n  width: 140px;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 1 0px;\n          flex: 1 1 0;\n  -ms-flex-preferred-size: 0 px;\n      flex-basis: 0 px;\n}\n\n.rbc-time-header-content + .rbc-time-header-content {\n  margin-left: -1px;\n}\n\n.rbc-time-slot {\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0px;\n          flex: 1 0 0;\n}\n.rbc-time-slot.rbc-now {\n  font-weight: bold;\n}\n\n.rbc-day-header {\n  text-align: center;\n}\n\n.rbc-slot-selection {\n  z-index: 10;\n  position: absolute;\n  background-color: rgba(0, 0, 0, 0.5);\n  color: white;\n  font-size: 75%;\n  width: 100%;\n  padding: 3px;\n}\n\n.rbc-slot-selecting {\n  cursor: move;\n}\n\n.rbc-time-view {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  width: 100%;\n  border: 1px solid #ddd;\n  min-height: 0;\n}\n.rbc-time-view .rbc-time-gutter {\n  white-space: nowrap;\n  text-align: right;\n}\n.rbc-time-view .rbc-allday-cell {\n  -webkit-box-sizing: content-box;\n          box-sizing: content-box;\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n.rbc-time-view .rbc-allday-cell + .rbc-allday-cell {\n  border-left: 1px solid #ddd;\n}\n.rbc-time-view .rbc-allday-events {\n  position: relative;\n  z-index: 4;\n}\n.rbc-time-view .rbc-row {\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  min-height: 20px;\n}\n\n.rbc-time-header {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 0;\n      -ms-flex: 0 0 auto;\n          flex: 0 0 auto;\n  -webkit-box-orient: horizontal;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: row;\n          flex-direction: row;\n}\n.rbc-time-header.rbc-overflowing {\n  border-right: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-header.rbc-overflowing {\n  border-right-width: 0;\n  border-left: 1px solid #ddd;\n}\n.rbc-time-header > .rbc-row:first-child {\n  border-bottom: 1px solid #ddd;\n}\n.rbc-time-header > .rbc-row.rbc-row-resource {\n  border-bottom: 1px solid #ddd;\n}\n\n.rbc-time-header-cell-single-day {\n  display: none;\n}\n\n.rbc-time-header-content {\n  -webkit-box-flex: 1;\n      -ms-flex: 1;\n          flex: 1;\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  min-width: 0;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-header-content {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-time-header-content > .rbc-row.rbc-row-resource {\n  border-bottom: 1px solid #ddd;\n  -ms-flex-negative: 0;\n      flex-shrink: 0;\n}\n\n.rbc-time-content {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-flex: 1;\n      -ms-flex: 1 0 0%;\n          flex: 1 0 0%;\n  -webkit-box-align: start;\n      -ms-flex-align: start;\n          align-items: flex-start;\n  width: 100%;\n  border-top: 2px solid #ddd;\n  overflow-y: auto;\n  position: relative;\n}\n.rbc-time-content > .rbc-time-gutter {\n  -webkit-box-flex: 0;\n      -ms-flex: none;\n          flex: none;\n}\n.rbc-time-content > * + * > * {\n  border-left: 1px solid #ddd;\n}\n.rbc-rtl .rbc-time-content > * + * > * {\n  border-left-width: 0;\n  border-right: 1px solid #ddd;\n}\n.rbc-time-content > .rbc-day-slot {\n  width: 100%;\n  -moz-user-select: none;\n   -ms-user-select: none;\n       user-select: none;\n  -webkit-user-select: none;\n}\n\n.rbc-current-time-indicator {\n  position: absolute;\n  z-index: 3;\n  left: 0;\n  right: 0;\n  height: 1px;\n  background-color: #74ad31;\n  pointer-events: none;\n}\n\n.rbc-resource-grouping.rbc-time-header-content {\n  display: -webkit-box;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-box-orient: vertical;\n  -webkit-box-direction: normal;\n      -ms-flex-direction: column;\n          flex-direction: column;\n}\n.rbc-resource-grouping .rbc-row .rbc-header {\n  width: 141px;\n}\n\n/*# sourceMappingURL=react-big-calendar.css.map */"], "names": [], "mappings": "AACA;;;;;;AAMA;;;;;;;;;AASA;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAIA;;;;;AAOA;;;;;;AASA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;;;;;;;AAeA;;;;AAGA;;;;;AAIA;;;;;AAKA;;;;;;;;;;;;;AAaA;;;;;;;;;AASA;;;;;;;;;;;AAUA;;;;;;;;;AASA;;;;AAIA;;;;AAIA;;;;;;;;;;;;AAeA;;;;;;;;AAOA;;;;;;;;;;;;;;AAcA;;;;;;;;AAOA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAOA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAIA;EACE;;;;;;;;AAOF;;;;;;;;;;;;;;;;AAeA;;;;;AAIA;;;;AAGA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;;;AAUA;;;;AAGA;;;;AAIA;;;;;;;;;;AASA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;;;;;;;AAUA;;;;;;;;;;;;;;;;;AAiBA;;;;AAIA;;;;;;;;;AAQA;;;;AAGA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;AAKA;;;;AAGA;;;;;AAKA;;;;;;;;;;AASA;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;AAaA;;;;;;;AAMA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;;;;;;;AAUA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;AAOA;;;;AAIA;;;;AAGA;;;;;;AAQA;;;;;AAIA;;;;;;;;;;;;;;;;AAiBA;;;;AAGA;;;;;;;;AAOA;;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;AASA;;;;AAGA;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;;;;AAUA;;;;AAIA;;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;;;;;;AAeA;;;;;AAIA;;;;;;;;AAOA;;;;AAGA;;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;AAYA;;;;AAGA;;;;;AAIA;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;;AAcA;;;;;AAIA;;;;;;AAMA;;;;;;;;;;;;;;AAeA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;;;;AASA", "ignoreList": [0]}}]}