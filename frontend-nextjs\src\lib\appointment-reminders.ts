// Appointment reminder system for medical clinic
// Handles scheduling and managing appointment reminders

import { Appointment } from '@/types/clinic';
import { appointmentNotifications } from './appointment-notifications';
import { formatDateTime } from './calendar-utils';

export interface ReminderSettings {
  enabled: boolean;
  reminderTimes: number[]; // Minutes before appointment
  methods: ('notification' | 'email' | 'sms')[];
}

export interface AppointmentReminder {
  id: string;
  appointmentId: string;
  reminderTime: Date;
  method: 'notification' | 'email' | 'sms';
  status: 'pending' | 'sent' | 'failed';
  createdAt: Date;
}

class AppointmentReminderService {
  private reminders: Map<string, AppointmentReminder[]> = new Map();
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private defaultSettings: ReminderSettings = {
    enabled: true,
    reminderTimes: [60, 15], // 1 hour and 15 minutes before
    methods: ['notification'],
  };

  /**
   * Schedule reminders for an appointment
   */
  scheduleReminders(appointment: Appointment, settings?: Partial<ReminderSettings>): void {
    const reminderSettings = { ...this.defaultSettings, ...settings };
    
    if (!reminderSettings.enabled) {
      return;
    }

    // Clear existing reminders for this appointment
    this.clearReminders(appointment.id);

    const appointmentTime = new Date(appointment.appointmentDate);
    const now = new Date();

    reminderSettings.reminderTimes.forEach((minutesBefore) => {
      const reminderTime = new Date(appointmentTime.getTime() - minutesBefore * 60 * 1000);
      
      // Only schedule if reminder time is in the future
      if (reminderTime > now) {
        reminderSettings.methods.forEach((method) => {
          const reminder: AppointmentReminder = {
            id: `${appointment.id}-${minutesBefore}-${method}`,
            appointmentId: appointment.id,
            reminderTime,
            method,
            status: 'pending',
            createdAt: new Date(),
          };

          this.scheduleReminder(reminder, appointment);
        });
      }
    });
  }

  /**
   * Schedule a single reminder
   */
  private scheduleReminder(reminder: AppointmentReminder, appointment: Appointment): void {
    const now = new Date();
    const delay = reminder.reminderTime.getTime() - now.getTime();

    if (delay <= 0) {
      // Send immediately if time has passed
      this.sendReminder(reminder, appointment);
      return;
    }

    const timer = setTimeout(() => {
      this.sendReminder(reminder, appointment);
    }, delay);

    // Store the timer for potential cancellation
    this.timers.set(reminder.id, timer);

    // Store the reminder
    const appointmentReminders = this.reminders.get(appointment.id) || [];
    appointmentReminders.push(reminder);
    this.reminders.set(appointment.id, appointmentReminders);

    console.log(`Scheduled reminder for appointment ${appointment.id} at ${reminder.reminderTime.toISOString()}`);
  }

  /**
   * Send a reminder
   */
  private async sendReminder(reminder: AppointmentReminder, appointment: Appointment): Promise<void> {
    try {
      const minutesUntil = Math.floor(
        (new Date(appointment.appointmentDate).getTime() - new Date().getTime()) / (1000 * 60)
      );

      switch (reminder.method) {
        case 'notification':
          this.sendNotificationReminder(appointment, minutesUntil);
          break;
        case 'email':
          await this.sendEmailReminder(appointment, minutesUntil);
          break;
        case 'sms':
          await this.sendSMSReminder(appointment, minutesUntil);
          break;
      }

      reminder.status = 'sent';
      appointmentNotifications.reminder.reminderSet(
        appointment,
        formatDateTime(reminder.reminderTime)
      );
    } catch (error) {
      console.error('Failed to send reminder:', error);
      reminder.status = 'failed';
    } finally {
      // Clean up timer
      const timer = this.timers.get(reminder.id);
      if (timer) {
        clearTimeout(timer);
        this.timers.delete(reminder.id);
      }
    }
  }

  /**
   * Send browser notification reminder
   */
  private sendNotificationReminder(appointment: Appointment, minutesUntil: number): void {
    // Check if notifications are supported and permitted
    if ('Notification' in window) {
      if (Notification.permission === 'granted') {
        const notification = new Notification('预约提醒', {
          body: `${appointment.patient.fullName} 的预约将在 ${minutesUntil} 分钟后开始`,
          icon: '/favicon.ico',
          tag: `appointment-${appointment.id}`,
        });

        // Auto-close after 10 seconds
        setTimeout(() => notification.close(), 10000);
      } else if (Notification.permission !== 'denied') {
        Notification.requestPermission().then((permission) => {
          if (permission === 'granted') {
            this.sendNotificationReminder(appointment, minutesUntil);
          }
        });
      }
    }

    // Also show toast notification
    appointmentNotifications.reminder.upcoming(appointment, minutesUntil);
  }

  /**
   * Send email reminder (placeholder - would integrate with email service)
   */
  private async sendEmailReminder(appointment: Appointment, minutesUntil: number): Promise<void> {
    // This would integrate with an email service like SendGrid, AWS SES, etc.
    console.log(`Email reminder would be sent for appointment ${appointment.id}`);
    
    // Placeholder implementation
    const emailData = {
      to: appointment.patient.email,
      subject: '预约提醒',
      body: `您好 ${appointment.patient.fullName}，您的预约将在 ${minutesUntil} 分钟后开始。`,
      appointmentDetails: {
        date: formatDateTime(new Date(appointment.appointmentDate)),
        treatment: appointment.treatment?.name || '咨询',
        practitioner: `${appointment.practitioner.firstName} ${appointment.practitioner.lastName}`,
      },
    };

    // TODO: Implement actual email sending
    console.log('Email data:', emailData);
  }

  /**
   * Send SMS reminder (placeholder - would integrate with SMS service)
   */
  private async sendSMSReminder(appointment: Appointment, minutesUntil: number): Promise<void> {
    // This would integrate with an SMS service like Twilio, AWS SNS, etc.
    console.log(`SMS reminder would be sent for appointment ${appointment.id}`);
    
    // Placeholder implementation
    const smsData = {
      to: appointment.patient.phone,
      message: `预约提醒：${appointment.patient.fullName}，您的预约将在 ${minutesUntil} 分钟后开始。时间：${formatDateTime(new Date(appointment.appointmentDate))}`,
    };

    // TODO: Implement actual SMS sending
    console.log('SMS data:', smsData);
  }

  /**
   * Clear all reminders for an appointment
   */
  clearReminders(appointmentId: string): void {
    const reminders = this.reminders.get(appointmentId) || [];
    
    reminders.forEach((reminder) => {
      const timer = this.timers.get(reminder.id);
      if (timer) {
        clearTimeout(timer);
        this.timers.delete(reminder.id);
      }
    });

    this.reminders.delete(appointmentId);
    console.log(`Cleared reminders for appointment ${appointmentId}`);
  }

  /**
   * Get reminders for an appointment
   */
  getReminders(appointmentId: string): AppointmentReminder[] {
    return this.reminders.get(appointmentId) || [];
  }

  /**
   * Check for overdue appointments and send notifications
   */
  checkOverdueAppointments(appointments: Appointment[]): void {
    const now = new Date();
    
    appointments.forEach((appointment) => {
      const appointmentTime = new Date(appointment.appointmentDate);
      const isOverdue = appointmentTime < now && 
        (appointment.status === 'scheduled' || appointment.status === 'confirmed');
      
      if (isOverdue) {
        appointmentNotifications.reminder.overdue(appointment);
      }
    });
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  }

  /**
   * Check if notifications are supported and permitted
   */
  isNotificationSupported(): boolean {
    return 'Notification' in window && Notification.permission === 'granted';
  }

  /**
   * Update reminder settings
   */
  updateSettings(settings: Partial<ReminderSettings>): void {
    this.defaultSettings = { ...this.defaultSettings, ...settings };
  }

  /**
   * Get current reminder settings
   */
  getSettings(): ReminderSettings {
    return { ...this.defaultSettings };
  }
}

// Export singleton instance
export const appointmentReminderService = new AppointmentReminderService();

// Export utility functions
export const scheduleAppointmentReminders = (
  appointment: Appointment,
  settings?: Partial<ReminderSettings>
) => {
  appointmentReminderService.scheduleReminders(appointment, settings);
};

export const clearAppointmentReminders = (appointmentId: string) => {
  appointmentReminderService.clearReminders(appointmentId);
};

export const checkOverdueAppointments = (appointments: Appointment[]) => {
  appointmentReminderService.checkOverdueAppointments(appointments);
};
