{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/node_modules__pnpm_f70aea8e._.js", "server/edge/chunks/[root-of-the-server]__92ca1197._.js", "server/edge/chunks/edge-wrapper_26561c45.js", "server/edge/chunks/_14c1c4d5._.js", "server/edge/chunks/a5167_@clerk_backend_dist_a1e27434._.js", "server/edge/chunks/79fbd_@clerk_shared_dist_f7e95f9b._.js", "server/edge/chunks/4e4c4_@clerk_nextjs_dist_esm_4e5142ff._.js", "server/edge/chunks/node_modules__pnpm_fac582cc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_5ff26227.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(api|trpc))(.*)(\\\\.json)?[\\/#\\?]?$", "originalSource": "/(api|trpc)(.*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "6iMNFxJQRw5kwyobQ2E/Q8u2QleJW6yuaZTizsNE3Gw=", "__NEXT_PREVIEW_MODE_ID": "ca2b58def6af2d8144550912d308cba8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3139bdf3d9ba362928b4d6073afed9e7739b6975c20fe7bbf55897035893e0be", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f2c57300c34dedb8aebcd9c6bb6f5320c209253e41db5078ad44032ad01af011"}}}, "sortedMiddleware": ["/"], "functions": {}}