{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/scroll-area.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as ScrollAreaPrimitive from '@radix-ui/react-scroll-area';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction ScrollArea({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.Root>) {\r\n  return (\r\n    <ScrollAreaPrimitive.Root\r\n      data-slot='scroll-area'\r\n      className={cn('relative', className)}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.Viewport\r\n        data-slot='scroll-area-viewport'\r\n        className='focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1'\r\n      >\r\n        {children}\r\n      </ScrollAreaPrimitive.Viewport>\r\n      <ScrollBar />\r\n      <ScrollAreaPrimitive.Corner />\r\n    </ScrollAreaPrimitive.Root>\r\n  );\r\n}\r\n\r\nfunction ScrollBar({\r\n  className,\r\n  orientation = 'vertical',\r\n  ...props\r\n}: React.ComponentProps<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>) {\r\n  return (\r\n    <ScrollAreaPrimitive.ScrollAreaScrollbar\r\n      data-slot='scroll-area-scrollbar'\r\n      orientation={orientation}\r\n      className={cn(\r\n        'flex touch-none p-px transition-colors select-none',\r\n        orientation === 'vertical' &&\r\n          'h-full w-2.5 border-l border-l-transparent',\r\n        orientation === 'horizontal' &&\r\n          'h-2.5 flex-col border-t border-t-transparent',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ScrollAreaPrimitive.ScrollAreaThumb\r\n        data-slot='scroll-area-thumb'\r\n        className='bg-border relative flex-1 rounded-full'\r\n      />\r\n    </ScrollAreaPrimitive.ScrollAreaScrollbar>\r\n  );\r\n}\r\n\r\nexport { ScrollArea, ScrollBar };\r\n"], "names": [], "mappings": ";;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,4SAAC,oRAAA,CAAA,OAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;;0BAET,4SAAC,oRAAA,CAAA,WAA4B;gBAC3B,aAAU;gBACV,WAAU;0BAET;;;;;;0BAEH,4SAAC;;;;;0BACD,4SAAC,oRAAA,CAAA,SAA0B;;;;;;;;;;;AAGjC;KArBS;AAuBT,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,UAAU,EACxB,GAAG,OACkE;IACrE,qBACE,4SAAC,oRAAA,CAAA,sBAAuC;QACtC,aAAU;QACV,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA,gBAAgB,cACd,8CACF,gBAAgB,gBACd,gDACF;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,oRAAA,CAAA,kBAAmC;YAClC,aAAU;YACV,WAAU;;;;;;;;;;;AAIlB;MAzBS", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst badgeVariants = cva(\r\n  'inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90',\r\n        secondary:\r\n          'border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90',\r\n        destructive:\r\n          'border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground'\r\n      }\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default'\r\n    }\r\n  }\r\n);\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'span'> &\r\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\r\n  const Comp = asChild ? Slot : 'span';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot='badge'\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/api.ts"], "sourcesContent": ["// Enhanced API utilities with best-practice authentication\nimport { Appointment, Patient, Treatment, PayloadResponse, DashboardMetrics, AppointmentCreateData, AppointmentUpdateData } from '@/types/clinic';\n\nexport interface ApiError {\n  error: string;\n  timestamp: string;\n  details?: any;\n}\n\nexport interface ApiResponse<T> {\n  data?: T;\n  error?: ApiError;\n  success: boolean;\n}\n\n// Enhanced API fetch function with proper error handling\nasync function apiRequest<T>(endpoint: string, options?: RequestInit): Promise<T> {\n  const url = `/api${endpoint}`;\n\n  try {\n    const response = await fetch(url, {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options?.headers,\n      },\n      ...options,\n    });\n\n    const responseData = await response.json();\n\n    if (!response.ok) {\n      // Handle structured error responses\n      if (responseData.error) {\n        throw new Error(responseData.error);\n      }\n      throw new Error(`API request failed: ${response.status} ${response.statusText}`);\n    }\n\n    return responseData;\n  } catch (error) {\n    console.error(`API request to ${url} failed:`, error);\n    throw error;\n  }\n}\n\n// User sync utility\nexport const authApi = {\n  syncUser: async (): Promise<any> => {\n    return apiRequest('/auth/sync', { method: 'POST' });\n  },\n};\n\n// Appointments API\nexport const appointmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number; where?: any }): Promise<PayloadResponse<Appointment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n\n    // Handle where clause for filtering\n    if (params?.where) {\n      // Convert where object to query parameters\n      // This is a simplified implementation - you might need to expand this based on your needs\n      if (params.where.patient?.equals) {\n        searchParams.append('where[patient][equals]', params.where.patient.equals);\n      }\n    }\n\n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Appointment>>(`/appointments${query}`);\n  },\n\n  getById: async (id: string): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`);\n  },\n\n  create: async (data: AppointmentCreateData): Promise<Appointment> => {\n    return apiRequest<Appointment>('/appointments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: AppointmentUpdateData): Promise<Appointment> => {\n    return apiRequest<Appointment>(`/appointments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/appointments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Patients API\nexport const patientsApi = {\n  getAll: async (params?: { limit?: number; page?: number; search?: string }): Promise<PayloadResponse<Patient>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    if (params?.search) searchParams.append('where[or][0][fullName][contains]', params.search);\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Patient>>(`/patients${query}`);\n  },\n\n  getById: async (id: string): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`);\n  },\n\n  create: async (data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>('/patients', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Patient>): Promise<Patient> => {\n    return apiRequest<Patient>(`/patients/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/patients/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Treatments API\nexport const treatmentsApi = {\n  getAll: async (params?: { limit?: number; page?: number }): Promise<PayloadResponse<Treatment>> => {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.append('limit', params.limit.toString());\n    if (params?.page) searchParams.append('page', params.page.toString());\n    \n    const query = searchParams.toString() ? `?${searchParams.toString()}` : '';\n    return apiRequest<PayloadResponse<Treatment>>(`/treatments${query}`);\n  },\n\n  getById: async (id: string): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`);\n  },\n\n  create: async (data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>('/treatments', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  },\n\n  update: async (id: string, data: Partial<Treatment>): Promise<Treatment> => {\n    return apiRequest<Treatment>(`/treatments/${id}`, {\n      method: 'PATCH',\n      body: JSON.stringify(data),\n    });\n  },\n\n  delete: async (id: string): Promise<void> => {\n    return apiRequest<void>(`/treatments/${id}`, {\n      method: 'DELETE',\n    });\n  },\n};\n\n// Dashboard metrics helper\nexport const getDashboardMetrics = async (): Promise<DashboardMetrics> => {\n  try {\n    // Get today's appointments\n    const today = new Date().toISOString().split('T')[0];\n    const todayAppointments = await appointmentsApi.getAll({\n      limit: 1000, // Get all to count\n    });\n    \n    // Filter for today's appointments\n    const todayCount = todayAppointments.docs.filter(apt => \n      apt.appointmentDate.startsWith(today)\n    ).length;\n\n    // Get recent patients (last 7 days)\n    const weekAgo = new Date();\n    weekAgo.setDate(weekAgo.getDate() - 7);\n    const allPatients = await patientsApi.getAll({ limit: 1000 });\n    const recentPatients = allPatients.docs.filter(patient => \n      new Date(patient.createdAt) >= weekAgo\n    ).length;\n\n    // Get all treatments\n    const allTreatments = await treatmentsApi.getAll({ limit: 1000 });\n\n    return {\n      todayAppointments: todayCount,\n      recentPatients,\n      totalPatients: allPatients.totalDocs,\n      activetreatments: allTreatments.totalDocs,\n    };\n  } catch (error) {\n    console.error('Failed to fetch dashboard metrics:', error);\n    // Return default values on error\n    return {\n      todayAppointments: 0,\n      recentPatients: 0,\n      totalPatients: 0,\n      activetreatments: 0,\n    };\n  }\n};\n"], "names": [], "mappings": "AAAA,2DAA2D;;;;;;;;AAe3D,yDAAyD;AACzD,eAAe,WAAc,QAAgB,EAAE,OAAqB;IAClE,MAAM,MAAM,CAAC,IAAI,EAAE,UAAU;IAE7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,gBAAgB;gBAChB,GAAG,SAAS,OAAO;YACrB;YACA,GAAG,OAAO;QACZ;QAEA,MAAM,eAAe,MAAM,SAAS,IAAI;QAExC,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,oCAAoC;YACpC,IAAI,aAAa,KAAK,EAAE;gBACtB,MAAM,IAAI,MAAM,aAAa,KAAK;YACpC;YACA,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QACjF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,IAAI,QAAQ,CAAC,EAAE;QAC/C,MAAM;IACR;AACF;AAGO,MAAM,UAAU;IACrB,UAAU;QACR,OAAO,WAAW,cAAc;YAAE,QAAQ;QAAO;IACnD;AACF;AAGO,MAAM,kBAAkB;IAC7B,QAAQ,OAAO;QACb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAElE,oCAAoC;QACpC,IAAI,QAAQ,OAAO;YACjB,2CAA2C;YAC3C,0FAA0F;YAC1F,IAAI,OAAO,KAAK,CAAC,OAAO,EAAE,QAAQ;gBAChC,aAAa,MAAM,CAAC,0BAA0B,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM;YAC3E;QACF;QAEA,MAAM,QAAQ,aAAa,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI,GAAG;QACxE,OAAO,WAAyC,CAAC,aAAa,EAAE,OAAO;IACzE;IAEA,SAAS,OAAO;QACd,OAAO,WAAwB,CAAC,cAAc,EAAE,IAAI;IACtD;IAEA,QAAQ,OAAO;QACb,OAAO,WAAwB,iBAAiB;YAC9C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO,IAAY;QACzB,OAAO,WAAwB,CAAC,cAAc,EAAE,IAAI,EAAE;YACpD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO;QACb,OAAO,WAAiB,CAAC,cAAc,EAAE,IAAI,EAAE;YAC7C,QAAQ;QACV;IACF;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,OAAO;QACb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAClE,IAAI,QAAQ,QAAQ,aAAa,MAAM,CAAC,oCAAoC,OAAO,MAAM;QAEzF,MAAM,QAAQ,aAAa,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI,GAAG;QACxE,OAAO,WAAqC,CAAC,SAAS,EAAE,OAAO;IACjE;IAEA,SAAS,OAAO;QACd,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI;IAC9C;IAEA,QAAQ,OAAO;QACb,OAAO,WAAoB,aAAa;YACtC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO,IAAY;QACzB,OAAO,WAAoB,CAAC,UAAU,EAAE,IAAI,EAAE;YAC5C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO;QACb,OAAO,WAAiB,CAAC,UAAU,EAAE,IAAI,EAAE;YACzC,QAAQ;QACV;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,QAAQ,OAAO;QACb,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,OAAO,aAAa,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACrE,IAAI,QAAQ,MAAM,aAAa,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAElE,MAAM,QAAQ,aAAa,QAAQ,KAAK,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI,GAAG;QACxE,OAAO,WAAuC,CAAC,WAAW,EAAE,OAAO;IACrE;IAEA,SAAS,OAAO;QACd,OAAO,WAAsB,CAAC,YAAY,EAAE,IAAI;IAClD;IAEA,QAAQ,OAAO;QACb,OAAO,WAAsB,eAAe;YAC1C,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO,IAAY;QACzB,OAAO,WAAsB,CAAC,YAAY,EAAE,IAAI,EAAE;YAChD,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,QAAQ,OAAO;QACb,OAAO,WAAiB,CAAC,YAAY,EAAE,IAAI,EAAE;YAC3C,QAAQ;QACV;IACF;AACF;AAGO,MAAM,sBAAsB;IACjC,IAAI;QACF,2BAA2B;QAC3B,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACpD,MAAM,oBAAoB,MAAM,gBAAgB,MAAM,CAAC;YACrD,OAAO;QACT;QAEA,kCAAkC;QAClC,MAAM,aAAa,kBAAkB,IAAI,CAAC,MAAM,CAAC,CAAA,MAC/C,IAAI,eAAe,CAAC,UAAU,CAAC,QAC/B,MAAM;QAER,oCAAoC;QACpC,MAAM,UAAU,IAAI;QACpB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;QACpC,MAAM,cAAc,MAAM,YAAY,MAAM,CAAC;YAAE,OAAO;QAAK;QAC3D,MAAM,iBAAiB,YAAY,IAAI,CAAC,MAAM,CAAC,CAAA,UAC7C,IAAI,KAAK,QAAQ,SAAS,KAAK,SAC/B,MAAM;QAER,qBAAqB;QACrB,MAAM,gBAAgB,MAAM,cAAc,MAAM,CAAC;YAAE,OAAO;QAAK;QAE/D,OAAO;YACL,mBAAmB;YACnB;YACA,eAAe,YAAY,SAAS;YACpC,kBAAkB,cAAc,SAAS;QAC3C;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,iCAAiC;QACjC,OAAO;YACL,mBAAmB;YACnB,gBAAgB;YAChB,eAAe;YACf,kBAAkB;QACpB;IACF;AACF", "debugId": null}}, {"offset": {"line": 319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-pagination.tsx"], "sourcesContent": ["import type { Table } from '@tanstack/react-table';\r\nimport { ChevronsLeft, ChevronsRight } from 'lucide-react';\r\n\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue\r\n} from '@/components/ui/select';\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons';\r\n\r\ninterface DataTablePaginationProps<TData> extends React.ComponentProps<'div'> {\r\n  table: Table<TData>;\r\n  pageSizeOptions?: number[];\r\n}\r\n\r\nexport function DataTablePagination<TData>({\r\n  table,\r\n  pageSizeOptions = [10, 20, 30, 40, 50],\r\n  className,\r\n  ...props\r\n}: DataTablePaginationProps<TData>) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'flex w-full flex-col-reverse items-center justify-between gap-4 overflow-auto p-1 sm:flex-row sm:gap-8',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className='text-muted-foreground flex-1 text-sm whitespace-nowrap'>\r\n        {table.getFilteredSelectedRowModel().rows.length > 0 ? (\r\n          <>\r\n            {table.getFilteredSelectedRowModel().rows.length} of{' '}\r\n            {table.getFilteredRowModel().rows.length} row(s) selected.\r\n          </>\r\n        ) : (\r\n          <>{table.getFilteredRowModel().rows.length} row(s) total.</>\r\n        )}\r\n      </div>\r\n      <div className='flex flex-col-reverse items-center gap-4 sm:flex-row sm:gap-6 lg:gap-8'>\r\n        <div className='flex items-center space-x-2'>\r\n          <p className='text-sm font-medium whitespace-nowrap'>Rows per page</p>\r\n          <Select\r\n            value={`${table.getState().pagination.pageSize}`}\r\n            onValueChange={(value) => {\r\n              table.setPageSize(Number(value));\r\n            }}\r\n          >\r\n            <SelectTrigger className='h-8 w-[4.5rem] [&[data-size]]:h-8'>\r\n              <SelectValue placeholder={table.getState().pagination.pageSize} />\r\n            </SelectTrigger>\r\n            <SelectContent side='top'>\r\n              {pageSizeOptions.map((pageSize) => (\r\n                <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                  {pageSize}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n        <div className='flex items-center justify-center text-sm font-medium'>\r\n          Page {table.getState().pagination.pageIndex + 1} of{' '}\r\n          {table.getPageCount()}\r\n        </div>\r\n        <div className='flex items-center space-x-2'>\r\n          <Button\r\n            aria-label='Go to first page'\r\n            variant='outline'\r\n            size='icon'\r\n            className='hidden size-8 lg:flex'\r\n            onClick={() => table.setPageIndex(0)}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <ChevronsLeft />\r\n          </Button>\r\n          <Button\r\n            aria-label='Go to previous page'\r\n            variant='outline'\r\n            size='icon'\r\n            className='size-8'\r\n            onClick={() => table.previousPage()}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <ChevronLeftIcon />\r\n          </Button>\r\n          <Button\r\n            aria-label='Go to next page'\r\n            variant='outline'\r\n            size='icon'\r\n            className='size-8'\r\n            onClick={() => table.nextPage()}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <ChevronRightIcon />\r\n          </Button>\r\n          <Button\r\n            aria-label='Go to last page'\r\n            variant='outline'\r\n            size='icon'\r\n            className='hidden size-8 lg:flex'\r\n            onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <ChevronsRight />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AAAA;AAEA;AACA;AAOA;AACA;;;;;;;AAOO,SAAS,oBAA2B,EACzC,KAAK,EACL,kBAAkB;IAAC;IAAI;IAAI;IAAI;IAAI;CAAG,EACtC,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;0BAET,4SAAC;gBAAI,WAAU;0BACZ,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,kBACjD;;wBACG,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;wBAAC;wBAAI;wBACpD,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;wBAAC;;iDAG3C;;wBAAG,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;wBAAC;;;;;;;;0BAG/C,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAE,WAAU;0CAAwC;;;;;;0CACrD,4SAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;gCAChD,eAAe,CAAC;oCACd,MAAM,WAAW,CAAC,OAAO;gCAC3B;;kDAEA,4SAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,4SAAC,qIAAA,CAAA,cAAW;4CAAC,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;kDAEhE,4SAAC,qIAAA,CAAA,gBAAa;wCAAC,MAAK;kDACjB,gBAAgB,GAAG,CAAC,CAAC,yBACpB,4SAAC,qIAAA,CAAA,aAAU;gDAAgB,OAAO,GAAG,UAAU;0DAC5C;+CADc;;;;;;;;;;;;;;;;;;;;;;kCAOzB,4SAAC;wBAAI,WAAU;;4BAAuD;4BAC9D,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;4BAAE;4BAAI;4BACnD,MAAM,YAAY;;;;;;;kCAErB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,qIAAA,CAAA,SAAM;gCACL,cAAW;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC;gCAClC,UAAU,CAAC,MAAM,kBAAkB;0CAEnC,cAAA,4SAAC,6SAAA,CAAA,eAAY;;;;;;;;;;0CAEf,4SAAC,qIAAA,CAAA,SAAM;gCACL,cAAW;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;0CAEnC,cAAA,4SAAC,qRAAA,CAAA,kBAAe;;;;;;;;;;0CAElB,4SAAC,qIAAA,CAAA,SAAM;gCACL,cAAW;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;0CAE/B,cAAA,4SAAC,qRAAA,CAAA,mBAAgB;;;;;;;;;;0CAEnB,4SAAC,qIAAA,CAAA,SAAM;gCACL,cAAW;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;gCACzD,UAAU,CAAC,MAAM,cAAc;0CAE/B,cAAA,4SAAC,+SAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;KA9FgB", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div\r\n      data-slot='table-container'\r\n      className='relative w-full overflow-x-auto'\r\n    >\r\n      <table\r\n        data-slot='table'\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return (\r\n    <thead\r\n      data-slot='table-header'\r\n      className={cn('[&_tr]:border-b', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot='table-body'\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot='table-footer'\r\n      className={cn(\r\n        'bg-muted/50 border-t font-medium [&>tr]:last:border-b-0',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot='table-row'\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot='table-head'\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot='table-cell'\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot='table-caption'\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,4SAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,4SAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 682, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/config/data-table.ts"], "sourcesContent": ["export type DataTableConfig = typeof dataTableConfig;\r\n\r\nexport const dataTableConfig = {\r\n  textOperators: [\r\n    { label: 'Contains', value: 'iLike' as const },\r\n    { label: 'Does not contain', value: 'notILike' as const },\r\n    { label: 'Is', value: 'eq' as const },\r\n    { label: 'Is not', value: 'ne' as const },\r\n    { label: 'Is empty', value: 'isEmpty' as const },\r\n    { label: 'Is not empty', value: 'isNotEmpty' as const }\r\n  ],\r\n  numericOperators: [\r\n    { label: 'Is', value: 'eq' as const },\r\n    { label: 'Is not', value: 'ne' as const },\r\n    { label: 'Is less than', value: 'lt' as const },\r\n    { label: 'Is less than or equal to', value: 'lte' as const },\r\n    { label: 'Is greater than', value: 'gt' as const },\r\n    { label: 'Is greater than or equal to', value: 'gte' as const },\r\n    { label: 'Is between', value: 'isBetween' as const },\r\n    { label: 'Is empty', value: 'isEmpty' as const },\r\n    { label: 'Is not empty', value: 'isNotEmpty' as const }\r\n  ],\r\n  dateOperators: [\r\n    { label: 'Is', value: 'eq' as const },\r\n    { label: 'Is not', value: 'ne' as const },\r\n    { label: 'Is before', value: 'lt' as const },\r\n    { label: 'Is after', value: 'gt' as const },\r\n    { label: 'Is on or before', value: 'lte' as const },\r\n    { label: 'Is on or after', value: 'gte' as const },\r\n    { label: 'Is between', value: 'isBetween' as const },\r\n    { label: 'Is relative to today', value: 'isRelativeToToday' as const },\r\n    { label: 'Is empty', value: 'isEmpty' as const },\r\n    { label: 'Is not empty', value: 'isNotEmpty' as const }\r\n  ],\r\n  selectOperators: [\r\n    { label: 'Is', value: 'eq' as const },\r\n    { label: 'Is not', value: 'ne' as const },\r\n    { label: 'Is empty', value: 'isEmpty' as const },\r\n    { label: 'Is not empty', value: 'isNotEmpty' as const }\r\n  ],\r\n  multiSelectOperators: [\r\n    { label: 'Has any of', value: 'inArray' as const },\r\n    { label: 'Has none of', value: 'notInArray' as const },\r\n    { label: 'Is empty', value: 'isEmpty' as const },\r\n    { label: 'Is not empty', value: 'isNotEmpty' as const }\r\n  ],\r\n  booleanOperators: [\r\n    { label: 'Is', value: 'eq' as const },\r\n    { label: 'Is not', value: 'ne' as const }\r\n  ],\r\n  sortOrders: [\r\n    { label: 'Asc', value: 'asc' as const },\r\n    { label: 'Desc', value: 'desc' as const }\r\n  ],\r\n  filterVariants: [\r\n    'text',\r\n    'number',\r\n    'range',\r\n    'date',\r\n    'dateRange',\r\n    'boolean',\r\n    'select',\r\n    'multiSelect'\r\n  ] as const,\r\n  operators: [\r\n    'iLike',\r\n    'notILike',\r\n    'eq',\r\n    'ne',\r\n    'inArray',\r\n    'notInArray',\r\n    'isEmpty',\r\n    'isNotEmpty',\r\n    'lt',\r\n    'lte',\r\n    'gt',\r\n    'gte',\r\n    'isBetween',\r\n    'isRelativeToToday'\r\n  ] as const,\r\n  joinOperators: ['and', 'or'] as const\r\n};\r\n"], "names": [], "mappings": ";;;AAEO,MAAM,kBAAkB;IAC7B,eAAe;QACb;YAAE,OAAO;YAAY,OAAO;QAAiB;QAC7C;YAAE,OAAO;YAAoB,OAAO;QAAoB;QACxD;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAU,OAAO;QAAc;QACxC;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAsB;KACvD;IACD,kBAAkB;QAChB;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAU,OAAO;QAAc;QACxC;YAAE,OAAO;YAAgB,OAAO;QAAc;QAC9C;YAAE,OAAO;YAA4B,OAAO;QAAe;QAC3D;YAAE,OAAO;YAAmB,OAAO;QAAc;QACjD;YAAE,OAAO;YAA+B,OAAO;QAAe;QAC9D;YAAE,OAAO;YAAc,OAAO;QAAqB;QACnD;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAsB;KACvD;IACD,eAAe;QACb;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAU,OAAO;QAAc;QACxC;YAAE,OAAO;YAAa,OAAO;QAAc;QAC3C;YAAE,OAAO;YAAY,OAAO;QAAc;QAC1C;YAAE,OAAO;YAAmB,OAAO;QAAe;QAClD;YAAE,OAAO;YAAkB,OAAO;QAAe;QACjD;YAAE,OAAO;YAAc,OAAO;QAAqB;QACnD;YAAE,OAAO;YAAwB,OAAO;QAA6B;QACrE;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAsB;KACvD;IACD,iBAAiB;QACf;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAU,OAAO;QAAc;QACxC;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAsB;KACvD;IACD,sBAAsB;QACpB;YAAE,OAAO;YAAc,OAAO;QAAmB;QACjD;YAAE,OAAO;YAAe,OAAO;QAAsB;QACrD;YAAE,OAAO;YAAY,OAAO;QAAmB;QAC/C;YAAE,OAAO;YAAgB,OAAO;QAAsB;KACvD;IACD,kBAAkB;QAChB;YAAE,OAAO;YAAM,OAAO;QAAc;QACpC;YAAE,OAAO;YAAU,OAAO;QAAc;KACzC;IACD,YAAY;QACV;YAAE,OAAO;YAAO,OAAO;QAAe;QACtC;YAAE,OAAO;YAAQ,OAAO;QAAgB;KACzC;IACD,gBAAgB;QACd;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,WAAW;QACT;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,eAAe;QAAC;QAAO;KAAK;AAC9B", "debugId": null}}, {"offset": {"line": 888, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/data-table.ts"], "sourcesContent": ["import type {\r\n  ExtendedColumnFilter,\r\n  FilterOperator,\r\n  FilterVariant\r\n} from '@/types/data-table';\r\nimport type { Column } from '@tanstack/react-table';\r\n\r\nimport { dataTableConfig } from '@/config/data-table';\r\n\r\nexport function getCommonPinningStyles<TData>({\r\n  column,\r\n  withBorder = false\r\n}: {\r\n  column: Column<TData>;\r\n  withBorder?: boolean;\r\n}): React.CSSProperties {\r\n  const isPinned = column.getIsPinned();\r\n  const isLastLeftPinnedColumn =\r\n    isPinned === 'left' && column.getIsLastColumn('left');\r\n  const isFirstRightPinnedColumn =\r\n    isPinned === 'right' && column.getIsFirstColumn('right');\r\n\r\n  return {\r\n    boxShadow: withBorder\r\n      ? isLastLeftPinnedColumn\r\n        ? '-4px 0 4px -4px hsl(var(--border)) inset'\r\n        : isFirstRightPinnedColumn\r\n          ? '4px 0 4px -4px hsl(var(--border)) inset'\r\n          : undefined\r\n      : undefined,\r\n    left: isPinned === 'left' ? `${column.getStart('left')}px` : undefined,\r\n    right: isPinned === 'right' ? `${column.getAfter('right')}px` : undefined,\r\n    opacity: isPinned ? 0.97 : 1,\r\n    position: isPinned ? 'sticky' : 'relative',\r\n    background: isPinned ? 'hsl(var(--background))' : 'hsl(var(--background))',\r\n    width: column.getSize(),\r\n    zIndex: isPinned ? 1 : 0\r\n  };\r\n}\r\n\r\nexport function getFilterOperators(filterVariant: FilterVariant) {\r\n  const operatorMap: Record<\r\n    FilterVariant,\r\n    { label: string; value: FilterOperator }[]\r\n  > = {\r\n    text: dataTableConfig.textOperators,\r\n    number: dataTableConfig.numericOperators,\r\n    range: dataTableConfig.numericOperators,\r\n    date: dataTableConfig.dateOperators,\r\n    dateRange: dataTableConfig.dateOperators,\r\n    boolean: dataTableConfig.booleanOperators,\r\n    select: dataTableConfig.selectOperators,\r\n    multiSelect: dataTableConfig.multiSelectOperators\r\n  };\r\n\r\n  return operatorMap[filterVariant] ?? dataTableConfig.textOperators;\r\n}\r\n\r\nexport function getDefaultFilterOperator(filterVariant: FilterVariant) {\r\n  const operators = getFilterOperators(filterVariant);\r\n\r\n  return operators[0]?.value ?? (filterVariant === 'text' ? 'iLike' : 'eq');\r\n}\r\n\r\nexport function getValidFilters<TData>(\r\n  filters: ExtendedColumnFilter<TData>[]\r\n): ExtendedColumnFilter<TData>[] {\r\n  return filters.filter(\r\n    (filter) =>\r\n      filter.operator === 'isEmpty' ||\r\n      filter.operator === 'isNotEmpty' ||\r\n      (Array.isArray(filter.value)\r\n        ? filter.value.length > 0\r\n        : filter.value !== '' &&\r\n          filter.value !== null &&\r\n          filter.value !== undefined)\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAOA;;AAEO,SAAS,uBAA8B,EAC5C,MAAM,EACN,aAAa,KAAK,EAInB;IACC,MAAM,WAAW,OAAO,WAAW;IACnC,MAAM,yBACJ,aAAa,UAAU,OAAO,eAAe,CAAC;IAChD,MAAM,2BACJ,aAAa,WAAW,OAAO,gBAAgB,CAAC;IAElD,OAAO;QACL,WAAW,aACP,yBACE,6CACA,2BACE,4CACA,YACJ;QACJ,MAAM,aAAa,SAAS,GAAG,OAAO,QAAQ,CAAC,QAAQ,EAAE,CAAC,GAAG;QAC7D,OAAO,aAAa,UAAU,GAAG,OAAO,QAAQ,CAAC,SAAS,EAAE,CAAC,GAAG;QAChE,SAAS,WAAW,OAAO;QAC3B,UAAU,WAAW,WAAW;QAChC,YAAY,WAAW,2BAA2B;QAClD,OAAO,OAAO,OAAO;QACrB,QAAQ,WAAW,IAAI;IACzB;AACF;AAEO,SAAS,mBAAmB,aAA4B;IAC7D,MAAM,cAGF;QACF,MAAM,iIAAA,CAAA,kBAAe,CAAC,aAAa;QACnC,QAAQ,iIAAA,CAAA,kBAAe,CAAC,gBAAgB;QACxC,OAAO,iIAAA,CAAA,kBAAe,CAAC,gBAAgB;QACvC,MAAM,iIAAA,CAAA,kBAAe,CAAC,aAAa;QACnC,WAAW,iIAAA,CAAA,kBAAe,CAAC,aAAa;QACxC,SAAS,iIAAA,CAAA,kBAAe,CAAC,gBAAgB;QACzC,QAAQ,iIAAA,CAAA,kBAAe,CAAC,eAAe;QACvC,aAAa,iIAAA,CAAA,kBAAe,CAAC,oBAAoB;IACnD;IAEA,OAAO,WAAW,CAAC,cAAc,IAAI,iIAAA,CAAA,kBAAe,CAAC,aAAa;AACpE;AAEO,SAAS,yBAAyB,aAA4B;IACnE,MAAM,YAAY,mBAAmB;IAErC,OAAO,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,kBAAkB,SAAS,UAAU,IAAI;AAC1E;AAEO,SAAS,gBACd,OAAsC;IAEtC,OAAO,QAAQ,MAAM,CACnB,CAAC,SACC,OAAO,QAAQ,KAAK,aACpB,OAAO,QAAQ,KAAK,gBACpB,CAAC,MAAM,OAAO,CAAC,OAAO,KAAK,IACvB,OAAO,KAAK,CAAC,MAAM,GAAG,IACtB,OAAO,KAAK,KAAK,MACjB,OAAO,KAAK,KAAK,QACjB,OAAO,KAAK,KAAK,SAAS;AAEpC", "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table.tsx"], "sourcesContent": ["import { type Table as TanstackTable, flexRender } from '@tanstack/react-table';\r\nimport type * as React from 'react';\r\n\r\nimport { DataTablePagination } from '@/components/ui/table/data-table-pagination';\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow\r\n} from '@/components/ui/table';\r\nimport { getCommonPinningStyles } from '@/lib/data-table';\r\nimport { ScrollArea, ScrollBar } from '@/components/ui/scroll-area';\r\n\r\ninterface DataTableProps<TData> extends React.ComponentProps<'div'> {\r\n  table: TanstackTable<TData>;\r\n  actionBar?: React.ReactNode;\r\n}\r\n\r\nexport function DataTable<TData>({\r\n  table,\r\n  actionBar,\r\n  children\r\n}: DataTableProps<TData>) {\r\n  return (\r\n    <div className='flex flex-1 flex-col space-y-4'>\r\n      {children}\r\n      <div className='relative flex flex-1'>\r\n        <div className='absolute inset-0 flex overflow-hidden rounded-lg border'>\r\n          <ScrollArea className='h-full w-full'>\r\n            <Table>\r\n              <TableHeader className='bg-muted sticky top-0 z-10'>\r\n                {table.getHeaderGroups().map((headerGroup) => (\r\n                  <TableRow key={headerGroup.id}>\r\n                    {headerGroup.headers.map((header) => (\r\n                      <TableHead\r\n                        key={header.id}\r\n                        colSpan={header.colSpan}\r\n                        style={{\r\n                          ...getCommonPinningStyles({ column: header.column })\r\n                        }}\r\n                      >\r\n                        {header.isPlaceholder\r\n                          ? null\r\n                          : flexRender(\r\n                              header.column.columnDef.header,\r\n                              header.getContext()\r\n                            )}\r\n                      </TableHead>\r\n                    ))}\r\n                  </TableRow>\r\n                ))}\r\n              </TableHeader>\r\n              <TableBody>\r\n                {table.getRowModel().rows?.length ? (\r\n                  table.getRowModel().rows.map((row) => (\r\n                    <TableRow\r\n                      key={row.id}\r\n                      data-state={row.getIsSelected() && 'selected'}\r\n                    >\r\n                      {row.getVisibleCells().map((cell) => (\r\n                        <TableCell\r\n                          key={cell.id}\r\n                          style={{\r\n                            ...getCommonPinningStyles({ column: cell.column })\r\n                          }}\r\n                        >\r\n                          {flexRender(\r\n                            cell.column.columnDef.cell,\r\n                            cell.getContext()\r\n                          )}\r\n                        </TableCell>\r\n                      ))}\r\n                    </TableRow>\r\n                  ))\r\n                ) : (\r\n                  <TableRow>\r\n                    <TableCell\r\n                      colSpan={table.getAllColumns().length}\r\n                      className='h-24 text-center'\r\n                    >\r\n                      No results.\r\n                    </TableCell>\r\n                  </TableRow>\r\n                )}\r\n              </TableBody>\r\n            </Table>\r\n            <ScrollBar orientation='horizontal' />\r\n          </ScrollArea>\r\n        </div>\r\n      </div>\r\n      <div className='flex flex-col gap-2.5'>\r\n        <DataTablePagination table={table} />\r\n        {actionBar &&\r\n          table.getFilteredSelectedRowModel().rows.length > 0 &&\r\n          actionBar}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAGA;AACA;AAQA;AACA;;;;;;;AAOO,SAAS,UAAiB,EAC/B,KAAK,EACL,SAAS,EACT,QAAQ,EACc;IACtB,qBACE,4SAAC;QAAI,WAAU;;YACZ;0BACD,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,6IAAA,CAAA,aAAU;wBAAC,WAAU;;0CACpB,4SAAC,oIAAA,CAAA,QAAK;;kDACJ,4SAAC,oIAAA,CAAA,cAAW;wCAAC,WAAU;kDACpB,MAAM,eAAe,GAAG,GAAG,CAAC,CAAC,4BAC5B,4SAAC,oIAAA,CAAA,WAAQ;0DACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,uBACxB,4SAAC,oIAAA,CAAA,YAAS;wDAER,SAAS,OAAO,OAAO;wDACvB,OAAO;4DACL,GAAG,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;gEAAE,QAAQ,OAAO,MAAM;4DAAC,EAAE;wDACtD;kEAEC,OAAO,aAAa,GACjB,OACA,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACP,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;uDAVlB,OAAO,EAAE;;;;;+CAHL,YAAY,EAAE;;;;;;;;;;kDAoBjC,4SAAC,oIAAA,CAAA,YAAS;kDACP,MAAM,WAAW,GAAG,IAAI,EAAE,SACzB,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,oBAC5B,4SAAC,oIAAA,CAAA,WAAQ;gDAEP,cAAY,IAAI,aAAa,MAAM;0DAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAC,qBAC1B,4SAAC,oIAAA,CAAA,YAAS;wDAER,OAAO;4DACL,GAAG,CAAA,GAAA,8HAAA,CAAA,yBAAsB,AAAD,EAAE;gEAAE,QAAQ,KAAK,MAAM;4DAAC,EAAE;wDACpD;kEAEC,CAAA,GAAA,mSAAA,CAAA,aAAU,AAAD,EACR,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;uDAPZ,KAAK,EAAE;;;;;+CALX,IAAI,EAAE;;;;sEAmBf,4SAAC,oIAAA,CAAA,WAAQ;sDACP,cAAA,4SAAC,oIAAA,CAAA,YAAS;gDACR,SAAS,MAAM,aAAa,GAAG,MAAM;gDACrC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;0CAOT,4SAAC,6IAAA,CAAA,YAAS;gCAAC,aAAY;;;;;;;;;;;;;;;;;;;;;;0BAI7B,4SAAC;gBAAI,WAAU;;kCACb,4SAAC,mKAAA,CAAA,sBAAmB;wBAAC,OAAO;;;;;;oBAC3B,aACC,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM,GAAG,KAClD;;;;;;;;;;;;;AAIV;KAhFgB", "debugId": null}}, {"offset": {"line": 1099, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/calendar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { DayPicker } from 'react-day-picker';\r\nimport type { ComponentProps } from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { buttonVariants } from '@/components/ui/button';\r\nimport { ChevronLeftIcon, ChevronRightIcon } from '@radix-ui/react-icons';\r\n\r\n// Custom icons that meet the DayPicker requirements\r\nconst LeftIcon = () => <ChevronLeftIcon className='size-4' />;\r\nconst RightIcon = () => <ChevronRightIcon className='size-4' />;\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: ComponentProps<typeof DayPicker>) {\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn('p-3', className)}\r\n      classNames={{\r\n        months: 'flex flex-col sm:flex-row gap-2',\r\n        month: 'flex flex-col gap-4',\r\n        caption: 'flex justify-center pt-1 relative items-center w-full',\r\n        caption_label: 'text-sm font-medium',\r\n        nav: 'flex items-center gap-1',\r\n        nav_button: cn(\r\n          buttonVariants({ variant: 'outline' }),\r\n          'size-7 bg-transparent p-0 opacity-50 hover:opacity-100'\r\n        ),\r\n        nav_button_previous: 'absolute left-1',\r\n        nav_button_next: 'absolute right-1',\r\n        table: 'w-full border-collapse space-x-1',\r\n        head_row: 'flex',\r\n        head_cell:\r\n          'text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]',\r\n        row: 'flex w-full mt-2',\r\n        cell: cn(\r\n          'relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md',\r\n          props.mode === 'range'\r\n            ? '[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md'\r\n            : '[&:has([aria-selected])]:rounded-md'\r\n        ),\r\n        day: cn(\r\n          buttonVariants({ variant: 'ghost' }),\r\n          'size-8 p-0 font-normal aria-selected:opacity-100'\r\n        ),\r\n        day_range_start:\r\n          'day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground',\r\n        day_range_end:\r\n          'day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground',\r\n        day_selected:\r\n          'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',\r\n        day_today: 'bg-accent text-accent-foreground',\r\n        day_outside:\r\n          'day-outside text-muted-foreground aria-selected:text-muted-foreground',\r\n        day_disabled: 'text-muted-foreground opacity-50',\r\n        day_range_middle:\r\n          'aria-selected:bg-accent aria-selected:text-accent-foreground',\r\n        day_hidden: 'invisible',\r\n        ...classNames\r\n      }}\r\n      components={{\r\n        IconLeft: LeftIcon,\r\n        IconRight: RightIcon\r\n      }}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Calendar };\r\n"], "names": [], "mappings": ";;;;AAGA;AAGA;AACA;AACA;AARA;;;;;;AAUA,oDAAoD;AACpD,MAAM,WAAW,kBAAM,4SAAC,qRAAA,CAAA,kBAAe;QAAC,WAAU;;;;;;KAA5C;AACN,MAAM,YAAY,kBAAM,4SAAC,qRAAA,CAAA,mBAAgB;QAAC,WAAU;;;;;;MAA9C;AAEN,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OAC8B;IACjC,qBACE,4SAAC,sRAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YACL,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACL,mKACA,MAAM,IAAI,KAAK,UACX,yKACA;YAEN,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,iBACE;YACF,eACE;YACF,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,UAAU;YACV,WAAW;QACb;QACC,GAAG,KAAK;;;;;;AAGf;MA3DS", "debugId": null}}, {"offset": {"line": 1189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/popover.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Popover({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\r\n  return <PopoverPrimitive.Root data-slot='popover' {...props} />;\r\n}\r\n\r\nfunction PopoverTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\r\n  return <PopoverPrimitive.Trigger data-slot='popover-trigger' {...props} />;\r\n}\r\n\r\nfunction PopoverContent({\r\n  className,\r\n  align = 'center',\r\n  sideOffset = 4,\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\r\n  return (\r\n    <PopoverPrimitive.Portal>\r\n      <PopoverPrimitive.Content\r\n        data-slot='popover-content'\r\n        align={align}\r\n        sideOffset={sideOffset}\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </PopoverPrimitive.Portal>\r\n  );\r\n}\r\n\r\nfunction PopoverAnchor({\r\n  ...props\r\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\r\n  return <PopoverPrimitive.Anchor data-slot='popover-anchor' {...props} />;\r\n}\r\n\r\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,4SAAC,gRAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,4SAAC,gRAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,4SAAC,gRAAA,CAAA,SAAuB;kBACtB,cAAA,4SAAC,gRAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,4SAAC,gRAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/format.ts"], "sourcesContent": ["export function formatDate(\r\n  date: Date | string | number | undefined,\r\n  opts: Intl.DateTimeFormatOptions = {}\r\n) {\r\n  if (!date) return '';\r\n\r\n  try {\r\n    return new Intl.DateTimeFormat('en-US', {\r\n      month: opts.month ?? 'long',\r\n      day: opts.day ?? 'numeric',\r\n      year: opts.year ?? 'numeric',\r\n      ...opts\r\n    }).format(new Date(date));\r\n  } catch (_err) {\r\n    return '';\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,SAAS,WACd,IAAwC,EACxC,OAAmC,CAAC,CAAC;IAErC,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;YACtC,OAAO,KAAK,KAAK,IAAI;YACrB,KAAK,KAAK,GAAG,IAAI;YACjB,MAAM,KAAK,IAAI,IAAI;YACnB,GAAG,IAAI;QACT,GAAG,MAAM,CAAC,IAAI,KAAK;IACrB,EAAE,OAAO,MAAM;QACb,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 1295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-date-filter.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Column } from '@tanstack/react-table';\r\nimport { CalendarIcon, XCircle } from 'lucide-react';\r\nimport * as React from 'react';\r\nimport type { DateRange } from 'react-day-picker';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { formatDate } from '@/lib/format';\r\n\r\ntype DateSelection = Date[] | DateRange;\r\n\r\nfunction getIsDateRange(value: DateSelection): value is DateRange {\r\n  return value && typeof value === 'object' && !Array.isArray(value);\r\n}\r\n\r\nfunction parseAsDate(timestamp: number | string | undefined): Date | undefined {\r\n  if (!timestamp) return undefined;\r\n  const numericTimestamp =\r\n    typeof timestamp === 'string' ? Number(timestamp) : timestamp;\r\n  const date = new Date(numericTimestamp);\r\n  return !Number.isNaN(date.getTime()) ? date : undefined;\r\n}\r\n\r\nfunction parseColumnFilterValue(value: unknown) {\r\n  if (value === null || value === undefined) {\r\n    return [];\r\n  }\r\n\r\n  if (Array.isArray(value)) {\r\n    return value.map((item) => {\r\n      if (typeof item === 'number' || typeof item === 'string') {\r\n        return item;\r\n      }\r\n      return undefined;\r\n    });\r\n  }\r\n\r\n  if (typeof value === 'string' || typeof value === 'number') {\r\n    return [value];\r\n  }\r\n\r\n  return [];\r\n}\r\n\r\ninterface DataTableDateFilterProps<TData> {\r\n  column: Column<TData, unknown>;\r\n  title?: string;\r\n  multiple?: boolean;\r\n}\r\n\r\nexport function DataTableDateFilter<TData>({\r\n  column,\r\n  title,\r\n  multiple\r\n}: DataTableDateFilterProps<TData>) {\r\n  const columnFilterValue = column.getFilterValue();\r\n\r\n  const selectedDates = React.useMemo<DateSelection>(() => {\r\n    if (!columnFilterValue) {\r\n      return multiple ? { from: undefined, to: undefined } : [];\r\n    }\r\n\r\n    if (multiple) {\r\n      const timestamps = parseColumnFilterValue(columnFilterValue);\r\n      return {\r\n        from: parseAsDate(timestamps[0]),\r\n        to: parseAsDate(timestamps[1])\r\n      };\r\n    }\r\n\r\n    const timestamps = parseColumnFilterValue(columnFilterValue);\r\n    const date = parseAsDate(timestamps[0]);\r\n    return date ? [date] : [];\r\n  }, [columnFilterValue, multiple]);\r\n\r\n  const onSelect = React.useCallback(\r\n    (date: Date | DateRange | undefined) => {\r\n      if (!date) {\r\n        column.setFilterValue(undefined);\r\n        return;\r\n      }\r\n\r\n      if (multiple && !('getTime' in date)) {\r\n        const from = date.from?.getTime();\r\n        const to = date.to?.getTime();\r\n        column.setFilterValue(from || to ? [from, to] : undefined);\r\n      } else if (!multiple && 'getTime' in date) {\r\n        column.setFilterValue(date.getTime());\r\n      }\r\n    },\r\n    [column, multiple]\r\n  );\r\n\r\n  const onReset = React.useCallback(\r\n    (event: React.MouseEvent) => {\r\n      event.stopPropagation();\r\n      column.setFilterValue(undefined);\r\n    },\r\n    [column]\r\n  );\r\n\r\n  const hasValue = React.useMemo(() => {\r\n    if (multiple) {\r\n      if (!getIsDateRange(selectedDates)) return false;\r\n      return selectedDates.from || selectedDates.to;\r\n    }\r\n    if (!Array.isArray(selectedDates)) return false;\r\n    return selectedDates.length > 0;\r\n  }, [multiple, selectedDates]);\r\n\r\n  const formatDateRange = React.useCallback((range: DateRange) => {\r\n    if (!range.from && !range.to) return '';\r\n    if (range.from && range.to) {\r\n      return `${formatDate(range.from)} - ${formatDate(range.to)}`;\r\n    }\r\n    return formatDate(range.from ?? range.to);\r\n  }, []);\r\n\r\n  const label = React.useMemo(() => {\r\n    if (multiple) {\r\n      if (!getIsDateRange(selectedDates)) return null;\r\n\r\n      const hasSelectedDates = selectedDates.from || selectedDates.to;\r\n      const dateText = hasSelectedDates\r\n        ? formatDateRange(selectedDates)\r\n        : 'Select date range';\r\n\r\n      return (\r\n        <span className='flex items-center gap-2'>\r\n          <span>{title}</span>\r\n          {hasSelectedDates && (\r\n            <>\r\n              <Separator\r\n                orientation='vertical'\r\n                className='mx-0.5 data-[orientation=vertical]:h-4'\r\n              />\r\n              <span>{dateText}</span>\r\n            </>\r\n          )}\r\n        </span>\r\n      );\r\n    }\r\n\r\n    if (getIsDateRange(selectedDates)) return null;\r\n\r\n    const hasSelectedDate = selectedDates.length > 0;\r\n    const dateText = hasSelectedDate\r\n      ? formatDate(selectedDates[0])\r\n      : 'Select date';\r\n\r\n    return (\r\n      <span className='flex items-center gap-2'>\r\n        <span>{title}</span>\r\n        {hasSelectedDate && (\r\n          <>\r\n            <Separator\r\n              orientation='vertical'\r\n              className='mx-0.5 data-[orientation=vertical]:h-4'\r\n            />\r\n            <span>{dateText}</span>\r\n          </>\r\n        )}\r\n      </span>\r\n    );\r\n  }, [selectedDates, multiple, formatDateRange, title]);\r\n\r\n  return (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant='outline' size='sm' className='border-dashed'>\r\n          {hasValue ? (\r\n            <div\r\n              role='button'\r\n              aria-label={`Clear ${title} filter`}\r\n              tabIndex={0}\r\n              onClick={onReset}\r\n              className='focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none'\r\n            >\r\n              <XCircle />\r\n            </div>\r\n          ) : (\r\n            <CalendarIcon />\r\n          )}\r\n          {label}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className='w-auto p-0' align='start'>\r\n        {multiple ? (\r\n          <Calendar\r\n            initialFocus\r\n            mode='range'\r\n            selected={\r\n              getIsDateRange(selectedDates)\r\n                ? selectedDates\r\n                : { from: undefined, to: undefined }\r\n            }\r\n            onSelect={onSelect}\r\n          />\r\n        ) : (\r\n          <Calendar\r\n            initialFocus\r\n            mode='single'\r\n            selected={\r\n              !getIsDateRange(selectedDates) ? selectedDates[0] : undefined\r\n            }\r\n            onSelect={onSelect}\r\n          />\r\n        )}\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAGA;AACA;AACA;AAKA;AACA;;;AAfA;;;;;;;;AAmBA,SAAS,eAAe,KAAoB;IAC1C,OAAO,SAAS,OAAO,UAAU,YAAY,CAAC,MAAM,OAAO,CAAC;AAC9D;AAEA,SAAS,YAAY,SAAsC;IACzD,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,mBACJ,OAAO,cAAc,WAAW,OAAO,aAAa;IACtD,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,CAAC,OAAO,KAAK,CAAC,KAAK,OAAO,MAAM,OAAO;AAChD;AAEA,SAAS,uBAAuB,KAAc;IAC5C,IAAI,UAAU,QAAQ,UAAU,WAAW;QACzC,OAAO,EAAE;IACX;IAEA,IAAI,MAAM,OAAO,CAAC,QAAQ;QACxB,OAAO,MAAM,GAAG,CAAC,CAAC;YAChB,IAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU;gBACxD,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;QAC1D,OAAO;YAAC;SAAM;IAChB;IAEA,OAAO,EAAE;AACX;AAQO,SAAS,oBAA2B,EACzC,MAAM,EACN,KAAK,EACL,QAAQ,EACwB;;IAChC,MAAM,oBAAoB,OAAO,cAAc;IAE/C,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;sDAAiB;YACjD,IAAI,CAAC,mBAAmB;gBACtB,OAAO,WAAW;oBAAE,MAAM;oBAAW,IAAI;gBAAU,IAAI,EAAE;YAC3D;YAEA,IAAI,UAAU;gBACZ,MAAM,aAAa,uBAAuB;gBAC1C,OAAO;oBACL,MAAM,YAAY,UAAU,CAAC,EAAE;oBAC/B,IAAI,YAAY,UAAU,CAAC,EAAE;gBAC/B;YACF;YAEA,MAAM,aAAa,uBAAuB;YAC1C,MAAM,OAAO,YAAY,UAAU,CAAC,EAAE;YACtC,OAAO,OAAO;gBAAC;aAAK,GAAG,EAAE;QAC3B;qDAAG;QAAC;QAAmB;KAAS;IAEhC,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;qDAC/B,CAAC;YACC,IAAI,CAAC,MAAM;gBACT,OAAO,cAAc,CAAC;gBACtB;YACF;YAEA,IAAI,YAAY,CAAC,CAAC,aAAa,IAAI,GAAG;gBACpC,MAAM,OAAO,KAAK,IAAI,EAAE;gBACxB,MAAM,KAAK,KAAK,EAAE,EAAE;gBACpB,OAAO,cAAc,CAAC,QAAQ,KAAK;oBAAC;oBAAM;iBAAG,GAAG;YAClD,OAAO,IAAI,CAAC,YAAY,aAAa,MAAM;gBACzC,OAAO,cAAc,CAAC,KAAK,OAAO;YACpC;QACF;oDACA;QAAC;QAAQ;KAAS;IAGpB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;oDAC9B,CAAC;YACC,MAAM,eAAe;YACrB,OAAO,cAAc,CAAC;QACxB;mDACA;QAAC;KAAO;IAGV,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;iDAAE;YAC7B,IAAI,UAAU;gBACZ,IAAI,CAAC,eAAe,gBAAgB,OAAO;gBAC3C,OAAO,cAAc,IAAI,IAAI,cAAc,EAAE;YAC/C;YACA,IAAI,CAAC,MAAM,OAAO,CAAC,gBAAgB,OAAO;YAC1C,OAAO,cAAc,MAAM,GAAG;QAChC;gDAAG;QAAC;QAAU;KAAc;IAE5B,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;4DAAE,CAAC;YACzC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,OAAO;YACrC,IAAI,MAAM,IAAI,IAAI,MAAM,EAAE,EAAE;gBAC1B,OAAO,GAAG,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI,EAAE,GAAG,EAAE,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,EAAE,GAAG;YAC9D;YACA,OAAO,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI,IAAI,MAAM,EAAE;QAC1C;2DAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;8CAAE;YAC1B,IAAI,UAAU;gBACZ,IAAI,CAAC,eAAe,gBAAgB,OAAO;gBAE3C,MAAM,mBAAmB,cAAc,IAAI,IAAI,cAAc,EAAE;gBAC/D,MAAM,WAAW,mBACb,gBAAgB,iBAChB;gBAEJ,qBACE,4SAAC;oBAAK,WAAU;;sCACd,4SAAC;sCAAM;;;;;;wBACN,kCACC;;8CACE,4SAAC,wIAAA,CAAA,YAAS;oCACR,aAAY;oCACZ,WAAU;;;;;;8CAEZ,4SAAC;8CAAM;;;;;;;;;;;;;;YAKjB;YAEA,IAAI,eAAe,gBAAgB,OAAO;YAE1C,MAAM,kBAAkB,cAAc,MAAM,GAAG;YAC/C,MAAM,WAAW,kBACb,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,CAAC,EAAE,IAC3B;YAEJ,qBACE,4SAAC;gBAAK,WAAU;;kCACd,4SAAC;kCAAM;;;;;;oBACN,iCACC;;0CACE,4SAAC,wIAAA,CAAA,YAAS;gCACR,aAAY;gCACZ,WAAU;;;;;;0CAEZ,4SAAC;0CAAM;;;;;;;;;;;;;;QAKjB;6CAAG;QAAC;QAAe;QAAU;QAAiB;KAAM;IAEpD,qBACE,4SAAC,sIAAA,CAAA,UAAO;;0BACN,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,WAAU;;wBAC3C,yBACC,4SAAC;4BACC,MAAK;4BACL,cAAY,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC;4BACnC,UAAU;4BACV,SAAS;4BACT,WAAU;sCAEV,cAAA,4SAAC,mSAAA,CAAA,UAAO;;;;;;;;;iDAGV,4SAAC,qSAAA,CAAA,eAAY;;;;;wBAEd;;;;;;;;;;;;0BAGL,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAa,OAAM;0BAC1C,yBACC,4SAAC,uIAAA,CAAA,WAAQ;oBACP,YAAY;oBACZ,MAAK;oBACL,UACE,eAAe,iBACX,gBACA;wBAAE,MAAM;wBAAW,IAAI;oBAAU;oBAEvC,UAAU;;;;;yCAGZ,4SAAC,uIAAA,CAAA,WAAQ;oBACP,YAAY;oBACZ,MAAK;oBACL,UACE,CAAC,eAAe,iBAAiB,aAAa,CAAC,EAAE,GAAG;oBAEtD,UAAU;;;;;;;;;;;;;;;;;AAMtB;GAjKgB;KAAA", "debugId": null}}, {"offset": {"line": 1602, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\r\nimport { XIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Dialog({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\r\n  return <DialogPrimitive.Root data-slot='dialog' {...props} />;\r\n}\r\n\r\nfunction DialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\r\n  return <DialogPrimitive.Trigger data-slot='dialog-trigger' {...props} />;\r\n}\r\n\r\nfunction DialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\r\n  return <DialogPrimitive.Portal data-slot='dialog-portal' {...props} />;\r\n}\r\n\r\nfunction DialogClose({\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\r\n  return <DialogPrimitive.Close data-slot='dialog-close' {...props} />;\r\n}\r\n\r\nfunction DialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\r\n  return (\r\n    <DialogPrimitive.Overlay\r\n      data-slot='dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogContent({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Content>) {\r\n  return (\r\n    <DialogPortal data-slot='dialog-portal'>\r\n      <DialogOverlay />\r\n      <DialogPrimitive.Content\r\n        data-slot='dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      >\r\n        {children}\r\n        <DialogPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\">\r\n          <XIcon />\r\n          <span className='sr-only'>Close</span>\r\n        </DialogPrimitive.Close>\r\n      </DialogPrimitive.Content>\r\n    </DialogPortal>\r\n  );\r\n}\r\n\r\nfunction DialogHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\r\n  return (\r\n    <DialogPrimitive.Title\r\n      data-slot='dialog-title'\r\n      className={cn('text-lg leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction DialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\r\n  return (\r\n    <DialogPrimitive.Description\r\n      data-slot='dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Dialog,\r\n  DialogClose,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogOverlay,\r\n  DialogPortal,\r\n  DialogTitle,\r\n  DialogTrigger\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,4SAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,4SAAC,kRAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS;AAMT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,4SAAC,kRAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,4SAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,4SAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,GAAG,OACkD;IACrD,qBACE,4SAAC;QAAa,aAAU;;0BACtB,4SAAC;;;;;0BACD,4SAAC,kRAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;kCACD,4SAAC,kRAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,4SAAC,uRAAA,CAAA,QAAK;;;;;0CACN,4SAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;MAxBS;AA0BT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,4SAAC,kRAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/command.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Command as CommandPrimitive } from 'cmdk';\r\nimport { SearchIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle\r\n} from '@/components/ui/dialog';\r\n\r\nfunction Command({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive>) {\r\n  return (\r\n    <CommandPrimitive\r\n      data-slot='command'\r\n      className={cn(\r\n        'bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandDialog({\r\n  title = 'Command Palette',\r\n  description = 'Search for a command to run...',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof Dialog> & {\r\n  title?: string;\r\n  description?: string;\r\n}) {\r\n  return (\r\n    <Dialog {...props}>\r\n      <DialogHeader className='sr-only'>\r\n        <DialogTitle>{title}</DialogTitle>\r\n        <DialogDescription>{description}</DialogDescription>\r\n      </DialogHeader>\r\n      <DialogContent className='overflow-hidden p-0'>\r\n        <Command className='[&_[cmdk-group-heading]]:text-muted-foreground **:data-[slot=command-input-wrapper]:h-12 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group]]:px-2 [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5'>\r\n          {children}\r\n        </Command>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}\r\n\r\nfunction CommandInput({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Input>) {\r\n  return (\r\n    <div\r\n      data-slot='command-input-wrapper'\r\n      className='flex h-9 items-center gap-2 border-b px-3'\r\n    >\r\n      <SearchIcon className='size-4 shrink-0 opacity-50' />\r\n      <CommandPrimitive.Input\r\n        data-slot='command-input'\r\n        className={cn(\r\n          'placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction CommandList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.List>) {\r\n  return (\r\n    <CommandPrimitive.List\r\n      data-slot='command-list'\r\n      className={cn(\r\n        'max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandEmpty({\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Empty>) {\r\n  return (\r\n    <CommandPrimitive.Empty\r\n      data-slot='command-empty'\r\n      className='py-6 text-center text-sm'\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandGroup({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Group>) {\r\n  return (\r\n    <CommandPrimitive.Group\r\n      data-slot='command-group'\r\n      className={cn(\r\n        'text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Separator>) {\r\n  return (\r\n    <CommandPrimitive.Separator\r\n      data-slot='command-separator'\r\n      className={cn('bg-border -mx-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandItem({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CommandPrimitive.Item>) {\r\n  return (\r\n    <CommandPrimitive.Item\r\n      data-slot='command-item'\r\n      className={cn(\r\n        \"data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CommandShortcut({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot='command-shortcut'\r\n      className={cn(\r\n        'text-muted-foreground ml-auto text-xs tracking-widest',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Command,\r\n  CommandDialog,\r\n  CommandInput,\r\n  CommandList,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandItem,\r\n  CommandShortcut,\r\n  CommandSeparator\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGA;AACA;AAEA;AACA;AAPA;;;;;;AAeA,SAAS,QAAQ,EACf,SAAS,EACT,GAAG,OAC2C;IAC9C,qBACE,4SAAC,qPAAA,CAAA,UAAgB;QACf,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,cAAc,EACrB,QAAQ,iBAAiB,EACzB,cAAc,gCAAgC,EAC9C,QAAQ,EACR,GAAG,OAIJ;IACC,qBACE,4SAAC,qIAAA,CAAA,SAAM;QAAE,GAAG,KAAK;;0BACf,4SAAC,qIAAA,CAAA,eAAY;gBAAC,WAAU;;kCACtB,4SAAC,qIAAA,CAAA,cAAW;kCAAE;;;;;;kCACd,4SAAC,qIAAA,CAAA,oBAAiB;kCAAE;;;;;;;;;;;;0BAEtB,4SAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;0BACvB,cAAA,4SAAC;oBAAQ,WAAU;8BAChB;;;;;;;;;;;;;;;;;AAKX;MAtBS;AAwBT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,4SAAC;QACC,aAAU;QACV,WAAU;;0BAEV,4SAAC,iSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,4SAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4JACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBACE,4SAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,4SAAC,qPAAA,CAAA,UAAgB,CAAC,KAAK;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0NACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,4SAAC,qPAAA,CAAA,UAAgB,CAAC,SAAS;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wBAAwB;QACrC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,qPAAA,CAAA,UAAgB,CAAC,IAAI;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-faceted-filter.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Option } from '@/types/data-table';\r\nimport type { Column } from '@tanstack/react-table';\r\nimport { PlusCircle, XCircle } from 'lucide-react';\r\n\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList,\r\n  CommandSeparator\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { cn } from '@/lib/utils';\r\nimport * as React from 'react';\r\nimport { CheckIcon } from '@radix-ui/react-icons';\r\n\r\ninterface DataTableFacetedFilterProps<TData, TValue> {\r\n  column?: Column<TData, TValue>;\r\n  title?: string;\r\n  options: Option[];\r\n  multiple?: boolean;\r\n}\r\n\r\nexport function DataTableFacetedFilter<TData, TValue>({\r\n  column,\r\n  title,\r\n  options,\r\n  multiple\r\n}: DataTableFacetedFilterProps<TData, TValue>) {\r\n  const [open, setOpen] = React.useState(false);\r\n\r\n  const columnFilterValue = column?.getFilterValue();\r\n  const selectedValues = React.useMemo(\r\n    () => new Set(Array.isArray(columnFilterValue) ? columnFilterValue : []),\r\n    [columnFilterValue]\r\n  );\r\n\r\n  const onItemSelect = React.useCallback(\r\n    (option: Option, isSelected: boolean) => {\r\n      if (!column) return;\r\n\r\n      if (multiple) {\r\n        const newSelectedValues = new Set(selectedValues);\r\n        if (isSelected) {\r\n          newSelectedValues.delete(option.value);\r\n        } else {\r\n          newSelectedValues.add(option.value);\r\n        }\r\n        const filterValues = Array.from(newSelectedValues);\r\n        column.setFilterValue(filterValues.length ? filterValues : undefined);\r\n      } else {\r\n        column.setFilterValue(isSelected ? undefined : [option.value]);\r\n        setOpen(false);\r\n      }\r\n    },\r\n    [column, multiple, selectedValues]\r\n  );\r\n\r\n  const onReset = React.useCallback(\r\n    (event?: React.MouseEvent) => {\r\n      event?.stopPropagation();\r\n      column?.setFilterValue(undefined);\r\n    },\r\n    [column]\r\n  );\r\n\r\n  return (\r\n    <Popover open={open} onOpenChange={setOpen}>\r\n      <PopoverTrigger asChild>\r\n        <Button variant='outline' size='sm' className='border-dashed'>\r\n          {selectedValues?.size > 0 ? (\r\n            <div\r\n              role='button'\r\n              aria-label={`Clear ${title} filter`}\r\n              tabIndex={0}\r\n              onClick={onReset}\r\n              className='focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none'\r\n            >\r\n              <XCircle />\r\n            </div>\r\n          ) : (\r\n            <PlusCircle />\r\n          )}\r\n          {title}\r\n          {selectedValues?.size > 0 && (\r\n            <>\r\n              <Separator\r\n                orientation='vertical'\r\n                className='mx-0.5 data-[orientation=vertical]:h-4'\r\n              />\r\n              <Badge\r\n                variant='secondary'\r\n                className='rounded-sm px-1 font-normal lg:hidden'\r\n              >\r\n                {selectedValues.size}\r\n              </Badge>\r\n              <div className='hidden items-center gap-1 lg:flex'>\r\n                {selectedValues.size > 2 ? (\r\n                  <Badge\r\n                    variant='secondary'\r\n                    className='rounded-sm px-1 font-normal'\r\n                  >\r\n                    {selectedValues.size} selected\r\n                  </Badge>\r\n                ) : (\r\n                  options\r\n                    .filter((option) => selectedValues.has(option.value))\r\n                    .map((option) => (\r\n                      <Badge\r\n                        variant='secondary'\r\n                        key={option.value}\r\n                        className='rounded-sm px-1 font-normal'\r\n                      >\r\n                        {option.label}\r\n                      </Badge>\r\n                    ))\r\n                )}\r\n              </div>\r\n            </>\r\n          )}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent className='w-[12.5rem] p-0' align='start'>\r\n        <Command>\r\n          <CommandInput placeholder={title} />\r\n          <CommandList className='max-h-full'>\r\n            <CommandEmpty>No results found.</CommandEmpty>\r\n            <CommandGroup className='max-h-[18.75rem] overflow-x-hidden overflow-y-auto'>\r\n              {options.map((option) => {\r\n                const isSelected = selectedValues.has(option.value);\r\n\r\n                return (\r\n                  <CommandItem\r\n                    key={option.value}\r\n                    onSelect={() => onItemSelect(option, isSelected)}\r\n                  >\r\n                    <div\r\n                      className={cn(\r\n                        'border-primary flex size-4 items-center justify-center rounded-sm border',\r\n                        isSelected\r\n                          ? 'bg-primary'\r\n                          : 'opacity-50 [&_svg]:invisible'\r\n                      )}\r\n                    >\r\n                      <CheckIcon />\r\n                    </div>\r\n                    {option.icon && <option.icon />}\r\n                    <span className='truncate'>{option.label}</span>\r\n                    {option.count && (\r\n                      <span className='ml-auto font-mono text-xs'>\r\n                        {option.count}\r\n                      </span>\r\n                    )}\r\n                  </CommandItem>\r\n                );\r\n              })}\r\n            </CommandGroup>\r\n            {selectedValues.size > 0 && (\r\n              <>\r\n                <CommandSeparator />\r\n                <CommandGroup>\r\n                  <CommandItem\r\n                    onSelect={() => onReset()}\r\n                    className='justify-center text-center'\r\n                  >\r\n                    Clear filters\r\n                  </CommandItem>\r\n                </CommandGroup>\r\n              </>\r\n            )}\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAIA;AAAA;AAEA;AACA;AACA;AASA;AAKA;AACA;AACA;AACA;;;AAzBA;;;;;;;;;;AAkCO,SAAS,uBAAsC,EACpD,MAAM,EACN,KAAK,EACL,OAAO,EACP,QAAQ,EACmC;;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IAEvC,MAAM,oBAAoB,QAAQ;IAClC,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;0DACjC,IAAM,IAAI,IAAI,MAAM,OAAO,CAAC,qBAAqB,oBAAoB,EAAE;yDACvE;QAAC;KAAkB;IAGrB,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;4DACnC,CAAC,QAAgB;YACf,IAAI,CAAC,QAAQ;YAEb,IAAI,UAAU;gBACZ,MAAM,oBAAoB,IAAI,IAAI;gBAClC,IAAI,YAAY;oBACd,kBAAkB,MAAM,CAAC,OAAO,KAAK;gBACvC,OAAO;oBACL,kBAAkB,GAAG,CAAC,OAAO,KAAK;gBACpC;gBACA,MAAM,eAAe,MAAM,IAAI,CAAC;gBAChC,OAAO,cAAc,CAAC,aAAa,MAAM,GAAG,eAAe;YAC7D,OAAO;gBACL,OAAO,cAAc,CAAC,aAAa,YAAY;oBAAC,OAAO,KAAK;iBAAC;gBAC7D,QAAQ;YACV;QACF;2DACA;QAAC;QAAQ;QAAU;KAAe;IAGpC,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;uDAC9B,CAAC;YACC,OAAO;YACP,QAAQ,eAAe;QACzB;sDACA;QAAC;KAAO;IAGV,qBACE,4SAAC,sIAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,WAAU;;wBAC3C,gBAAgB,OAAO,kBACtB,4SAAC;4BACC,MAAK;4BACL,cAAY,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC;4BACnC,UAAU;4BACV,SAAS;4BACT,WAAU;sCAEV,cAAA,4SAAC,mSAAA,CAAA,UAAO;;;;;;;;;iDAGV,4SAAC,ySAAA,CAAA,aAAU;;;;;wBAEZ;wBACA,gBAAgB,OAAO,mBACtB;;8CACE,4SAAC,wIAAA,CAAA,YAAS;oCACR,aAAY;oCACZ,WAAU;;;;;;8CAEZ,4SAAC,oIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CAET,eAAe,IAAI;;;;;;8CAEtB,4SAAC;oCAAI,WAAU;8CACZ,eAAe,IAAI,GAAG,kBACrB,4SAAC,oIAAA,CAAA,QAAK;wCACJ,SAAQ;wCACR,WAAU;;4CAET,eAAe,IAAI;4CAAC;;;;;;+CAGvB,QACG,MAAM,CAAC,CAAC,SAAW,eAAe,GAAG,CAAC,OAAO,KAAK,GAClD,GAAG,CAAC,CAAC,uBACJ,4SAAC,oIAAA,CAAA,QAAK;4CACJ,SAAQ;4CAER,WAAU;sDAET,OAAO,KAAK;2CAHR,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAYnC,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,WAAU;gBAAkB,OAAM;0BAChD,cAAA,4SAAC,sIAAA,CAAA,UAAO;;sCACN,4SAAC,sIAAA,CAAA,eAAY;4BAAC,aAAa;;;;;;sCAC3B,4SAAC,sIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,4SAAC,sIAAA,CAAA,eAAY;8CAAC;;;;;;8CACd,4SAAC,sIAAA,CAAA,eAAY;oCAAC,WAAU;8CACrB,QAAQ,GAAG,CAAC,CAAC;wCACZ,MAAM,aAAa,eAAe,GAAG,CAAC,OAAO,KAAK;wCAElD,qBACE,4SAAC,sIAAA,CAAA,cAAW;4CAEV,UAAU,IAAM,aAAa,QAAQ;;8DAErC,4SAAC;oDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA,aACI,eACA;8DAGN,cAAA,4SAAC,qRAAA,CAAA,YAAS;;;;;;;;;;gDAEX,OAAO,IAAI,kBAAI,4SAAC,OAAO,IAAI;;;;;8DAC5B,4SAAC;oDAAK,WAAU;8DAAY,OAAO,KAAK;;;;;;gDACvC,OAAO,KAAK,kBACX,4SAAC;oDAAK,WAAU;8DACb,OAAO,KAAK;;;;;;;2CAjBZ,OAAO,KAAK;;;;;oCAsBvB;;;;;;gCAED,eAAe,IAAI,GAAG,mBACrB;;sDACE,4SAAC,sIAAA,CAAA,mBAAgB;;;;;sDACjB,4SAAC,sIAAA,CAAA,eAAY;sDACX,cAAA,4SAAC,sIAAA,CAAA,cAAW;gDACV,UAAU,IAAM;gDAChB,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GAxJgB;KAAA", "debugId": null}}, {"offset": {"line": 2297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/slider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SliderPrimitive from '@radix-ui/react-slider';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Slider({\r\n  className,\r\n  defaultValue,\r\n  value,\r\n  min = 0,\r\n  max = 100,\r\n  ...props\r\n}: React.ComponentProps<typeof SliderPrimitive.Root>) {\r\n  const _values = React.useMemo(\r\n    () =>\r\n      Array.isArray(value)\r\n        ? value\r\n        : Array.isArray(defaultValue)\r\n          ? defaultValue\r\n          : [min, max],\r\n    [value, defaultValue, min, max]\r\n  );\r\n\r\n  return (\r\n    <SliderPrimitive.Root\r\n      data-slot='slider'\r\n      defaultValue={defaultValue}\r\n      value={value}\r\n      min={min}\r\n      max={max}\r\n      className={cn(\r\n        'relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SliderPrimitive.Track\r\n        data-slot='slider-track'\r\n        className={cn(\r\n          'bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5'\r\n        )}\r\n      >\r\n        <SliderPrimitive.Range\r\n          data-slot='slider-range'\r\n          className={cn(\r\n            'bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full'\r\n          )}\r\n        />\r\n      </SliderPrimitive.Track>\r\n      {Array.from({ length: _values.length }, (_, index) => (\r\n        <SliderPrimitive.Thumb\r\n          data-slot='slider-thumb'\r\n          key={index}\r\n          className='border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50'\r\n        />\r\n      ))}\r\n    </SliderPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Slider };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,YAAY,EACZ,KAAK,EACL,MAAM,CAAC,EACP,MAAM,GAAG,EACT,GAAG,OAC+C;;IAClD,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;mCAC1B,IACE,MAAM,OAAO,CAAC,SACV,QACA,MAAM,OAAO,CAAC,gBACZ,eACA;gBAAC;gBAAK;aAAI;kCAClB;QAAC;QAAO;QAAc;QAAK;KAAI;IAGjC,qBACE,4SAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,cAAc;QACd,OAAO;QACP,KAAK;QACL,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uOACA;QAED,GAAG,KAAK;;0BAET,4SAAC,kRAAA,CAAA,QAAqB;gBACpB,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;0BAGF,cAAA,4SAAC,kRAAA,CAAA,QAAqB;oBACpB,aAAU;oBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;YAIL,MAAM,IAAI,CAAC;gBAAE,QAAQ,QAAQ,MAAM;YAAC,GAAG,CAAC,GAAG,sBAC1C,4SAAC,kRAAA,CAAA,QAAqB;oBACpB,aAAU;oBAEV,WAAU;mBADL;;;;;;;;;;;AAMf;GArDS;KAAA", "debugId": null}}, {"offset": {"line": 2379, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-slider-filter.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Column } from '@tanstack/react-table';\r\nimport * as React from 'react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from '@/components/ui/popover';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { Slider } from '@/components/ui/slider';\r\nimport { cn } from '@/lib/utils';\r\nimport { PlusCircle, XCircle } from 'lucide-react';\r\n\r\ninterface Range {\r\n  min: number;\r\n  max: number;\r\n}\r\n\r\ntype RangeValue = [number, number];\r\n\r\nfunction getIsValidRange(value: unknown): value is RangeValue {\r\n  return (\r\n    Array.isArray(value) &&\r\n    value.length === 2 &&\r\n    typeof value[0] === 'number' &&\r\n    typeof value[1] === 'number'\r\n  );\r\n}\r\n\r\ninterface DataTableSliderFilterProps<TData> {\r\n  column: Column<TData, unknown>;\r\n  title?: string;\r\n}\r\n\r\nexport function DataTableSliderFilter<TData>({\r\n  column,\r\n  title\r\n}: DataTableSliderFilterProps<TData>) {\r\n  const id = React.useId();\r\n\r\n  const columnFilterValue = getIsValidRange(column.getFilterValue())\r\n    ? (column.getFilterValue() as RangeValue)\r\n    : undefined;\r\n\r\n  const defaultRange = column.columnDef.meta?.range;\r\n  const unit = column.columnDef.meta?.unit;\r\n\r\n  const { min, max, step } = React.useMemo<Range & { step: number }>(() => {\r\n    let minValue = 0;\r\n    let maxValue = 100;\r\n\r\n    if (defaultRange && getIsValidRange(defaultRange)) {\r\n      [minValue, maxValue] = defaultRange;\r\n    } else {\r\n      const values = column.getFacetedMinMaxValues();\r\n      if (values && Array.isArray(values) && values.length === 2) {\r\n        const [facetMinValue, facetMaxValue] = values;\r\n        if (\r\n          typeof facetMinValue === 'number' &&\r\n          typeof facetMaxValue === 'number'\r\n        ) {\r\n          minValue = facetMinValue;\r\n          maxValue = facetMaxValue;\r\n        }\r\n      }\r\n    }\r\n\r\n    const rangeSize = maxValue - minValue;\r\n    const step =\r\n      rangeSize <= 20\r\n        ? 1\r\n        : rangeSize <= 100\r\n          ? Math.ceil(rangeSize / 20)\r\n          : Math.ceil(rangeSize / 50);\r\n\r\n    return { min: minValue, max: maxValue, step };\r\n  }, [column, defaultRange]);\r\n\r\n  const range = React.useMemo((): RangeValue => {\r\n    return columnFilterValue ?? [min, max];\r\n  }, [columnFilterValue, min, max]);\r\n\r\n  const formatValue = React.useCallback((value: number) => {\r\n    return value.toLocaleString(undefined, { maximumFractionDigits: 0 });\r\n  }, []);\r\n\r\n  const onFromInputChange = React.useCallback(\r\n    (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const numValue = Number(event.target.value);\r\n      if (!Number.isNaN(numValue) && numValue >= min && numValue <= range[1]) {\r\n        column.setFilterValue([numValue, range[1]]);\r\n      }\r\n    },\r\n    [column, min, range]\r\n  );\r\n\r\n  const onToInputChange = React.useCallback(\r\n    (event: React.ChangeEvent<HTMLInputElement>) => {\r\n      const numValue = Number(event.target.value);\r\n      if (!Number.isNaN(numValue) && numValue <= max && numValue >= range[0]) {\r\n        column.setFilterValue([range[0], numValue]);\r\n      }\r\n    },\r\n    [column, max, range]\r\n  );\r\n\r\n  const onSliderValueChange = React.useCallback(\r\n    (value: RangeValue) => {\r\n      if (Array.isArray(value) && value.length === 2) {\r\n        column.setFilterValue(value);\r\n      }\r\n    },\r\n    [column]\r\n  );\r\n\r\n  const onReset = React.useCallback(\r\n    (event: React.MouseEvent) => {\r\n      if (event.target instanceof HTMLDivElement) {\r\n        event.stopPropagation();\r\n      }\r\n      column.setFilterValue(undefined);\r\n    },\r\n    [column]\r\n  );\r\n\r\n  return (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button variant='outline' size='sm' className='border-dashed'>\r\n          {columnFilterValue ? (\r\n            <div\r\n              role='button'\r\n              aria-label={`Clear ${title} filter`}\r\n              tabIndex={0}\r\n              className='focus-visible:ring-ring rounded-sm opacity-70 transition-opacity hover:opacity-100 focus-visible:ring-1 focus-visible:outline-none'\r\n              onClick={onReset}\r\n            >\r\n              <XCircle />\r\n            </div>\r\n          ) : (\r\n            <PlusCircle />\r\n          )}\r\n          <span>{title}</span>\r\n          {columnFilterValue ? (\r\n            <>\r\n              <Separator\r\n                orientation='vertical'\r\n                className='mx-0.5 data-[orientation=vertical]:h-4'\r\n              />\r\n              {formatValue(columnFilterValue[0])} -{' '}\r\n              {formatValue(columnFilterValue[1])}\r\n              {unit ? ` ${unit}` : ''}\r\n            </>\r\n          ) : null}\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent align='start' className='flex w-auto flex-col gap-4'>\r\n        <div className='flex flex-col gap-3'>\r\n          <p className='leading-none font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>\r\n            {title}\r\n          </p>\r\n          <div className='flex items-center gap-4'>\r\n            <Label htmlFor={`${id}-from`} className='sr-only'>\r\n              From\r\n            </Label>\r\n            <div className='relative'>\r\n              <Input\r\n                id={`${id}-from`}\r\n                type='number'\r\n                aria-valuemin={min}\r\n                aria-valuemax={max}\r\n                inputMode='numeric'\r\n                pattern='[0-9]*'\r\n                placeholder={min.toString()}\r\n                min={min}\r\n                max={max}\r\n                value={range[0]?.toString()}\r\n                onChange={onFromInputChange}\r\n                className={cn('h-8 w-24', unit && 'pr-8')}\r\n              />\r\n              {unit && (\r\n                <span className='bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm'>\r\n                  {unit}\r\n                </span>\r\n              )}\r\n            </div>\r\n            <Label htmlFor={`${id}-to`} className='sr-only'>\r\n              to\r\n            </Label>\r\n            <div className='relative'>\r\n              <Input\r\n                id={`${id}-to`}\r\n                type='number'\r\n                aria-valuemin={min}\r\n                aria-valuemax={max}\r\n                inputMode='numeric'\r\n                pattern='[0-9]*'\r\n                placeholder={max.toString()}\r\n                min={min}\r\n                max={max}\r\n                value={range[1]?.toString()}\r\n                onChange={onToInputChange}\r\n                className={cn('h-8 w-24', unit && 'pr-8')}\r\n              />\r\n              {unit && (\r\n                <span className='bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm'>\r\n                  {unit}\r\n                </span>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <Label htmlFor={`${id}-slider`} className='sr-only'>\r\n            {title} slider\r\n          </Label>\r\n          <Slider\r\n            id={`${id}-slider`}\r\n            min={min}\r\n            max={max}\r\n            step={step}\r\n            value={range}\r\n            onValueChange={onSliderValueChange}\r\n          />\r\n        </div>\r\n        <Button\r\n          aria-label={`Clear ${title} filter`}\r\n          variant='outline'\r\n          size='sm'\r\n          onClick={onReset}\r\n        >\r\n          Clear\r\n        </Button>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AAKA;AACA;AACA;AACA;AAAA;;;AAhBA;;;;;;;;;;AAyBA,SAAS,gBAAgB,KAAc;IACrC,OACE,MAAM,OAAO,CAAC,UACd,MAAM,MAAM,KAAK,KACjB,OAAO,KAAK,CAAC,EAAE,KAAK,YACpB,OAAO,KAAK,CAAC,EAAE,KAAK;AAExB;AAOO,SAAS,sBAA6B,EAC3C,MAAM,EACN,KAAK,EAC6B;;IAClC,MAAM,KAAK,CAAA,GAAA,4QAAA,CAAA,QAAW,AAAD;IAErB,MAAM,oBAAoB,gBAAgB,OAAO,cAAc,MAC1D,OAAO,cAAc,KACtB;IAEJ,MAAM,eAAe,OAAO,SAAS,CAAC,IAAI,EAAE;IAC5C,MAAM,OAAO,OAAO,SAAS,CAAC,IAAI,EAAE;IAEpC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;yCAA4B;YACjE,IAAI,WAAW;YACf,IAAI,WAAW;YAEf,IAAI,gBAAgB,gBAAgB,eAAe;gBACjD,CAAC,UAAU,SAAS,GAAG;YACzB,OAAO;gBACL,MAAM,SAAS,OAAO,sBAAsB;gBAC5C,IAAI,UAAU,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,KAAK,GAAG;oBAC1D,MAAM,CAAC,eAAe,cAAc,GAAG;oBACvC,IACE,OAAO,kBAAkB,YACzB,OAAO,kBAAkB,UACzB;wBACA,WAAW;wBACX,WAAW;oBACb;gBACF;YACF;YAEA,MAAM,YAAY,WAAW;YAC7B,MAAM,OACJ,aAAa,KACT,IACA,aAAa,MACX,KAAK,IAAI,CAAC,YAAY,MACtB,KAAK,IAAI,CAAC,YAAY;YAE9B,OAAO;gBAAE,KAAK;gBAAU,KAAK;gBAAU;YAAK;QAC9C;wCAAG;QAAC;QAAQ;KAAa;IAEzB,MAAM,QAAQ,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;gDAAE;YAC1B,OAAO,qBAAqB;gBAAC;gBAAK;aAAI;QACxC;+CAAG;QAAC;QAAmB;QAAK;KAAI;IAEhC,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;0DAAE,CAAC;YACrC,OAAO,MAAM,cAAc,CAAC,WAAW;gBAAE,uBAAuB;YAAE;QACpE;yDAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;gEACxC,CAAC;YACC,MAAM,WAAW,OAAO,MAAM,MAAM,CAAC,KAAK;YAC1C,IAAI,CAAC,OAAO,KAAK,CAAC,aAAa,YAAY,OAAO,YAAY,KAAK,CAAC,EAAE,EAAE;gBACtE,OAAO,cAAc,CAAC;oBAAC;oBAAU,KAAK,CAAC,EAAE;iBAAC;YAC5C;QACF;+DACA;QAAC;QAAQ;QAAK;KAAM;IAGtB,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;8DACtC,CAAC;YACC,MAAM,WAAW,OAAO,MAAM,MAAM,CAAC,KAAK;YAC1C,IAAI,CAAC,OAAO,KAAK,CAAC,aAAa,YAAY,OAAO,YAAY,KAAK,CAAC,EAAE,EAAE;gBACtE,OAAO,cAAc,CAAC;oBAAC,KAAK,CAAC,EAAE;oBAAE;iBAAS;YAC5C;QACF;6DACA;QAAC;QAAQ;QAAK;KAAM;IAGtB,MAAM,sBAAsB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;kEAC1C,CAAC;YACC,IAAI,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,KAAK,GAAG;gBAC9C,OAAO,cAAc,CAAC;YACxB;QACF;iEACA;QAAC;KAAO;IAGV,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;sDAC9B,CAAC;YACC,IAAI,MAAM,MAAM,YAAY,gBAAgB;gBAC1C,MAAM,eAAe;YACvB;YACA,OAAO,cAAc,CAAC;QACxB;qDACA;QAAC;KAAO;IAGV,qBACE,4SAAC,sIAAA,CAAA,UAAO;;0BACN,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;oBAAK,WAAU;;wBAC3C,kCACC,4SAAC;4BACC,MAAK;4BACL,cAAY,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC;4BACnC,UAAU;4BACV,WAAU;4BACV,SAAS;sCAET,cAAA,4SAAC,mSAAA,CAAA,UAAO;;;;;;;;;iDAGV,4SAAC,ySAAA,CAAA,aAAU;;;;;sCAEb,4SAAC;sCAAM;;;;;;wBACN,kCACC;;8CACE,4SAAC,wIAAA,CAAA,YAAS;oCACR,aAAY;oCACZ,WAAU;;;;;;gCAEX,YAAY,iBAAiB,CAAC,EAAE;gCAAE;gCAAG;gCACrC,YAAY,iBAAiB,CAAC,EAAE;gCAChC,OAAO,CAAC,CAAC,EAAE,MAAM,GAAG;;2CAErB;;;;;;;;;;;;0BAGR,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAM;gBAAQ,WAAU;;kCACtC,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAE,WAAU;0CACV;;;;;;0CAEH,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,oIAAA,CAAA,QAAK;wCAAC,SAAS,GAAG,GAAG,KAAK,CAAC;wCAAE,WAAU;kDAAU;;;;;;kDAGlD,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,oIAAA,CAAA,QAAK;gDACJ,IAAI,GAAG,GAAG,KAAK,CAAC;gDAChB,MAAK;gDACL,iBAAe;gDACf,iBAAe;gDACf,WAAU;gDACV,SAAQ;gDACR,aAAa,IAAI,QAAQ;gDACzB,KAAK;gDACL,KAAK;gDACL,OAAO,KAAK,CAAC,EAAE,EAAE;gDACjB,UAAU;gDACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,QAAQ;;;;;;4CAEnC,sBACC,4SAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;kDAIP,4SAAC,oIAAA,CAAA,QAAK;wCAAC,SAAS,GAAG,GAAG,GAAG,CAAC;wCAAE,WAAU;kDAAU;;;;;;kDAGhD,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,oIAAA,CAAA,QAAK;gDACJ,IAAI,GAAG,GAAG,GAAG,CAAC;gDACd,MAAK;gDACL,iBAAe;gDACf,iBAAe;gDACf,WAAU;gDACV,SAAQ;gDACR,aAAa,IAAI,QAAQ;gDACzB,KAAK;gDACL,KAAK;gDACL,OAAO,KAAK,CAAC,EAAE,EAAE;gDACjB,UAAU;gDACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,QAAQ;;;;;;4CAEnC,sBACC,4SAAC;gDAAK,WAAU;0DACb;;;;;;;;;;;;;;;;;;0CAKT,4SAAC,oIAAA,CAAA,QAAK;gCAAC,SAAS,GAAG,GAAG,OAAO,CAAC;gCAAE,WAAU;;oCACvC;oCAAM;;;;;;;0CAET,4SAAC,qIAAA,CAAA,SAAM;gCACL,IAAI,GAAG,GAAG,OAAO,CAAC;gCAClB,KAAK;gCACL,KAAK;gCACL,MAAM;gCACN,OAAO;gCACP,eAAe;;;;;;;;;;;;kCAGnB,4SAAC,qIAAA,CAAA,SAAM;wBACL,cAAY,CAAC,MAAM,EAAE,MAAM,OAAO,CAAC;wBACnC,SAAQ;wBACR,MAAK;wBACL,SAAS;kCACV;;;;;;;;;;;;;;;;;;AAMT;GAxMgB;KAAA", "debugId": null}}, {"offset": {"line": 2753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-view-options.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Table } from '@tanstack/react-table';\r\nimport { Settings2 } from 'lucide-react';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Command,\r\n  CommandEmpty,\r\n  CommandGroup,\r\n  CommandInput,\r\n  CommandItem,\r\n  CommandList\r\n} from '@/components/ui/command';\r\nimport {\r\n  Popover,\r\n  PopoverContent,\r\n  PopoverTrigger\r\n} from '@/components/ui/popover';\r\nimport { cn } from '@/lib/utils';\r\nimport * as React from 'react';\r\nimport { CheckIcon, CaretSortIcon } from '@radix-ui/react-icons';\r\n\r\ninterface DataTableViewOptionsProps<TData> {\r\n  table: Table<TData>;\r\n}\r\n\r\nexport function DataTableViewOptions<TData>({\r\n  table\r\n}: DataTableViewOptionsProps<TData>) {\r\n  const columns = React.useMemo(\r\n    () =>\r\n      table\r\n        .getAllColumns()\r\n        .filter(\r\n          (column) =>\r\n            typeof column.accessorFn !== 'undefined' && column.getCanHide()\r\n        ),\r\n    [table]\r\n  );\r\n\r\n  return (\r\n    <Popover>\r\n      <PopoverTrigger asChild>\r\n        <Button\r\n          aria-label='Toggle columns'\r\n          role='combobox'\r\n          variant='outline'\r\n          size='sm'\r\n          className='ml-auto hidden h-8 lg:flex'\r\n        >\r\n          <Settings2 />\r\n          View\r\n          <CaretSortIcon className='ml-auto opacity-50' />\r\n        </Button>\r\n      </PopoverTrigger>\r\n      <PopoverContent align='end' className='w-44 p-0'>\r\n        <Command>\r\n          <CommandInput placeholder='Search columns...' />\r\n          <CommandList>\r\n            <CommandEmpty>No columns found.</CommandEmpty>\r\n            <CommandGroup>\r\n              {columns.map((column) => (\r\n                <CommandItem\r\n                  key={column.id}\r\n                  onSelect={() =>\r\n                    column.toggleVisibility(!column.getIsVisible())\r\n                  }\r\n                >\r\n                  <span className='truncate'>\r\n                    {column.columnDef.meta?.label ?? column.id}\r\n                  </span>\r\n                  <CheckIcon\r\n                    className={cn(\r\n                      'ml-auto size-4 shrink-0',\r\n                      column.getIsVisible() ? 'opacity-100' : 'opacity-0'\r\n                    )}\r\n                  />\r\n                </CommandItem>\r\n              ))}\r\n            </CommandGroup>\r\n          </CommandList>\r\n        </Command>\r\n      </PopoverContent>\r\n    </Popover>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AAQA;AAKA;AACA;AACA;;;AArBA;;;;;;;;AA2BO,SAAS,qBAA4B,EAC1C,KAAK,EAC4B;;IACjC,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;iDAC1B,IACE,MACG,aAAa,GACb,MAAM;yDACL,CAAC,SACC,OAAO,OAAO,UAAU,KAAK,eAAe,OAAO,UAAU;;gDAErE;QAAC;KAAM;IAGT,qBACE,4SAAC,sIAAA,CAAA,UAAO;;0BACN,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBACL,cAAW;oBACX,MAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,4SAAC,uSAAA,CAAA,YAAS;;;;;wBAAG;sCAEb,4SAAC,qRAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAG7B,4SAAC,sIAAA,CAAA,iBAAc;gBAAC,OAAM;gBAAM,WAAU;0BACpC,cAAA,4SAAC,sIAAA,CAAA,UAAO;;sCACN,4SAAC,sIAAA,CAAA,eAAY;4BAAC,aAAY;;;;;;sCAC1B,4SAAC,sIAAA,CAAA,cAAW;;8CACV,4SAAC,sIAAA,CAAA,eAAY;8CAAC;;;;;;8CACd,4SAAC,sIAAA,CAAA,eAAY;8CACV,QAAQ,GAAG,CAAC,CAAC,uBACZ,4SAAC,sIAAA,CAAA,cAAW;4CAEV,UAAU,IACR,OAAO,gBAAgB,CAAC,CAAC,OAAO,YAAY;;8DAG9C,4SAAC;oDAAK,WAAU;8DACb,OAAO,SAAS,CAAC,IAAI,EAAE,SAAS,OAAO,EAAE;;;;;;8DAE5C,4SAAC,qRAAA,CAAA,YAAS;oDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2BACA,OAAO,YAAY,KAAK,gBAAgB;;;;;;;2CAXvC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBhC;GA3DgB;KAAA", "debugId": null}}, {"offset": {"line": 2906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/table/data-table-toolbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport type { Column, Table } from '@tanstack/react-table';\r\nimport * as React from 'react';\r\n\r\nimport { DataTableDateFilter } from '@/components/ui/table/data-table-date-filter';\r\nimport { DataTableFacetedFilter } from '@/components/ui/table/data-table-faceted-filter';\r\nimport { DataTableSliderFilter } from '@/components/ui/table/data-table-slider-filter';\r\nimport { DataTableViewOptions } from '@/components/ui/table/data-table-view-options';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { cn } from '@/lib/utils';\r\nimport { Cross2Icon } from '@radix-ui/react-icons';\r\n\r\ninterface DataTableToolbarProps<TData> extends React.ComponentProps<'div'> {\r\n  table: Table<TData>;\r\n}\r\n\r\nexport function DataTableToolbar<TData>({\r\n  table,\r\n  children,\r\n  className,\r\n  ...props\r\n}: DataTableToolbarProps<TData>) {\r\n  const isFiltered = table.getState().columnFilters.length > 0;\r\n\r\n  const columns = React.useMemo(\r\n    () => table.getAllColumns().filter((column) => column.getCanFilter()),\r\n    [table]\r\n  );\r\n\r\n  const onReset = React.useCallback(() => {\r\n    table.resetColumnFilters();\r\n  }, [table]);\r\n\r\n  return (\r\n    <div\r\n      role='toolbar'\r\n      aria-orientation='horizontal'\r\n      className={cn(\r\n        'flex w-full items-start justify-between gap-2 p-1',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <div className='flex flex-1 flex-wrap items-center gap-2'>\r\n        {columns.map((column) => (\r\n          <DataTableToolbarFilter key={column.id} column={column} />\r\n        ))}\r\n        {isFiltered && (\r\n          <Button\r\n            aria-label='Reset filters'\r\n            variant='outline'\r\n            size='sm'\r\n            className='border-dashed'\r\n            onClick={onReset}\r\n          >\r\n            <Cross2Icon />\r\n            Reset\r\n          </Button>\r\n        )}\r\n      </div>\r\n      <div className='flex items-center gap-2'>\r\n        {children}\r\n        <DataTableViewOptions table={table} />\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\ninterface DataTableToolbarFilterProps<TData> {\r\n  column: Column<TData>;\r\n}\r\n\r\nfunction DataTableToolbarFilter<TData>({\r\n  column\r\n}: DataTableToolbarFilterProps<TData>) {\r\n  {\r\n    const columnMeta = column.columnDef.meta;\r\n\r\n    const onFilterRender = React.useCallback(() => {\r\n      if (!columnMeta?.variant) return null;\r\n\r\n      switch (columnMeta.variant) {\r\n        case 'text':\r\n          return (\r\n            <Input\r\n              placeholder={columnMeta.placeholder ?? columnMeta.label}\r\n              value={(column.getFilterValue() as string) ?? ''}\r\n              onChange={(event) => column.setFilterValue(event.target.value)}\r\n              className='h-8 w-40 lg:w-56'\r\n            />\r\n          );\r\n\r\n        case 'number':\r\n          return (\r\n            <div className='relative'>\r\n              <Input\r\n                type='number'\r\n                inputMode='numeric'\r\n                placeholder={columnMeta.placeholder ?? columnMeta.label}\r\n                value={(column.getFilterValue() as string) ?? ''}\r\n                onChange={(event) => column.setFilterValue(event.target.value)}\r\n                className={cn('h-8 w-[120px]', columnMeta.unit && 'pr-8')}\r\n              />\r\n              {columnMeta.unit && (\r\n                <span className='bg-accent text-muted-foreground absolute top-0 right-0 bottom-0 flex items-center rounded-r-md px-2 text-sm'>\r\n                  {columnMeta.unit}\r\n                </span>\r\n              )}\r\n            </div>\r\n          );\r\n\r\n        case 'range':\r\n          return (\r\n            <DataTableSliderFilter\r\n              column={column}\r\n              title={columnMeta.label ?? column.id}\r\n            />\r\n          );\r\n\r\n        case 'date':\r\n        case 'dateRange':\r\n          return (\r\n            <DataTableDateFilter\r\n              column={column}\r\n              title={columnMeta.label ?? column.id}\r\n              multiple={columnMeta.variant === 'dateRange'}\r\n            />\r\n          );\r\n\r\n        case 'select':\r\n        case 'multiSelect':\r\n          return (\r\n            <DataTableFacetedFilter\r\n              column={column}\r\n              title={columnMeta.label ?? column.id}\r\n              options={columnMeta.options ?? []}\r\n              multiple={columnMeta.variant === 'multiSelect'}\r\n            />\r\n          );\r\n\r\n        default:\r\n          return null;\r\n      }\r\n    }, [column, columnMeta]);\r\n\r\n    return onFilterRender();\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;AAkBO,SAAS,iBAAwB,EACtC,KAAK,EACL,QAAQ,EACR,SAAS,EACT,GAAG,OAC0B;;IAC7B,MAAM,aAAa,MAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,GAAG;IAE3D,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;6CAC1B,IAAM,MAAM,aAAa,GAAG,MAAM;qDAAC,CAAC,SAAW,OAAO,YAAY;;4CAClE;QAAC;KAAM;IAGT,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;iDAAE;YAChC,MAAM,kBAAkB;QAC1B;gDAAG;QAAC;KAAM;IAEV,qBACE,4SAAC;QACC,MAAK;QACL,oBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;0BAET,4SAAC;gBAAI,WAAU;;oBACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,4SAAC;4BAAuC,QAAQ;2BAAnB,OAAO,EAAE;;;;;oBAEvC,4BACC,4SAAC,qIAAA,CAAA,SAAM;wBACL,cAAW;wBACX,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;;0CAET,4SAAC,qRAAA,CAAA,aAAU;;;;;4BAAG;;;;;;;;;;;;;0BAKpB,4SAAC;gBAAI,WAAU;;oBACZ;kCACD,4SAAC,wKAAA,CAAA,uBAAoB;wBAAC,OAAO;;;;;;;;;;;;;;;;;;AAIrC;GAlDgB;KAAA;AAuDhB,SAAS,uBAA8B,EACrC,MAAM,EAC6B;IACnC;QACE,MAAM,aAAa,OAAO,SAAS,CAAC,IAAI;QAExC,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;kEAAE;gBACvC,IAAI,CAAC,YAAY,SAAS,OAAO;gBAEjC,OAAQ,WAAW,OAAO;oBACxB,KAAK;wBACH,qBACE,4SAAC,oIAAA,CAAA,QAAK;4BACJ,aAAa,WAAW,WAAW,IAAI,WAAW,KAAK;4BACvD,OAAO,AAAC,OAAO,cAAc,MAAiB;4BAC9C,QAAQ;sFAAE,CAAC,QAAU,OAAO,cAAc,CAAC,MAAM,MAAM,CAAC,KAAK;;4BAC7D,WAAU;;;;;;oBAIhB,KAAK;wBACH,qBACE,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,oIAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,WAAU;oCACV,aAAa,WAAW,WAAW,IAAI,WAAW,KAAK;oCACvD,OAAO,AAAC,OAAO,cAAc,MAAiB;oCAC9C,QAAQ;8FAAE,CAAC,QAAU,OAAO,cAAc,CAAC,MAAM,MAAM,CAAC,KAAK;;oCAC7D,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB,WAAW,IAAI,IAAI;;;;;;gCAEnD,WAAW,IAAI,kBACd,4SAAC;oCAAK,WAAU;8CACb,WAAW,IAAI;;;;;;;;;;;;oBAM1B,KAAK;wBACH,qBACE,4SAAC,yKAAA,CAAA,wBAAqB;4BACpB,QAAQ;4BACR,OAAO,WAAW,KAAK,IAAI,OAAO,EAAE;;;;;;oBAI1C,KAAK;oBACL,KAAK;wBACH,qBACE,4SAAC,uKAAA,CAAA,sBAAmB;4BAClB,QAAQ;4BACR,OAAO,WAAW,KAAK,IAAI,OAAO,EAAE;4BACpC,UAAU,WAAW,OAAO,KAAK;;;;;;oBAIvC,KAAK;oBACL,KAAK;wBACH,qBACE,4SAAC,0KAAA,CAAA,yBAAsB;4BACrB,QAAQ;4BACR,OAAO,WAAW,KAAK,IAAI,OAAO,EAAE;4BACpC,SAAS,WAAW,OAAO,IAAI,EAAE;4BACjC,UAAU,WAAW,OAAO,KAAK;;;;;;oBAIvC;wBACE,OAAO;gBACX;YACF;iEAAG;YAAC;YAAQ;SAAW;QAEvB,OAAO;IACT;AACF;MA3ES", "debugId": null}}, {"offset": {"line": 3123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-callback-ref.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\n/**\r\n * @see https://github.com/radix-ui/primitives/blob/main/packages/react/use-callback-ref/src/useCallbackRef.tsx\r\n */\r\n\r\n/**\r\n * A custom hook that converts a callback to a ref to avoid triggering re-renders when passed as a\r\n * prop or avoid re-executing effects when passed as a dependency\r\n */\r\nfunction useCallbackRef<T extends (...args: never[]) => unknown>(\r\n  callback: T | undefined\r\n): T {\r\n  const callbackRef = React.useRef(callback);\r\n\r\n  React.useEffect(() => {\r\n    callbackRef.current = callback;\r\n  });\r\n\r\n  // https://github.com/facebook/react/issues/19240\r\n  return React.useMemo(\r\n    () => ((...args) => callbackRef.current?.(...args)) as T,\r\n    []\r\n  );\r\n}\r\n\r\nexport { useCallbackRef };\r\n"], "names": [], "mappings": ";;;AAAA;;;AAEA;;CAEC,GAED;;;CAGC,GACD,SAAS,eACP,QAAuB;;IAEvB,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IAEjC,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;oCAAE;YACd,YAAY,OAAO,GAAG;QACxB;;IAEA,iDAAiD;IACjD,OAAO,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;kCACjB,IAAM;0CAAC,CAAC,GAAG,OAAS,YAAY,OAAO,MAAM;aAAK;iCAClD,EAAE;AAEN;GAdS", "debugId": null}}, {"offset": {"line": 3160, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-debounced-callback.ts"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { useCallbackRef } from '@/hooks/use-callback-ref';\r\n\r\nexport function useDebouncedCallback<T extends (...args: never[]) => unknown>(\r\n  callback: T,\r\n  delay: number\r\n) {\r\n  const handleCallback = useCallbackRef(callback);\r\n  const debounceTimerRef = React.useRef(0);\r\n  React.useEffect(\r\n    () => () => window.clearTimeout(debounceTimerRef.current),\r\n    []\r\n  );\r\n\r\n  const setValue = React.useCallback(\r\n    (...args: Parameters<T>) => {\r\n      window.clearTimeout(debounceTimerRef.current);\r\n      debounceTimerRef.current = window.setTimeout(\r\n        () => handleCallback(...args),\r\n        delay\r\n      );\r\n    },\r\n    [handleCallback, delay]\r\n  );\r\n\r\n  return setValue;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AAEO,SAAS,qBACd,QAAW,EACX,KAAa;;IAEb,MAAM,iBAAiB,CAAA,GAAA,0IAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IACtC,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;0CACZ;kDAAM,IAAM,OAAO,YAAY,CAAC,iBAAiB,OAAO;;yCACxD,EAAE;IAGJ,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;sDAC/B,CAAC,GAAG;YACF,OAAO,YAAY,CAAC,iBAAiB,OAAO;YAC5C,iBAAiB,OAAO,GAAG,OAAO,UAAU;8DAC1C,IAAM,kBAAkB;6DACxB;QAEJ;qDACA;QAAC;QAAgB;KAAM;IAGzB,OAAO;AACT;GAvBgB;;QAIS,0IAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 3204, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/parsers.ts"], "sourcesContent": ["import { createParser } from 'nuqs/server';\r\nimport { z } from 'zod';\r\n\r\nimport { dataTableConfig } from '@/config/data-table';\r\n\r\nimport type {\r\n  ExtendedColumnFilter,\r\n  ExtendedColumnSort\r\n} from '@/types/data-table';\r\n\r\nconst sortingItemSchema = z.object({\r\n  id: z.string(),\r\n  desc: z.boolean()\r\n});\r\n\r\nexport const getSortingStateParser = <TData>(\r\n  columnIds?: string[] | Set<string>\r\n) => {\r\n  const validKeys = columnIds\r\n    ? columnIds instanceof Set\r\n      ? columnIds\r\n      : new Set(columnIds)\r\n    : null;\r\n\r\n  return createParser({\r\n    parse: (value) => {\r\n      try {\r\n        const parsed = JSON.parse(value);\r\n        const result = z.array(sortingItemSchema).safeParse(parsed);\r\n\r\n        if (!result.success) return null;\r\n\r\n        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {\r\n          return null;\r\n        }\r\n\r\n        return result.data as ExtendedColumnSort<TData>[];\r\n      } catch {\r\n        return null;\r\n      }\r\n    },\r\n    serialize: (value) => JSON.stringify(value),\r\n    eq: (a, b) =>\r\n      a.length === b.length &&\r\n      a.every(\r\n        (item, index) =>\r\n          item.id === b[index]?.id && item.desc === b[index]?.desc\r\n      )\r\n  });\r\n};\r\n\r\nconst filterItemSchema = z.object({\r\n  id: z.string(),\r\n  value: z.union([z.string(), z.array(z.string())]),\r\n  variant: z.enum(dataTableConfig.filterVariants),\r\n  operator: z.enum(dataTableConfig.operators),\r\n  filterId: z.string()\r\n});\r\n\r\nexport type FilterItemSchema = z.infer<typeof filterItemSchema>;\r\n\r\nexport const getFiltersStateParser = <TData>(\r\n  columnIds?: string[] | Set<string>\r\n) => {\r\n  const validKeys = columnIds\r\n    ? columnIds instanceof Set\r\n      ? columnIds\r\n      : new Set(columnIds)\r\n    : null;\r\n\r\n  return createParser({\r\n    parse: (value) => {\r\n      try {\r\n        const parsed = JSON.parse(value);\r\n        const result = z.array(filterItemSchema).safeParse(parsed);\r\n\r\n        if (!result.success) return null;\r\n\r\n        if (validKeys && result.data.some((item) => !validKeys.has(item.id))) {\r\n          return null;\r\n        }\r\n\r\n        return result.data as ExtendedColumnFilter<TData>[];\r\n      } catch {\r\n        return null;\r\n      }\r\n    },\r\n    serialize: (value) => JSON.stringify(value),\r\n    eq: (a, b) =>\r\n      a.length === b.length &&\r\n      a.every(\r\n        (filter, index) =>\r\n          filter.id === b[index]?.id &&\r\n          filter.value === b[index]?.value &&\r\n          filter.variant === b[index]?.variant &&\r\n          filter.operator === b[index]?.operator\r\n      )\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAOA,MAAM,oBAAoB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACjC,IAAI,wLAAA,CAAA,IAAC,CAAC,MAAM;IACZ,MAAM,wLAAA,CAAA,IAAC,CAAC,OAAO;AACjB;AAEO,MAAM,wBAAwB,CACnC;IAEA,MAAM,YAAY,YACd,qBAAqB,MACnB,YACA,IAAI,IAAI,aACV;IAEJ,OAAO,CAAA,GAAA,wPAAA,CAAA,eAAY,AAAD,EAAE;QAClB,OAAO,CAAC;YACN,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,MAAM,SAAS,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,mBAAmB,SAAS,CAAC;gBAEpD,IAAI,CAAC,OAAO,OAAO,EAAE,OAAO;gBAE5B,IAAI,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAS,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,IAAI;oBACpE,OAAO;gBACT;gBAEA,OAAO,OAAO,IAAI;YACpB,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QACA,WAAW,CAAC,QAAU,KAAK,SAAS,CAAC;QACrC,IAAI,CAAC,GAAG,IACN,EAAE,MAAM,KAAK,EAAE,MAAM,IACrB,EAAE,KAAK,CACL,CAAC,MAAM,QACL,KAAK,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,KAAK,CAAC,CAAC,MAAM,EAAE;IAE5D;AACF;AAEA,MAAM,mBAAmB,wLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAChC,IAAI,wLAAA,CAAA,IAAC,CAAC,MAAM;IACZ,OAAO,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC;QAAC,wLAAA,CAAA,IAAC,CAAC,MAAM;QAAI,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,wLAAA,CAAA,IAAC,CAAC,MAAM;KAAI;IAChD,SAAS,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,iIAAA,CAAA,kBAAe,CAAC,cAAc;IAC9C,UAAU,wLAAA,CAAA,IAAC,CAAC,IAAI,CAAC,iIAAA,CAAA,kBAAe,CAAC,SAAS;IAC1C,UAAU,wLAAA,CAAA,IAAC,CAAC,MAAM;AACpB;AAIO,MAAM,wBAAwB,CACnC;IAEA,MAAM,YAAY,YACd,qBAAqB,MACnB,YACA,IAAI,IAAI,aACV;IAEJ,OAAO,CAAA,GAAA,wPAAA,CAAA,eAAY,AAAD,EAAE;QAClB,OAAO,CAAC;YACN,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,MAAM,SAAS,wLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,kBAAkB,SAAS,CAAC;gBAEnD,IAAI,CAAC,OAAO,OAAO,EAAE,OAAO;gBAE5B,IAAI,aAAa,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,OAAS,CAAC,UAAU,GAAG,CAAC,KAAK,EAAE,IAAI;oBACpE,OAAO;gBACT;gBAEA,OAAO,OAAO,IAAI;YACpB,EAAE,OAAM;gBACN,OAAO;YACT;QACF;QACA,WAAW,CAAC,QAAU,KAAK,SAAS,CAAC;QACrC,IAAI,CAAC,GAAG,IACN,EAAE,MAAM,KAAK,EAAE,MAAM,IACrB,EAAE,KAAK,CACL,CAAC,QAAQ,QACP,OAAO,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,MACxB,OAAO,KAAK,KAAK,CAAC,CAAC,MAAM,EAAE,SAC3B,OAAO,OAAO,KAAK,CAAC,CAAC,MAAM,EAAE,WAC7B,OAAO,QAAQ,KAAK,CAAC,CAAC,MAAM,EAAE;IAEtC;AACF", "debugId": null}}, {"offset": {"line": 3277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/hooks/use-data-table.ts"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  type ColumnFiltersState,\r\n  type PaginationState,\r\n  type RowSelectionState,\r\n  type SortingState,\r\n  type TableOptions,\r\n  type TableState,\r\n  type Updater,\r\n  type VisibilityState,\r\n  getCoreRowModel,\r\n  getFacetedMinMaxValues,\r\n  getFacetedRowModel,\r\n  getFacetedUniqueValues,\r\n  getFilteredRowModel,\r\n  getPaginationRowModel,\r\n  getSortedRowModel,\r\n  useReactTable\r\n} from '@tanstack/react-table';\r\nimport {\r\n  type Parser,\r\n  type UseQueryStateOptions,\r\n  parseAsArrayOf,\r\n  parseAsInteger,\r\n  parseAsString,\r\n  useQueryState,\r\n  useQueryStates\r\n} from 'nuqs';\r\nimport * as React from 'react';\r\n\r\nimport { useDebouncedCallback } from '@/hooks/use-debounced-callback';\r\nimport { getSortingStateParser } from '@/lib/parsers';\r\nimport type { ExtendedColumnSort } from '@/types/data-table';\r\n\r\nconst PAGE_KEY = 'page';\r\nconst PER_PAGE_KEY = 'perPage';\r\nconst SORT_KEY = 'sort';\r\nconst ARRAY_SEPARATOR = ',';\r\nconst DEBOUNCE_MS = 300;\r\nconst THROTTLE_MS = 50;\r\n\r\ninterface UseDataTableProps<TData>\r\n  extends Omit<\r\n      TableOptions<TData>,\r\n      | 'state'\r\n      | 'pageCount'\r\n      | 'getCoreRowModel'\r\n      | 'manualFiltering'\r\n      | 'manualPagination'\r\n      | 'manualSorting'\r\n    >,\r\n    Required<Pick<TableOptions<TData>, 'pageCount'>> {\r\n  initialState?: Omit<Partial<TableState>, 'sorting'> & {\r\n    sorting?: ExtendedColumnSort<TData>[];\r\n  };\r\n  history?: 'push' | 'replace';\r\n  debounceMs?: number;\r\n  throttleMs?: number;\r\n  clearOnDefault?: boolean;\r\n  enableAdvancedFilter?: boolean;\r\n  scroll?: boolean;\r\n  shallow?: boolean;\r\n  startTransition?: React.TransitionStartFunction;\r\n}\r\n\r\nexport function useDataTable<TData>(props: UseDataTableProps<TData>) {\r\n  const {\r\n    columns,\r\n    pageCount = -1,\r\n    initialState,\r\n    history = 'replace',\r\n    debounceMs = DEBOUNCE_MS,\r\n    throttleMs = THROTTLE_MS,\r\n    clearOnDefault = false,\r\n    enableAdvancedFilter = false,\r\n    scroll = false,\r\n    shallow = true,\r\n    startTransition,\r\n    ...tableProps\r\n  } = props;\r\n\r\n  const queryStateOptions = React.useMemo<\r\n    Omit<UseQueryStateOptions<string>, 'parse'>\r\n  >(\r\n    () => ({\r\n      history,\r\n      scroll,\r\n      shallow,\r\n      throttleMs,\r\n      debounceMs,\r\n      clearOnDefault,\r\n      startTransition\r\n    }),\r\n    [\r\n      history,\r\n      scroll,\r\n      shallow,\r\n      throttleMs,\r\n      debounceMs,\r\n      clearOnDefault,\r\n      startTransition\r\n    ]\r\n  );\r\n\r\n  const [rowSelection, setRowSelection] = React.useState<RowSelectionState>(\r\n    initialState?.rowSelection ?? {}\r\n  );\r\n  const [columnVisibility, setColumnVisibility] =\r\n    React.useState<VisibilityState>(initialState?.columnVisibility ?? {});\r\n\r\n  const [page, setPage] = useQueryState(\r\n    PAGE_KEY,\r\n    parseAsInteger.withOptions(queryStateOptions).withDefault(1)\r\n  );\r\n  const [perPage, setPerPage] = useQueryState(\r\n    PER_PAGE_KEY,\r\n    parseAsInteger\r\n      .withOptions(queryStateOptions)\r\n      .withDefault(initialState?.pagination?.pageSize ?? 10)\r\n  );\r\n\r\n  const pagination: PaginationState = React.useMemo(() => {\r\n    return {\r\n      pageIndex: page - 1, // zero-based index -> one-based index\r\n      pageSize: perPage\r\n    };\r\n  }, [page, perPage]);\r\n\r\n  const onPaginationChange = React.useCallback(\r\n    (updaterOrValue: Updater<PaginationState>) => {\r\n      if (typeof updaterOrValue === 'function') {\r\n        const newPagination = updaterOrValue(pagination);\r\n        void setPage(newPagination.pageIndex + 1);\r\n        void setPerPage(newPagination.pageSize);\r\n      } else {\r\n        void setPage(updaterOrValue.pageIndex + 1);\r\n        void setPerPage(updaterOrValue.pageSize);\r\n      }\r\n    },\r\n    [pagination, setPage, setPerPage]\r\n  );\r\n\r\n  const columnIds = React.useMemo(() => {\r\n    return new Set(\r\n      columns.map((column) => column.id).filter(Boolean) as string[]\r\n    );\r\n  }, [columns]);\r\n\r\n  const [sorting, setSorting] = useQueryState(\r\n    SORT_KEY,\r\n    getSortingStateParser<TData>(columnIds)\r\n      .withOptions(queryStateOptions)\r\n      .withDefault(initialState?.sorting ?? [])\r\n  );\r\n\r\n  const onSortingChange = React.useCallback(\r\n    (updaterOrValue: Updater<SortingState>) => {\r\n      if (typeof updaterOrValue === 'function') {\r\n        const newSorting = updaterOrValue(sorting);\r\n        setSorting(newSorting as ExtendedColumnSort<TData>[]);\r\n      } else {\r\n        setSorting(updaterOrValue as ExtendedColumnSort<TData>[]);\r\n      }\r\n    },\r\n    [sorting, setSorting]\r\n  );\r\n\r\n  const filterableColumns = React.useMemo(() => {\r\n    if (enableAdvancedFilter) return [];\r\n\r\n    return columns.filter((column) => column.enableColumnFilter);\r\n  }, [columns, enableAdvancedFilter]);\r\n\r\n  const filterParsers = React.useMemo(() => {\r\n    if (enableAdvancedFilter) return {};\r\n\r\n    return filterableColumns.reduce<\r\n      Record<string, Parser<string> | Parser<string[]>>\r\n    >((acc, column) => {\r\n      if (column.meta?.options) {\r\n        acc[column.id ?? ''] = parseAsArrayOf(\r\n          parseAsString,\r\n          ARRAY_SEPARATOR\r\n        ).withOptions(queryStateOptions);\r\n      } else {\r\n        acc[column.id ?? ''] = parseAsString.withOptions(queryStateOptions);\r\n      }\r\n      return acc;\r\n    }, {});\r\n  }, [filterableColumns, queryStateOptions, enableAdvancedFilter]);\r\n\r\n  const [filterValues, setFilterValues] = useQueryStates(filterParsers);\r\n\r\n  const debouncedSetFilterValues = useDebouncedCallback(\r\n    (values: typeof filterValues) => {\r\n      void setPage(1);\r\n      void setFilterValues(values);\r\n    },\r\n    debounceMs\r\n  );\r\n\r\n  const initialColumnFilters: ColumnFiltersState = React.useMemo(() => {\r\n    if (enableAdvancedFilter) return [];\r\n\r\n    return Object.entries(filterValues).reduce<ColumnFiltersState>(\r\n      (filters, [key, value]) => {\r\n        if (value !== null) {\r\n          const processedValue = Array.isArray(value)\r\n            ? value\r\n            : typeof value === 'string' && /[^a-zA-Z0-9]/.test(value)\r\n              ? value.split(/[^a-zA-Z0-9]+/).filter(Boolean)\r\n              : [value];\r\n\r\n          filters.push({\r\n            id: key,\r\n            value: processedValue\r\n          });\r\n        }\r\n        return filters;\r\n      },\r\n      []\r\n    );\r\n  }, [filterValues, enableAdvancedFilter]);\r\n\r\n  const [columnFilters, setColumnFilters] =\r\n    React.useState<ColumnFiltersState>(initialColumnFilters);\r\n\r\n  const onColumnFiltersChange = React.useCallback(\r\n    (updaterOrValue: Updater<ColumnFiltersState>) => {\r\n      if (enableAdvancedFilter) return;\r\n\r\n      setColumnFilters((prev) => {\r\n        const next =\r\n          typeof updaterOrValue === 'function'\r\n            ? updaterOrValue(prev)\r\n            : updaterOrValue;\r\n\r\n        const filterUpdates = next.reduce<\r\n          Record<string, string | string[] | null>\r\n        >((acc, filter) => {\r\n          if (filterableColumns.find((column) => column.id === filter.id)) {\r\n            acc[filter.id] = filter.value as string | string[];\r\n          }\r\n          return acc;\r\n        }, {});\r\n\r\n        for (const prevFilter of prev) {\r\n          if (!next.some((filter) => filter.id === prevFilter.id)) {\r\n            filterUpdates[prevFilter.id] = null;\r\n          }\r\n        }\r\n\r\n        debouncedSetFilterValues(filterUpdates);\r\n        return next;\r\n      });\r\n    },\r\n    [debouncedSetFilterValues, filterableColumns, enableAdvancedFilter]\r\n  );\r\n\r\n  const table = useReactTable({\r\n    ...tableProps,\r\n    columns,\r\n    initialState,\r\n    pageCount,\r\n    state: {\r\n      pagination,\r\n      sorting,\r\n      columnVisibility,\r\n      rowSelection,\r\n      columnFilters\r\n    },\r\n    defaultColumn: {\r\n      ...tableProps.defaultColumn,\r\n      enableColumnFilter: false\r\n    },\r\n    enableRowSelection: true,\r\n    onRowSelectionChange: setRowSelection,\r\n    onPaginationChange,\r\n    onSortingChange,\r\n    onColumnFiltersChange,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getFilteredRowModel: getFilteredRowModel(),\r\n    getPaginationRowModel: getPaginationRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    getFacetedRowModel: getFacetedRowModel(),\r\n    getFacetedUniqueValues: getFacetedUniqueValues(),\r\n    getFacetedMinMaxValues: getFacetedMinMaxValues(),\r\n    manualPagination: true,\r\n    manualSorting: true,\r\n    manualFiltering: true\r\n  });\r\n\r\n  return { table, shallow, debounceMs, throttleMs };\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;AAAA;AAkBA;AASA;AAEA;AACA;;AAhCA;;;;;;AAmCA,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,WAAW;AACjB,MAAM,kBAAkB;AACxB,MAAM,cAAc;AACpB,MAAM,cAAc;AA0Bb,SAAS,aAAoB,KAA+B;;IACjE,MAAM,EACJ,OAAO,EACP,YAAY,CAAC,CAAC,EACd,YAAY,EACZ,UAAU,SAAS,EACnB,aAAa,WAAW,EACxB,aAAa,WAAW,EACxB,iBAAiB,KAAK,EACtB,uBAAuB,KAAK,EAC5B,SAAS,KAAK,EACd,UAAU,IAAI,EACd,eAAe,EACf,GAAG,YACJ,GAAG;IAEJ,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;mDAGpC,IAAM,CAAC;gBACL;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF,CAAC;kDACD;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EACnD,cAAc,gBAAgB,CAAC;IAEjC,MAAM,CAAC,kBAAkB,oBAAoB,GAC3C,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAmB,cAAc,oBAAoB,CAAC;IAErE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,uPAAA,CAAA,gBAAa,AAAD,EAClC,UACA,uPAAA,CAAA,iBAAc,CAAC,WAAW,CAAC,mBAAmB,WAAW,CAAC;IAE5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,uPAAA,CAAA,gBAAa,AAAD,EACxC,cACA,uPAAA,CAAA,iBAAc,CACX,WAAW,CAAC,mBACZ,WAAW,CAAC,cAAc,YAAY,YAAY;IAGvD,MAAM,aAA8B,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;4CAAE;YAChD,OAAO;gBACL,WAAW,OAAO;gBAClB,UAAU;YACZ;QACF;2CAAG;QAAC;QAAM;KAAQ;IAElB,MAAM,qBAAqB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;wDACzC,CAAC;YACC,IAAI,OAAO,mBAAmB,YAAY;gBACxC,MAAM,gBAAgB,eAAe;gBACrC,KAAK,QAAQ,cAAc,SAAS,GAAG;gBACvC,KAAK,WAAW,cAAc,QAAQ;YACxC,OAAO;gBACL,KAAK,QAAQ,eAAe,SAAS,GAAG;gBACxC,KAAK,WAAW,eAAe,QAAQ;YACzC;QACF;uDACA;QAAC;QAAY;QAAS;KAAW;IAGnC,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;2CAAE;YAC9B,OAAO,IAAI,IACT,QAAQ,GAAG;mDAAC,CAAC,SAAW,OAAO,EAAE;kDAAE,MAAM,CAAC;QAE9C;0CAAG;QAAC;KAAQ;IAEZ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,uPAAA,CAAA,gBAAa,AAAD,EACxC,UACA,CAAA,GAAA,wHAAA,CAAA,wBAAqB,AAAD,EAAS,WAC1B,WAAW,CAAC,mBACZ,WAAW,CAAC,cAAc,WAAW,EAAE;IAG5C,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;qDACtC,CAAC;YACC,IAAI,OAAO,mBAAmB,YAAY;gBACxC,MAAM,aAAa,eAAe;gBAClC,WAAW;YACb,OAAO;gBACL,WAAW;YACb;QACF;oDACA;QAAC;QAAS;KAAW;IAGvB,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;mDAAE;YACtC,IAAI,sBAAsB,OAAO,EAAE;YAEnC,OAAO,QAAQ,MAAM;2DAAC,CAAC,SAAW,OAAO,kBAAkB;;QAC7D;kDAAG;QAAC;QAAS;KAAqB;IAElC,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;+CAAE;YAClC,IAAI,sBAAsB,OAAO,CAAC;YAElC,OAAO,kBAAkB,MAAM;uDAE7B,CAAC,KAAK;oBACN,IAAI,OAAO,IAAI,EAAE,SAAS;wBACxB,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD,EAClC,uPAAA,CAAA,gBAAa,EACb,iBACA,WAAW,CAAC;oBAChB,OAAO;wBACL,GAAG,CAAC,OAAO,EAAE,IAAI,GAAG,GAAG,uPAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;oBACnD;oBACA,OAAO;gBACT;sDAAG,CAAC;QACN;8CAAG;QAAC;QAAmB;QAAmB;KAAqB;IAE/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,uPAAA,CAAA,iBAAc,AAAD,EAAE;IAEvD,MAAM,2BAA2B,CAAA,GAAA,+IAAA,CAAA,uBAAoB,AAAD;uEAClD,CAAC;YACC,KAAK,QAAQ;YACb,KAAK,gBAAgB;QACvB;sEACA;IAGF,MAAM,uBAA2C,CAAA,GAAA,4QAAA,CAAA,UAAa,AAAD;sDAAE;YAC7D,IAAI,sBAAsB,OAAO,EAAE;YAEnC,OAAO,OAAO,OAAO,CAAC,cAAc,MAAM;8DACxC,CAAC,SAAS,CAAC,KAAK,MAAM;oBACpB,IAAI,UAAU,MAAM;wBAClB,MAAM,iBAAiB,MAAM,OAAO,CAAC,SACjC,QACA,OAAO,UAAU,YAAY,eAAe,IAAI,CAAC,SAC/C,MAAM,KAAK,CAAC,iBAAiB,MAAM,CAAC,WACpC;4BAAC;yBAAM;wBAEb,QAAQ,IAAI,CAAC;4BACX,IAAI;4BACJ,OAAO;wBACT;oBACF;oBACA,OAAO;gBACT;6DACA,EAAE;QAEN;qDAAG;QAAC;QAAc;KAAqB;IAEvC,MAAM,CAAC,eAAe,iBAAiB,GACrC,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAsB;IAErC,MAAM,wBAAwB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;2DAC5C,CAAC;YACC,IAAI,sBAAsB;YAE1B;mEAAiB,CAAC;oBAChB,MAAM,OACJ,OAAO,mBAAmB,aACtB,eAAe,QACf;oBAEN,MAAM,gBAAgB,KAAK,MAAM;yFAE/B,CAAC,KAAK;4BACN,IAAI,kBAAkB,IAAI;iGAAC,CAAC,SAAW,OAAO,EAAE,KAAK,OAAO,EAAE;iGAAG;gCAC/D,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,OAAO,KAAK;4BAC/B;4BACA,OAAO;wBACT;wFAAG,CAAC;oBAEJ,KAAK,MAAM,cAAc,KAAM;wBAC7B,IAAI,CAAC,KAAK,IAAI;+EAAC,CAAC,SAAW,OAAO,EAAE,KAAK,WAAW,EAAE;+EAAG;4BACvD,aAAa,CAAC,WAAW,EAAE,CAAC,GAAG;wBACjC;oBACF;oBAEA,yBAAyB;oBACzB,OAAO;gBACT;;QACF;0DACA;QAAC;QAA0B;QAAmB;KAAqB;IAGrE,MAAM,QAAQ,CAAA,GAAA,mSAAA,CAAA,gBAAa,AAAD,EAAE;QAC1B,GAAG,UAAU;QACb;QACA;QACA;QACA,OAAO;YACL;YACA;YACA;YACA;YACA;QACF;QACA,eAAe;YACb,GAAG,WAAW,aAAa;YAC3B,oBAAoB;QACtB;QACA,oBAAoB;QACpB,sBAAsB;QACtB;QACA;QACA;QACA,0BAA0B;QAC1B,iBAAiB,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;QAC/B,qBAAqB,CAAA,GAAA,iPAAA,CAAA,sBAAmB,AAAD;QACvC,uBAAuB,CAAA,GAAA,iPAAA,CAAA,wBAAqB,AAAD;QAC3C,mBAAmB,CAAA,GAAA,iPAAA,CAAA,oBAAiB,AAAD;QACnC,oBAAoB,CAAA,GAAA,iPAAA,CAAA,qBAAkB,AAAD;QACrC,wBAAwB,CAAA,GAAA,iPAAA,CAAA,yBAAsB,AAAD;QAC7C,wBAAwB,CAAA,GAAA,iPAAA,CAAA,yBAAsB,AAAD;QAC7C,kBAAkB;QAClB,eAAe;QACf,iBAAiB;IACnB;IAEA,OAAO;QAAE;QAAO;QAAS;QAAY;IAAW;AAClD;GArOgB;;QA6CU,uPAAA,CAAA,gBAAa;QAIP,uPAAA,CAAA,gBAAa;QAkCb,uPAAA,CAAA,gBAAa;QA2CH,uPAAA,CAAA,iBAAc;QAErB,+IAAA,CAAA,uBAAoB;QAkEvC,mSAAA,CAAA,gBAAa", "debugId": null}}, {"offset": {"line": 3527, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as LabelPrimitive from '@radix-ui/react-label';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues\r\n} from 'react-hook-form';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { Label } from '@/components/ui/label';\r\n\r\nconst Form = FormProvider;\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName;\r\n};\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n);\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  );\r\n};\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext);\r\n  const itemContext = React.useContext(FormItemContext);\r\n  const { getFieldState } = useFormContext();\r\n  const formState = useFormState({ name: fieldContext.name });\r\n  const fieldState = getFieldState(fieldContext.name, formState);\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>');\r\n  }\r\n\r\n  const { id } = itemContext;\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState\r\n  };\r\n};\r\n\r\ntype FormItemContextValue = {\r\n  id: string;\r\n};\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n);\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId();\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot='form-item'\r\n        className={cn('grid gap-2', className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  );\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField();\r\n\r\n  return (\r\n    <Label\r\n      data-slot='form-label'\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } =\r\n    useFormField();\r\n\r\n  return (\r\n    <Slot\r\n      data-slot='form-control'\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField();\r\n\r\n  return (\r\n    <p\r\n      data-slot='form-description'\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField();\r\n  const body = error ? String(error?.message ?? '') : props.children;\r\n\r\n  if (!body) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot='form-message'\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  );\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AAUA;AACA;;;AAhBA;;;;;;AAkBA,MAAM,OAAO,0PAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,4SAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,4SAAC,0PAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,0PAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,YAAY,CAAA,GAAA,0PAAA,CAAA,eAAY,AAAD,EAAE;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,0PAAA,CAAA,iBAAc;QACtB,0PAAA,CAAA,eAAY;;;AAuBhC,MAAM,gCAAkB,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACpE,MAAM,KAAK,CAAA,GAAA,4QAAA,CAAA,QAAW,AAAD;IAErB,qBACE,4SAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,4SAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,4SAAC,oIAAA,CAAA,QAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,EAAE,GAAG,OAA0C;;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,4SAAC,uSAAA,CAAA,OAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAjBS;;QAEL;;;MAFK;AAmBT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,4SAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,4SAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 3729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/appointment-utils.ts"], "sourcesContent": ["// Appointment scheduling utilities and conflict detection\n\nimport { Appointment } from '@/types/clinic';\n\nexport interface AppointmentConflict {\n  hasConflict: boolean;\n  conflictingAppointment?: Appointment;\n  message?: string;\n}\n\nexport interface TimeSlot {\n  start: Date;\n  end: Date;\n}\n\n/**\n * Check if two time slots overlap\n */\nexport function doTimeSlotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {\n  return slot1.start < slot2.end && slot2.start < slot1.end;\n}\n\n/**\n * Create a time slot from appointment data\n */\nexport function createTimeSlot(appointmentDate: Date, durationInMinutes: number): TimeSlot {\n  const start = new Date(appointmentDate);\n  const end = new Date(start.getTime() + durationInMinutes * 60 * 1000);\n  return { start, end };\n}\n\n/**\n * Check for appointment conflicts with existing appointments\n */\nexport function checkAppointmentConflicts(\n  newAppointment: {\n    appointmentDate: Date;\n    durationInMinutes: number;\n    practitionerId: string;\n    id?: string; // For editing existing appointments\n  },\n  existingAppointments: Appointment[]\n): AppointmentConflict {\n  const newSlot = createTimeSlot(newAppointment.appointmentDate, newAppointment.durationInMinutes);\n  \n  // Filter appointments for the same practitioner on the same day\n  const samePractitionerAppointments = existingAppointments.filter(apt => {\n    // Skip the appointment being edited\n    if (newAppointment.id && apt.id === newAppointment.id) {\n      return false;\n    }\n    \n    // Only check appointments for the same practitioner\n    if (apt.practitioner.id !== newAppointment.practitionerId) {\n      return false;\n    }\n    \n    // Only check appointments that are not cancelled\n    if (apt.status === 'cancelled') {\n      return false;\n    }\n    \n    // Only check appointments on the same day\n    const aptDate = new Date(apt.appointmentDate);\n    const newDate = newAppointment.appointmentDate;\n    return (\n      aptDate.getFullYear() === newDate.getFullYear() &&\n      aptDate.getMonth() === newDate.getMonth() &&\n      aptDate.getDate() === newDate.getDate()\n    );\n  });\n  \n  // Check for overlaps\n  for (const existingApt of samePractitionerAppointments) {\n    const existingSlot = createTimeSlot(\n      new Date(existingApt.appointmentDate),\n      existingApt.durationInMinutes\n    );\n    \n    if (doTimeSlotsOverlap(newSlot, existingSlot)) {\n      return {\n        hasConflict: true,\n        conflictingAppointment: existingApt,\n        message: `This time slot conflicts with an existing appointment for ${existingApt.patient.fullName} from ${formatTime(existingSlot.start)} to ${formatTime(existingSlot.end)}.`\n      };\n    }\n  }\n  \n  return { hasConflict: false };\n}\n\n/**\n * Format time for display\n */\nexport function formatTime(date: Date): string {\n  return date.toLocaleTimeString('en-US', {\n    hour: 'numeric',\n    minute: '2-digit',\n    hour12: true\n  });\n}\n\n/**\n * Format date for display\n */\nexport function formatDate(date: Date): string {\n  return date.toLocaleDateString('en-US', {\n    weekday: 'long',\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n}\n\n/**\n * Get available time slots for a practitioner on a given date\n */\nexport function getAvailableTimeSlots(\n  date: Date,\n  practitionerId: string,\n  durationInMinutes: number,\n  existingAppointments: Appointment[],\n  businessHours: { start: number; end: number } = { start: 9, end: 17 } // 9 AM to 5 PM\n): TimeSlot[] {\n  const availableSlots: TimeSlot[] = [];\n  \n  // Get appointments for the practitioner on the given date\n  const dayAppointments = existingAppointments.filter(apt => {\n    if (apt.practitioner.id !== practitionerId || apt.status === 'cancelled') {\n      return false;\n    }\n    \n    const aptDate = new Date(apt.appointmentDate);\n    return (\n      aptDate.getFullYear() === date.getFullYear() &&\n      aptDate.getMonth() === date.getMonth() &&\n      aptDate.getDate() === date.getDate()\n    );\n  });\n  \n  // Sort appointments by time\n  dayAppointments.sort((a, b) => \n    new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime()\n  );\n  \n  // Create time slots from business hours\n  const startTime = new Date(date);\n  startTime.setHours(businessHours.start, 0, 0, 0);\n  \n  const endTime = new Date(date);\n  endTime.setHours(businessHours.end, 0, 0, 0);\n  \n  let currentTime = new Date(startTime);\n  \n  while (currentTime.getTime() + durationInMinutes * 60 * 1000 <= endTime.getTime()) {\n    const slotEnd = new Date(currentTime.getTime() + durationInMinutes * 60 * 1000);\n    const proposedSlot = { start: new Date(currentTime), end: slotEnd };\n    \n    // Check if this slot conflicts with any existing appointment\n    const hasConflict = dayAppointments.some(apt => {\n      const existingSlot = createTimeSlot(\n        new Date(apt.appointmentDate),\n        apt.durationInMinutes\n      );\n      return doTimeSlotsOverlap(proposedSlot, existingSlot);\n    });\n    \n    if (!hasConflict) {\n      availableSlots.push(proposedSlot);\n    }\n    \n    // Move to next 15-minute interval\n    currentTime.setMinutes(currentTime.getMinutes() + 15);\n  }\n  \n  return availableSlots;\n}\n\n/**\n * Suggest alternative time slots when there's a conflict\n */\nexport function suggestAlternativeSlots(\n  originalDate: Date,\n  practitionerId: string,\n  durationInMinutes: number,\n  existingAppointments: Appointment[],\n  maxSuggestions: number = 5\n): TimeSlot[] {\n  const suggestions: TimeSlot[] = [];\n  \n  // Try the same day first\n  const sameDaySlots = getAvailableTimeSlots(\n    originalDate,\n    practitionerId,\n    durationInMinutes,\n    existingAppointments\n  );\n  \n  suggestions.push(...sameDaySlots.slice(0, maxSuggestions));\n  \n  // If we need more suggestions, try the next few days\n  if (suggestions.length < maxSuggestions) {\n    for (let dayOffset = 1; dayOffset <= 7 && suggestions.length < maxSuggestions; dayOffset++) {\n      const nextDay = new Date(originalDate);\n      nextDay.setDate(nextDay.getDate() + dayOffset);\n      \n      // Skip weekends (optional - can be configured)\n      if (nextDay.getDay() === 0 || nextDay.getDay() === 6) {\n        continue;\n      }\n      \n      const nextDaySlots = getAvailableTimeSlots(\n        nextDay,\n        practitionerId,\n        durationInMinutes,\n        existingAppointments\n      );\n      \n      const remainingSlots = maxSuggestions - suggestions.length;\n      suggestions.push(...nextDaySlots.slice(0, remainingSlots));\n    }\n  }\n  \n  return suggestions;\n}\n\n/**\n * Validate appointment timing rules\n */\nexport function validateAppointmentTiming(appointmentDate: Date): {\n  isValid: boolean;\n  message?: string;\n} {\n  const now = new Date();\n  const appointmentTime = new Date(appointmentDate);\n  \n  // Check if appointment is in the past (allow up to 1 hour in the past for flexibility)\n  const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);\n  if (appointmentTime < oneHourAgo) {\n    return {\n      isValid: false,\n      message: 'Appointments cannot be scheduled more than 1 hour in the past.'\n    };\n  }\n  \n  // Check if appointment is too far in the future (e.g., 1 year)\n  const oneYearFromNow = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);\n  if (appointmentTime > oneYearFromNow) {\n    return {\n      isValid: false,\n      message: 'Appointments cannot be scheduled more than 1 year in advance.'\n    };\n  }\n  \n  // Check if appointment is during business hours (9 AM to 5 PM)\n  const hour = appointmentTime.getHours();\n  if (hour < 9 || hour >= 17) {\n    return {\n      isValid: false,\n      message: 'Appointments must be scheduled during business hours (9 AM to 5 PM).'\n    };\n  }\n  \n  // Check if appointment is on a weekend\n  const dayOfWeek = appointmentTime.getDay();\n  if (dayOfWeek === 0 || dayOfWeek === 6) {\n    return {\n      isValid: false,\n      message: 'Appointments cannot be scheduled on weekends.'\n    };\n  }\n  \n  return { isValid: true };\n}\n\n/**\n * Calculate appointment end time\n */\nexport function calculateEndTime(startTime: Date, durationInMinutes: number): Date {\n  return new Date(startTime.getTime() + durationInMinutes * 60 * 1000);\n}\n\n/**\n * Check if two appointments are on the same day\n */\nexport function isSameDay(date1: Date, date2: Date): boolean {\n  return (\n    date1.getFullYear() === date2.getFullYear() &&\n    date1.getMonth() === date2.getMonth() &&\n    date1.getDate() === date2.getDate()\n  );\n}\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;;;;;;;AAkBnD,SAAS,mBAAmB,KAAe,EAAE,KAAe;IACjE,OAAO,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,KAAK,GAAG,MAAM,GAAG;AAC3D;AAKO,SAAS,eAAe,eAAqB,EAAE,iBAAyB;IAC7E,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,IAAI,KAAK,MAAM,OAAO,KAAK,oBAAoB,KAAK;IAChE,OAAO;QAAE;QAAO;IAAI;AACtB;AAKO,SAAS,0BACd,cAKC,EACD,oBAAmC;IAEnC,MAAM,UAAU,eAAe,eAAe,eAAe,EAAE,eAAe,iBAAiB;IAE/F,gEAAgE;IAChE,MAAM,+BAA+B,qBAAqB,MAAM,CAAC,CAAA;QAC/D,oCAAoC;QACpC,IAAI,eAAe,EAAE,IAAI,IAAI,EAAE,KAAK,eAAe,EAAE,EAAE;YACrD,OAAO;QACT;QAEA,oDAAoD;QACpD,IAAI,IAAI,YAAY,CAAC,EAAE,KAAK,eAAe,cAAc,EAAE;YACzD,OAAO;QACT;QAEA,iDAAiD;QACjD,IAAI,IAAI,MAAM,KAAK,aAAa;YAC9B,OAAO;QACT;QAEA,0CAA0C;QAC1C,MAAM,UAAU,IAAI,KAAK,IAAI,eAAe;QAC5C,MAAM,UAAU,eAAe,eAAe;QAC9C,OACE,QAAQ,WAAW,OAAO,QAAQ,WAAW,MAC7C,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,MACvC,QAAQ,OAAO,OAAO,QAAQ,OAAO;IAEzC;IAEA,qBAAqB;IACrB,KAAK,MAAM,eAAe,6BAA8B;QACtD,MAAM,eAAe,eACnB,IAAI,KAAK,YAAY,eAAe,GACpC,YAAY,iBAAiB;QAG/B,IAAI,mBAAmB,SAAS,eAAe;YAC7C,OAAO;gBACL,aAAa;gBACb,wBAAwB;gBACxB,SAAS,CAAC,0DAA0D,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,aAAa,KAAK,EAAE,IAAI,EAAE,WAAW,aAAa,GAAG,EAAE,CAAC,CAAC;YACjL;QACF;IACF;IAEA,OAAO;QAAE,aAAa;IAAM;AAC9B;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,QAAQ;QACR,QAAQ;IACV;AACF;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,sBACd,IAAU,EACV,cAAsB,EACtB,iBAAyB,EACzB,oBAAmC,EACnC,gBAAgD;IAAE,OAAO;IAAG,KAAK;AAAG,EAAE,eAAe;AAAhB;IAErE,MAAM,iBAA6B,EAAE;IAErC,0DAA0D;IAC1D,MAAM,kBAAkB,qBAAqB,MAAM,CAAC,CAAA;QAClD,IAAI,IAAI,YAAY,CAAC,EAAE,KAAK,kBAAkB,IAAI,MAAM,KAAK,aAAa;YACxE,OAAO;QACT;QAEA,MAAM,UAAU,IAAI,KAAK,IAAI,eAAe;QAC5C,OACE,QAAQ,WAAW,OAAO,KAAK,WAAW,MAC1C,QAAQ,QAAQ,OAAO,KAAK,QAAQ,MACpC,QAAQ,OAAO,OAAO,KAAK,OAAO;IAEtC;IAEA,4BAA4B;IAC5B,gBAAgB,IAAI,CAAC,CAAC,GAAG,IACvB,IAAI,KAAK,EAAE,eAAe,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,eAAe,EAAE,OAAO;IAG7E,wCAAwC;IACxC,MAAM,YAAY,IAAI,KAAK;IAC3B,UAAU,QAAQ,CAAC,cAAc,KAAK,EAAE,GAAG,GAAG;IAE9C,MAAM,UAAU,IAAI,KAAK;IACzB,QAAQ,QAAQ,CAAC,cAAc,GAAG,EAAE,GAAG,GAAG;IAE1C,IAAI,cAAc,IAAI,KAAK;IAE3B,MAAO,YAAY,OAAO,KAAK,oBAAoB,KAAK,QAAQ,QAAQ,OAAO,GAAI;QACjF,MAAM,UAAU,IAAI,KAAK,YAAY,OAAO,KAAK,oBAAoB,KAAK;QAC1E,MAAM,eAAe;YAAE,OAAO,IAAI,KAAK;YAAc,KAAK;QAAQ;QAElE,6DAA6D;QAC7D,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA;YACvC,MAAM,eAAe,eACnB,IAAI,KAAK,IAAI,eAAe,GAC5B,IAAI,iBAAiB;YAEvB,OAAO,mBAAmB,cAAc;QAC1C;QAEA,IAAI,CAAC,aAAa;YAChB,eAAe,IAAI,CAAC;QACtB;QAEA,kCAAkC;QAClC,YAAY,UAAU,CAAC,YAAY,UAAU,KAAK;IACpD;IAEA,OAAO;AACT;AAKO,SAAS,wBACd,YAAkB,EAClB,cAAsB,EACtB,iBAAyB,EACzB,oBAAmC,EACnC,iBAAyB,CAAC;IAE1B,MAAM,cAA0B,EAAE;IAElC,yBAAyB;IACzB,MAAM,eAAe,sBACnB,cACA,gBACA,mBACA;IAGF,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG;IAE1C,qDAAqD;IACrD,IAAI,YAAY,MAAM,GAAG,gBAAgB;QACvC,IAAK,IAAI,YAAY,GAAG,aAAa,KAAK,YAAY,MAAM,GAAG,gBAAgB,YAAa;YAC1F,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;YAEpC,+CAA+C;YAC/C,IAAI,QAAQ,MAAM,OAAO,KAAK,QAAQ,MAAM,OAAO,GAAG;gBACpD;YACF;YAEA,MAAM,eAAe,sBACnB,SACA,gBACA,mBACA;YAGF,MAAM,iBAAiB,iBAAiB,YAAY,MAAM;YAC1D,YAAY,IAAI,IAAI,aAAa,KAAK,CAAC,GAAG;QAC5C;IACF;IAEA,OAAO;AACT;AAKO,SAAS,0BAA0B,eAAqB;IAI7D,MAAM,MAAM,IAAI;IAChB,MAAM,kBAAkB,IAAI,KAAK;IAEjC,uFAAuF;IACvF,MAAM,aAAa,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK;IACtD,IAAI,kBAAkB,YAAY;QAChC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,+DAA+D;IAC/D,MAAM,iBAAiB,IAAI,KAAK,IAAI,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK;IACrE,IAAI,kBAAkB,gBAAgB;QACpC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,+DAA+D;IAC/D,MAAM,OAAO,gBAAgB,QAAQ;IACrC,IAAI,OAAO,KAAK,QAAQ,IAAI;QAC1B,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,uCAAuC;IACvC,MAAM,YAAY,gBAAgB,MAAM;IACxC,IAAI,cAAc,KAAK,cAAc,GAAG;QACtC,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IAEA,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,iBAAiB,SAAe,EAAE,iBAAyB;IACzE,OAAO,IAAI,KAAK,UAAU,OAAO,KAAK,oBAAoB,KAAK;AACjE;AAKO,SAAS,UAAU,KAAW,EAAE,KAAW;IAChD,OACE,MAAM,WAAW,OAAO,MAAM,WAAW,MACzC,MAAM,QAAQ,OAAO,MAAM,QAAQ,MACnC,MAAM,OAAO,OAAO,MAAM,OAAO;AAErC", "debugId": null}}, {"offset": {"line": 3920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/calendar-utils.ts"], "sourcesContent": ["// Calendar utilities for appointment scheduling and data conversion\n// Handles conversion between Appointment data and Calendar events\n\nimport { format, addMinutes, parseISO } from 'date-fns';\nimport { zhCN } from 'date-fns/locale';\nimport { Appointment, CalendarEvent, TimeSlot, AppointmentConflict } from '@/types/clinic';\n\n/**\n * Convert appointments to calendar events for react-big-calendar\n */\nexport function appointmentsToCalendarEvents(appointments: Appointment[]): CalendarEvent[] {\n  return appointments.map((appointment) => {\n    const startDate = parseISO(appointment.appointmentDate);\n    const endDate = addMinutes(startDate, appointment.durationInMinutes);\n    \n    // Generate title based on appointment type\n    const title = generateEventTitle(appointment);\n    \n    return {\n      id: appointment.id,\n      title,\n      start: startDate,\n      end: endDate,\n      resource: appointment,\n      status: appointment.status,\n      appointmentType: appointment.appointmentType,\n    };\n  });\n}\n\n/**\n * Generate event title for calendar display\n */\nexport function generateEventTitle(appointment: Appointment): string {\n  const patientName = appointment.patient.fullName;\n  const typeLabel = appointment.appointmentType === 'consultation' ? '咨询' : '治疗';\n  \n  if (appointment.appointmentType === 'treatment' && appointment.treatment) {\n    return `${patientName} - ${appointment.treatment.name}`;\n  } else if (appointment.appointmentType === 'consultation') {\n    const consultationTypeMap = {\n      'initial': '初次咨询',\n      'follow-up': '复诊咨询',\n      'price-inquiry': '价格咨询'\n    };\n    const consultationType = appointment.consultationType \n      ? consultationTypeMap[appointment.consultationType] \n      : '咨询';\n    return `${patientName} - ${consultationType}`;\n  }\n  \n  return `${patientName} - ${typeLabel}`;\n}\n\n/**\n * Get status color for calendar events\n */\nexport function getStatusColor(status: string): string {\n  const statusColors = {\n    'scheduled': '#3b82f6', // blue\n    'confirmed': '#10b981', // green\n    'completed': '#6b7280', // gray\n    'cancelled': '#ef4444', // red\n    'no-show': '#f59e0b', // amber\n  };\n  \n  return statusColors[status as keyof typeof statusColors] || '#6b7280';\n}\n\n/**\n * Get appointment type color\n */\nexport function getAppointmentTypeColor(type: string): string {\n  const typeColors = {\n    'consultation': '#8b5cf6', // purple\n    'treatment': '#06b6d4', // cyan\n  };\n  \n  return typeColors[type as keyof typeof typeColors] || '#6b7280';\n}\n\n/**\n * Create time slot from date and duration\n */\nexport function createTimeSlot(appointmentDate: Date, durationInMinutes: number): TimeSlot {\n  const start = new Date(appointmentDate);\n  const end = addMinutes(start, durationInMinutes);\n  return { start, end };\n}\n\n/**\n * Check if two time slots overlap\n */\nexport function doTimeSlotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {\n  return slot1.start < slot2.end && slot2.start < slot1.end;\n}\n\n/**\n * Check for appointment conflicts\n */\nexport function checkAppointmentConflicts(\n  newAppointment: {\n    appointmentDate: Date;\n    durationInMinutes: number;\n    practitionerId: string;\n    id?: string; // For editing existing appointments\n  },\n  existingAppointments: Appointment[]\n): AppointmentConflict {\n  const newSlot = createTimeSlot(newAppointment.appointmentDate, newAppointment.durationInMinutes);\n  \n  // Filter appointments for the same practitioner, excluding cancelled/no-show\n  const samePractitionerAppointments = existingAppointments.filter(apt => \n    apt.practitioner.id === newAppointment.practitionerId &&\n    apt.status !== 'cancelled' &&\n    apt.status !== 'no-show' &&\n    apt.id !== newAppointment.id // Exclude current appointment when editing\n  );\n  \n  // Check for overlaps\n  for (const existingApt of samePractitionerAppointments) {\n    const existingSlot = createTimeSlot(\n      parseISO(existingApt.appointmentDate),\n      existingApt.durationInMinutes\n    );\n    \n    if (doTimeSlotsOverlap(newSlot, existingSlot)) {\n      return {\n        hasConflict: true,\n        conflictingAppointment: existingApt,\n        message: `此时间段与现有预约冲突：${existingApt.patient.fullName} (${format(existingSlot.start, 'HH:mm', { locale: zhCN })} - ${format(existingSlot.end, 'HH:mm', { locale: zhCN })})`\n      };\n    }\n  }\n  \n  return { hasConflict: false };\n}\n\n/**\n * Suggest alternative time slots when conflict occurs\n */\nexport function suggestAlternativeSlots(\n  preferredDate: Date,\n  practitionerId: string,\n  durationInMinutes: number,\n  existingAppointments: Appointment[],\n  maxSuggestions: number = 3\n): Date[] {\n  const suggestions: Date[] = [];\n  const baseDate = new Date(preferredDate);\n  \n  // Try slots every 30 minutes for the next 4 hours\n  for (let i = 1; i <= 8 && suggestions.length < maxSuggestions; i++) {\n    const candidateDate = addMinutes(baseDate, i * 30);\n    \n    const conflict = checkAppointmentConflicts(\n      {\n        appointmentDate: candidateDate,\n        durationInMinutes,\n        practitionerId,\n      },\n      existingAppointments\n    );\n    \n    if (!conflict.hasConflict) {\n      suggestions.push(candidateDate);\n    }\n  }\n  \n  return suggestions;\n}\n\n/**\n * Format time for display\n */\nexport function formatTime(date: Date): string {\n  return format(date, 'HH:mm', { locale: zhCN });\n}\n\n/**\n * Format date for display\n */\nexport function formatDate(date: Date): string {\n  return format(date, 'yyyy年MM月dd日', { locale: zhCN });\n}\n\n/**\n * Format date and time for display\n */\nexport function formatDateTime(date: Date): string {\n  return format(date, 'yyyy年MM月dd日 HH:mm', { locale: zhCN });\n}\n\n/**\n * Get calendar view labels in Chinese\n */\nexport const calendarLabels = {\n  month: '月视图',\n  week: '周视图',\n  work_week: '工作周',\n  day: '日视图',\n  agenda: '议程',\n  today: '今天',\n  previous: '上一个',\n  next: '下一个',\n  showMore: (total: number) => `+${total} 更多`,\n};\n\n/**\n * Get month names in Chinese\n */\nexport const monthNames = [\n  '一月', '二月', '三月', '四月', '五月', '六月',\n  '七月', '八月', '九月', '十月', '十一月', '十二月'\n];\n\n/**\n * Get day names in Chinese\n */\nexport const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];\n\n/**\n * Get short day names in Chinese\n */\nexport const dayNamesShort = ['日', '一', '二', '三', '四', '五', '六'];\n"], "names": [], "mappings": "AAAA,oEAAoE;AACpE,kEAAkE;;;;;;;;;;;;;;;;;;AAElE;AAAA;AAAA;AACA;;;AAMO,SAAS,6BAA6B,YAA2B;IACtE,OAAO,aAAa,GAAG,CAAC,CAAC;QACvB,MAAM,YAAY,CAAA,GAAA,kMAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,eAAe;QACtD,MAAM,UAAU,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE,WAAW,YAAY,iBAAiB;QAEnE,2CAA2C;QAC3C,MAAM,QAAQ,mBAAmB;QAEjC,OAAO;YACL,IAAI,YAAY,EAAE;YAClB;YACA,OAAO;YACP,KAAK;YACL,UAAU;YACV,QAAQ,YAAY,MAAM;YAC1B,iBAAiB,YAAY,eAAe;QAC9C;IACF;AACF;AAKO,SAAS,mBAAmB,WAAwB;IACzD,MAAM,cAAc,YAAY,OAAO,CAAC,QAAQ;IAChD,MAAM,YAAY,YAAY,eAAe,KAAK,iBAAiB,OAAO;IAE1E,IAAI,YAAY,eAAe,KAAK,eAAe,YAAY,SAAS,EAAE;QACxE,OAAO,GAAG,YAAY,GAAG,EAAE,YAAY,SAAS,CAAC,IAAI,EAAE;IACzD,OAAO,IAAI,YAAY,eAAe,KAAK,gBAAgB;QACzD,MAAM,sBAAsB;YAC1B,WAAW;YACX,aAAa;YACb,iBAAiB;QACnB;QACA,MAAM,mBAAmB,YAAY,gBAAgB,GACjD,mBAAmB,CAAC,YAAY,gBAAgB,CAAC,GACjD;QACJ,OAAO,GAAG,YAAY,GAAG,EAAE,kBAAkB;IAC/C;IAEA,OAAO,GAAG,YAAY,GAAG,EAAE,WAAW;AACxC;AAKO,SAAS,eAAe,MAAc;IAC3C,MAAM,eAAe;QACnB,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW;IACb;IAEA,OAAO,YAAY,CAAC,OAAoC,IAAI;AAC9D;AAKO,SAAS,wBAAwB,IAAY;IAClD,MAAM,aAAa;QACjB,gBAAgB;QAChB,aAAa;IACf;IAEA,OAAO,UAAU,CAAC,KAAgC,IAAI;AACxD;AAKO,SAAS,eAAe,eAAqB,EAAE,iBAAyB;IAC7E,MAAM,QAAQ,IAAI,KAAK;IACvB,MAAM,MAAM,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE,OAAO;IAC9B,OAAO;QAAE;QAAO;IAAI;AACtB;AAKO,SAAS,mBAAmB,KAAe,EAAE,KAAe;IACjE,OAAO,MAAM,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,KAAK,GAAG,MAAM,GAAG;AAC3D;AAKO,SAAS,0BACd,cAKC,EACD,oBAAmC;IAEnC,MAAM,UAAU,eAAe,eAAe,eAAe,EAAE,eAAe,iBAAiB;IAE/F,6EAA6E;IAC7E,MAAM,+BAA+B,qBAAqB,MAAM,CAAC,CAAA,MAC/D,IAAI,YAAY,CAAC,EAAE,KAAK,eAAe,cAAc,IACrD,IAAI,MAAM,KAAK,eACf,IAAI,MAAM,KAAK,aACf,IAAI,EAAE,KAAK,eAAe,EAAE,CAAC,2CAA2C;;IAG1E,qBAAqB;IACrB,KAAK,MAAM,eAAe,6BAA8B;QACtD,MAAM,eAAe,eACnB,CAAA,GAAA,kMAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,eAAe,GACpC,YAAY,iBAAiB;QAG/B,IAAI,mBAAmB,SAAS,eAAe;YAC7C,OAAO;gBACL,aAAa;gBACb,wBAAwB;gBACxB,SAAS,CAAC,YAAY,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,aAAa,KAAK,EAAE,SAAS;oBAAE,QAAQ,4MAAA,CAAA,OAAI;gBAAC,GAAG,GAAG,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,aAAa,GAAG,EAAE,SAAS;oBAAE,QAAQ,4MAAA,CAAA,OAAI;gBAAC,GAAG,CAAC,CAAC;YAC5K;QACF;IACF;IAEA,OAAO;QAAE,aAAa;IAAM;AAC9B;AAKO,SAAS,wBACd,aAAmB,EACnB,cAAsB,EACtB,iBAAyB,EACzB,oBAAmC,EACnC,iBAAyB,CAAC;IAE1B,MAAM,cAAsB,EAAE;IAC9B,MAAM,WAAW,IAAI,KAAK;IAE1B,kDAAkD;IAClD,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,YAAY,MAAM,GAAG,gBAAgB,IAAK;QAClE,MAAM,gBAAgB,CAAA,GAAA,oMAAA,CAAA,aAAU,AAAD,EAAE,UAAU,IAAI;QAE/C,MAAM,WAAW,0BACf;YACE,iBAAiB;YACjB;YACA;QACF,GACA;QAGF,IAAI,CAAC,SAAS,WAAW,EAAE;YACzB,YAAY,IAAI,CAAC;QACnB;IACF;IAEA,OAAO;AACT;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS;QAAE,QAAQ,4MAAA,CAAA,OAAI;IAAC;AAC9C;AAKO,SAAS,WAAW,IAAU;IACnC,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,eAAe;QAAE,QAAQ,4MAAA,CAAA,OAAI;IAAC;AACpD;AAKO,SAAS,eAAe,IAAU;IACvC,OAAO,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,qBAAqB;QAAE,QAAQ,4MAAA,CAAA,OAAI;IAAC;AAC1D;AAKO,MAAM,iBAAiB;IAC5B,OAAO;IACP,MAAM;IACN,WAAW;IACX,KAAK;IACL,QAAQ;IACR,OAAO;IACP,UAAU;IACV,MAAM;IACN,UAAU,CAAC,QAAkB,CAAC,CAAC,EAAE,MAAM,GAAG,CAAC;AAC7C;AAKO,MAAM,aAAa;IACxB;IAAM;IAAM;IAAM;IAAM;IAAM;IAC9B;IAAM;IAAM;IAAM;IAAM;IAAO;CAChC;AAKM,MAAM,WAAW;IAAC;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;CAAK;AAK3D,MAAM,gBAAgB;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI", "debugId": null}}, {"offset": {"line": 4114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/appointment-notifications.ts"], "sourcesContent": ["// Comprehensive toast notification utilities for appointment actions\n// Provides consistent messaging in Chinese for all appointment operations\n\nimport { toast } from 'sonner';\nimport { Appointment } from '@/types/clinic';\nimport { formatDateTime } from './calendar-utils';\n\nexport const appointmentNotifications = {\n  // Appointment CRUD notifications\n  appointment: {\n    created: (appointment: Appointment) => {\n      const typeLabel = appointment.appointmentType === 'consultation' ? '咨询预约' : '治疗预约';\n      toast.success(`${typeLabel}创建成功！`, {\n        description: `患者: ${appointment.patient.fullName} | 时间: ${formatDateTime(new Date(appointment.appointmentDate))}`,\n        duration: 4000,\n      });\n    },\n\n    updated: (appointment: Appointment) => {\n      const typeLabel = appointment.appointmentType === 'consultation' ? '咨询预约' : '治疗预约';\n      toast.success(`${typeLabel}更新成功！`, {\n        description: `患者: ${appointment.patient.fullName} | 时间: ${formatDateTime(new Date(appointment.appointmentDate))}`,\n        duration: 4000,\n      });\n    },\n\n    deleted: (patientName: string) => {\n      toast.success(`预约删除成功！`, {\n        description: `患者: ${patientName}`,\n        duration: 4000,\n      });\n    },\n\n    statusChanged: (appointment: Appointment, oldStatus: string, newStatus: string) => {\n      const statusMap = {\n        'scheduled': '已预约',\n        'confirmed': '已确认',\n        'completed': '已完成',\n        'cancelled': '已取消',\n        'no-show': '未到诊'\n      };\n      \n      const oldStatusText = statusMap[oldStatus as keyof typeof statusMap] || oldStatus;\n      const newStatusText = statusMap[newStatus as keyof typeof statusMap] || newStatus;\n      \n      toast.success(`预约状态更新成功！`, {\n        description: `${appointment.patient.fullName}: ${oldStatusText} → ${newStatusText}`,\n        duration: 4000,\n      });\n    },\n\n    batchStatusChanged: (count: number, status: string) => {\n      const statusMap = {\n        'confirmed': '已确认',\n        'completed': '已完成',\n        'cancelled': '已取消',\n      };\n      \n      const statusText = statusMap[status as keyof typeof statusMap] || status;\n      toast.success(`批量状态更新成功！`, {\n        description: `${count} 个预约已更新为 ${statusText}`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // Conflict and validation notifications\n  conflict: {\n    detected: (conflictMessage: string) => {\n      toast.warning(`时间冲突检测`, {\n        description: conflictMessage,\n        duration: 6000,\n      });\n    },\n\n    resolved: () => {\n      toast.success(`时间冲突已解决`, {\n        description: '预约时间已调整，无冲突',\n        duration: 3000,\n      });\n    },\n\n    alternativeSuggested: (count: number) => {\n      toast.info(`建议替代时间`, {\n        description: `为您找到 ${count} 个可选时间段`,\n        duration: 5000,\n      });\n    },\n  },\n\n  // Calendar view notifications\n  calendar: {\n    viewChanged: (view: string) => {\n      const viewMap = {\n        'month': '月视图',\n        'week': '周视图',\n        'day': '日视图',\n        'agenda': '议程视图'\n      };\n      \n      const viewText = viewMap[view as keyof typeof viewMap] || view;\n      toast.info(`切换到${viewText}`, {\n        duration: 2000,\n      });\n    },\n\n    dateNavigated: (date: Date) => {\n      toast.info(`导航到 ${formatDateTime(date)}`, {\n        duration: 2000,\n      });\n    },\n\n    eventSelected: (appointment: Appointment) => {\n      toast.info(`选中预约`, {\n        description: `${appointment.patient.fullName} - ${formatDateTime(new Date(appointment.appointmentDate))}`,\n        duration: 3000,\n      });\n    },\n  },\n\n  // Reminder notifications\n  reminder: {\n    upcoming: (appointment: Appointment, minutesUntil: number) => {\n      const timeText = minutesUntil < 60 \n        ? `${minutesUntil} 分钟后`\n        : `${Math.floor(minutesUntil / 60)} 小时后`;\n        \n      toast.info(`预约提醒`, {\n        description: `${appointment.patient.fullName} 的预约将在 ${timeText} 开始`,\n        duration: 8000,\n      });\n    },\n\n    overdue: (appointment: Appointment) => {\n      toast.error(`预约超时`, {\n        description: `${appointment.patient.fullName} 的预约时间已过，状态为: ${appointment.status}`,\n        duration: 10000,\n      });\n    },\n\n    reminderSet: (appointment: Appointment, reminderTime: string) => {\n      toast.success(`预约提醒已设置`, {\n        description: `将在 ${reminderTime} 提醒 ${appointment.patient.fullName} 的预约`,\n        duration: 4000,\n      });\n    },\n  },\n\n  // Form validation notifications\n  validation: {\n    invalidTime: (message: string) => {\n      toast.error(`时间验证失败`, {\n        description: message,\n        duration: 5000,\n      });\n    },\n\n    missingRequired: (fieldName: string) => {\n      toast.error(`必填字段缺失`, {\n        description: `请填写: ${fieldName}`,\n        duration: 4000,\n      });\n    },\n\n    invalidDuration: (duration: number) => {\n      toast.error(`预约时长无效`, {\n        description: `时长 ${duration} 分钟不在有效范围内 (5-480分钟)`,\n        duration: 4000,\n      });\n    },\n\n    pastDate: () => {\n      toast.error(`预约时间无效`, {\n        description: '预约时间不能早于当前时间超过24小时',\n        duration: 4000,\n      });\n    },\n  },\n\n  // Data loading notifications\n  loading: {\n    fetchingAppointments: () => {\n      return toast.loading(`加载预约数据中...`, {\n        duration: Infinity,\n      });\n    },\n\n    savingAppointment: (isEditing: boolean) => {\n      return toast.loading(`${isEditing ? '更新' : '创建'}预约中...`, {\n        duration: Infinity,\n      });\n    },\n\n    deletingAppointment: () => {\n      return toast.loading(`删除预约中...`, {\n        duration: Infinity,\n      });\n    },\n\n    checkingConflicts: () => {\n      return toast.loading(`检查时间冲突中...`, {\n        duration: Infinity,\n      });\n    },\n  },\n\n  // Error notifications\n  error: {\n    fetchFailed: () => {\n      toast.error(`获取预约数据失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n\n    saveFailed: (isEditing: boolean) => {\n      toast.error(`${isEditing ? '更新' : '创建'}预约失败`, {\n        description: '请稍后重试或联系管理员',\n        duration: 5000,\n      });\n    },\n\n    deleteFailed: () => {\n      toast.error(`删除预约失败`, {\n        description: '请稍后重试或联系管理员',\n        duration: 5000,\n      });\n    },\n\n    permissionDenied: (action: string) => {\n      toast.error(`权限不足`, {\n        description: `您没有权限执行: ${action}`,\n        duration: 5000,\n      });\n    },\n\n    networkError: () => {\n      toast.error(`网络连接失败`, {\n        description: '请检查网络连接后重试',\n        duration: 5000,\n      });\n    },\n  },\n\n  // Success notifications\n  success: {\n    dataRefreshed: () => {\n      toast.success(`预约数据刷新成功`, {\n        duration: 2000,\n      });\n    },\n\n    bulkOperation: (operation: string, count: number) => {\n      toast.success(`批量${operation}成功`, {\n        description: `已处理 ${count} 个预约`,\n        duration: 4000,\n      });\n    },\n\n    exportCompleted: (format: string) => {\n      toast.success(`预约数据导出成功`, {\n        description: `已导出为 ${format} 格式`,\n        duration: 4000,\n      });\n    },\n  },\n};\n\n// Utility function to dismiss all appointment-related toasts\nexport const dismissAppointmentToasts = () => {\n  toast.dismiss();\n};\n\n// Utility function to show custom appointment toast with consistent styling\nexport const showAppointmentToast = (\n  type: 'success' | 'error' | 'warning' | 'info',\n  title: string,\n  description?: string,\n  duration: number = 4000\n) => {\n  const toastFunction = toast[type];\n  toastFunction(title, {\n    description,\n    duration,\n  });\n};\n\n// Format loading message for appointments\nexport const formatLoadingMessage = (action: 'create' | 'update' | 'delete', entity: string) => {\n  const actionMap = {\n    create: '创建',\n    update: '更新',\n    delete: '删除'\n  };\n  \n  const entityMap = {\n    appointment: '预约'\n  };\n  \n  return `${actionMap[action]}${entityMap[entity as keyof typeof entityMap] || entity}中...`;\n};\n"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,0EAA0E;;;;;;;AAE1E;AAEA;;;AAEO,MAAM,2BAA2B;IACtC,iCAAiC;IACjC,aAAa;QACX,SAAS,CAAC;YACR,MAAM,YAAY,YAAY,eAAe,KAAK,iBAAiB,SAAS;YAC5E,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE;gBACjC,aAAa,CAAC,IAAI,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,YAAY,eAAe,IAAI;gBACjH,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,MAAM,YAAY,YAAY,eAAe,KAAK,iBAAiB,SAAS;YAC5E,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,UAAU,KAAK,CAAC,EAAE;gBACjC,aAAa,CAAC,IAAI,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,YAAY,eAAe,IAAI;gBACjH,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,IAAI,EAAE,aAAa;gBACjC,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,aAA0B,WAAmB;YAC3D,MAAM,YAAY;gBAChB,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,aAAa;gBACb,WAAW;YACb;YAEA,MAAM,gBAAgB,SAAS,CAAC,UAAoC,IAAI;YACxE,MAAM,gBAAgB,SAAS,CAAC,UAAoC,IAAI;YAExE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE;gBACzB,aAAa,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,cAAc,GAAG,EAAE,eAAe;gBACnF,UAAU;YACZ;QACF;QAEA,oBAAoB,CAAC,OAAe;YAClC,MAAM,YAAY;gBAChB,aAAa;gBACb,aAAa;gBACb,aAAa;YACf;YAEA,MAAM,aAAa,SAAS,CAAC,OAAiC,IAAI;YAClE,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,CAAC,EAAE;gBACzB,aAAa,GAAG,MAAM,SAAS,EAAE,YAAY;gBAC7C,UAAU;YACZ;QACF;IACF;IAEA,wCAAwC;IACxC,UAAU;QACR,UAAU,CAAC;YACT,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,EAAE;gBACtB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,UAAU;YACR,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,sBAAsB,CAAC;YACrB,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE;gBACnB,aAAa,CAAC,KAAK,EAAE,MAAM,OAAO,CAAC;gBACnC,UAAU;YACZ;QACF;IACF;IAEA,8BAA8B;IAC9B,UAAU;QACR,aAAa,CAAC;YACZ,MAAM,UAAU;gBACd,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,UAAU;YACZ;YAEA,MAAM,WAAW,OAAO,CAAC,KAA6B,IAAI;YAC1D,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE;gBAC3B,UAAU;YACZ;QACF;QAEA,eAAe,CAAC;YACd,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE;gBACxC,UAAU;YACZ;QACF;QAEA,eAAe,CAAC;YACd,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjB,aAAa,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,YAAY,eAAe,IAAI;gBACzG,UAAU;YACZ;QACF;IACF;IAEA,yBAAyB;IACzB,UAAU;QACR,UAAU,CAAC,aAA0B;YACnC,MAAM,WAAW,eAAe,KAC5B,GAAG,aAAa,IAAI,CAAC,GACrB,GAAG,KAAK,KAAK,CAAC,eAAe,IAAI,IAAI,CAAC;YAE1C,2QAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;gBACjB,aAAa,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,GAAG,CAAC;gBACnE,UAAU;YACZ;QACF;QAEA,SAAS,CAAC;YACR,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,cAAc,EAAE,YAAY,MAAM,EAAE;gBACjF,UAAU;YACZ;QACF;QAEA,aAAa,CAAC,aAA0B;YACtC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,EAAE;gBACvB,aAAa,CAAC,GAAG,EAAE,aAAa,IAAI,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC;gBACxE,UAAU;YACZ;QACF;IACF;IAEA,gCAAgC;IAChC,YAAY;QACV,aAAa,CAAC;YACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,CAAC,KAAK,EAAE,WAAW;gBAChC,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa,CAAC,GAAG,EAAE,SAAS,oBAAoB,CAAC;gBACjD,UAAU;YACZ;QACF;QAEA,UAAU;YACR,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,6BAA6B;IAC7B,SAAS;QACP,sBAAsB;YACpB,OAAO,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE;gBACjC,UAAU;YACZ;QACF;QAEA,mBAAmB,CAAC;YAClB,OAAO,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,YAAY,OAAO,KAAK,MAAM,CAAC,EAAE;gBACvD,UAAU;YACZ;QACF;QAEA,qBAAqB;YACnB,OAAO,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBAC/B,UAAU;YACZ;QACF;QAEA,mBAAmB;YACjB,OAAO,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,CAAC,EAAE;gBACjC,UAAU;YACZ;QACF;IACF;IAEA,sBAAsB;IACtB,OAAO;QACL,aAAa;YACX,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACtB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,YAAY,CAAC;YACX,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,YAAY,OAAO,KAAK,IAAI,CAAC,EAAE;gBAC5C,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,cAAc;YACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;QAEA,kBAAkB,CAAC;YACjB,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,EAAE;gBAClB,aAAa,CAAC,SAAS,EAAE,QAAQ;gBACjC,UAAU;YACZ;QACF;QAEA,cAAc;YACZ,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,EAAE;gBACpB,aAAa;gBACb,UAAU;YACZ;QACF;IACF;IAEA,wBAAwB;IACxB,SAAS;QACP,eAAe;YACb,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,UAAU;YACZ;QACF;QAEA,eAAe,CAAC,WAAmB;YACjC,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,UAAU,EAAE,CAAC,EAAE;gBAChC,aAAa,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC;gBAC/B,UAAU;YACZ;QACF;QAEA,iBAAiB,CAAC;YAChB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE;gBACxB,aAAa,CAAC,KAAK,EAAE,OAAO,GAAG,CAAC;gBAChC,UAAU;YACZ;QACF;IACF;AACF;AAGO,MAAM,2BAA2B;IACtC,2QAAA,CAAA,QAAK,CAAC,OAAO;AACf;AAGO,MAAM,uBAAuB,CAClC,MACA,OACA,aACA,WAAmB,IAAI;IAEvB,MAAM,gBAAgB,2QAAA,CAAA,QAAK,CAAC,KAAK;IACjC,cAAc,OAAO;QACnB;QACA;IACF;AACF;AAGO,MAAM,uBAAuB,CAAC,QAAwC;IAC3E,MAAM,YAAY;QAChB,QAAQ;QACR,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,YAAY;QAChB,aAAa;IACf;IAEA,OAAO,GAAG,SAAS,CAAC,OAAO,GAAG,SAAS,CAAC,OAAiC,IAAI,OAAO,IAAI,CAAC;AAC3F", "debugId": null}}, {"offset": {"line": 4380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointment-form-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useForm } from 'react-hook-form'\nimport { zodResolver } from '@hookform/resolvers/zod'\nimport * as z from 'zod'\nimport { format } from 'date-fns'\nimport { CalendarIcon } from 'lucide-react'\n\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/form'\nimport { Input } from '@/components/ui/input'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Calendar } from '@/components/ui/calendar'\nimport { cn } from '@/lib/utils'\nimport { appointmentsApi, patientsApi, treatmentsApi } from '@/lib/api'\nimport { Appointment, Patient, Treatment, User } from '@/types/clinic'\nimport { toast } from 'sonner'\nimport { checkAppointmentConflicts, validateAppointmentTiming, suggestAlternativeSlots } from '@/lib/appointment-utils'\nimport { appointmentNotifications } from '@/lib/appointment-notifications'\nimport { t } from '@/lib/translations'\n\nconst appointmentSchema = z.object({\n  appointmentType: z.enum(['consultation', 'treatment'], {\n    required_error: 'Please select appointment type',\n  }),\n  appointmentDate: z.date({\n    required_error: 'Please select an appointment date and time',\n  }).refine((date) => {\n    const now = new Date();\n    const minDate = new Date(now.getTime() - 24 * 60 * 60 * 1000); // Allow up to 24 hours in the past\n    return date >= minDate;\n  }, {\n    message: 'Appointment date cannot be more than 24 hours in the past',\n  }),\n  patientId: z.string()\n    .min(1, 'Please select a patient from the dropdown'),\n  treatmentId: z.string()\n    .optional(),\n  practitionerId: z.string()\n    .min(1, 'Please select a practitioner from the dropdown'),\n  price: z.number()\n    .min(0, 'Price must be $0 or greater')\n    .max(10000, 'Price cannot exceed $10,000')\n    .multipleOf(0.01, 'Price must be a valid currency amount')\n    .optional(),\n  durationInMinutes: z.number()\n    .min(5, 'Duration must be at least 5 minutes')\n    .max(480, 'Duration cannot exceed 8 hours (480 minutes)')\n    .int('Duration must be a whole number of minutes'),\n  status: z.enum(['scheduled', 'confirmed', 'completed', 'cancelled', 'no-show'], {\n    required_error: 'Please select an appointment status',\n  }),\n  consultationType: z.enum(['initial', 'follow-up', 'price-inquiry']).optional(),\n  interestedTreatments: z.array(z.string()).optional(),\n}).refine((data) => {\n  // Treatment appointments must have a treatment selected\n  if (data.appointmentType === 'treatment' && !data.treatmentId) {\n    return false;\n  }\n  return true;\n}, {\n  message: 'Treatment appointments must have a treatment selected',\n  path: ['treatmentId'],\n})\n\ntype AppointmentFormData = z.infer<typeof appointmentSchema>\n\ninterface AppointmentFormDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  appointment?: Appointment\n  onSuccess?: () => void\n}\n\nexport function AppointmentFormDialog({\n  open,\n  onOpenChange,\n  appointment,\n  onSuccess,\n}: AppointmentFormDialogProps) {\n  const [loading, setLoading] = useState(false)\n  const [patients, setPatients] = useState<Patient[]>([])\n  const [treatments, setTreatments] = useState<Treatment[]>([])\n  const [practitioners, setPractitioners] = useState<User[]>([])\n  const [existingAppointments, setExistingAppointments] = useState<Appointment[]>([])\n  const [conflictWarning, setConflictWarning] = useState<string | null>(null)\n  const [suggestedSlots, setSuggestedSlots] = useState<any[]>([])\n\n  const isEditing = !!appointment\n\n  const form = useForm<AppointmentFormData>({\n    resolver: zodResolver(appointmentSchema),\n    defaultValues: {\n      appointmentType: appointment?.appointmentType || 'consultation',\n      appointmentDate: appointment ? new Date(appointment.appointmentDate) : new Date(),\n      patientId: appointment?.patient.id || '',\n      treatmentId: appointment?.treatment?.id || '',\n      practitionerId: appointment?.practitioner.id || '',\n      price: appointment?.price || 0,\n      durationInMinutes: appointment?.durationInMinutes || 30,\n      status: appointment?.status || 'scheduled',\n      consultationType: appointment?.consultationType || 'initial',\n      interestedTreatments: appointment?.interestedTreatments?.map(t => t.id) || [],\n    },\n  })\n\n  // Load form data when dialog opens\n  useEffect(() => {\n    if (open) {\n      loadFormData()\n      if (appointment) {\n        form.reset({\n          appointmentDate: new Date(appointment.appointmentDate),\n          patientId: appointment.patient.id,\n          treatmentId: appointment.treatment.id,\n          practitionerId: appointment.practitioner.id,\n          price: appointment.price,\n          durationInMinutes: appointment.durationInMinutes,\n          status: appointment.status,\n        })\n      }\n    }\n  }, [open, appointment, form])\n\n  const loadFormData = async () => {\n    try {\n      const [patientsResponse, treatmentsResponse, appointmentsResponse] = await Promise.all([\n        patientsApi.getAll({ limit: 100 }),\n        treatmentsApi.getAll({ limit: 100 }),\n        appointmentsApi.getAll({ limit: 1000 }), // Load more appointments for conflict checking\n      ])\n\n      setPatients(patientsResponse.docs)\n      setTreatments(treatmentsResponse.docs)\n      setExistingAppointments(appointmentsResponse.docs)\n      \n      // For now, we'll use a mock practitioner list\n      // In a real app, you'd fetch this from the users API\n      setPractitioners([\n        {\n          id: 'user-1',\n          email: '<EMAIL>',\n          role: 'doctor',\n          clerkId: 'clerk-1',\n          firstName: 'Dr. Jane',\n          lastName: 'Smith',\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        },\n      ])\n    } catch (error) {\n      console.error('Failed to load form data:', error)\n      toast.error('Failed to load form data')\n    }\n  }\n\n  // Auto-fill price and duration when treatment changes\n  const watchTreatment = form.watch('treatmentId')\n  useEffect(() => {\n    if (watchTreatment && !isEditing) {\n      const selectedTreatment = treatments.find(t => t.id === watchTreatment)\n      if (selectedTreatment) {\n        form.setValue('price', selectedTreatment.defaultPrice)\n        form.setValue('durationInMinutes', selectedTreatment.defaultDurationInMinutes)\n      }\n    }\n  }, [watchTreatment, treatments, form, isEditing])\n\n  // Check for appointment conflicts when relevant fields change\n  const appointmentDate = form.watch('appointmentDate')\n  const durationInMinutes = form.watch('durationInMinutes')\n  const practitionerId = form.watch('practitionerId')\n\n  useEffect(() => {\n\n    if (appointmentDate && durationInMinutes && practitionerId && existingAppointments.length > 0) {\n      // Validate appointment timing\n      const timingValidation = validateAppointmentTiming(appointmentDate)\n      if (!timingValidation.isValid) {\n        setConflictWarning(timingValidation.message || 'Invalid appointment time')\n        setSuggestedSlots([])\n        return\n      }\n\n      // Check for conflicts\n      const conflictCheck = checkAppointmentConflicts(\n        {\n          appointmentDate,\n          durationInMinutes,\n          practitionerId,\n          id: appointment?.id, // For editing\n        },\n        existingAppointments\n      )\n\n      if (conflictCheck.hasConflict) {\n        setConflictWarning(conflictCheck.message || '检测到时间段冲突')\n\n        // Suggest alternative slots\n        const alternatives = suggestAlternativeSlots(\n          appointmentDate,\n          practitionerId,\n          durationInMinutes,\n          existingAppointments,\n          3\n        )\n        setSuggestedSlots(alternatives)\n      } else {\n        setConflictWarning(null)\n        setSuggestedSlots([])\n      }\n    } else {\n      setConflictWarning(null)\n      setSuggestedSlots([])\n    }\n  }, [appointmentDate, durationInMinutes, practitionerId, existingAppointments, appointment?.id])\n\n  const onSubmit = async (data: AppointmentFormData) => {\n    setLoading(true)\n    try {\n      const appointmentData = {\n        appointmentType: data.appointmentType,\n        appointmentDate: data.appointmentDate.toISOString(),\n        patient: data.patientId,\n        treatment: data.treatmentId || undefined,\n        practitioner: data.practitionerId,\n        price: data.price || 0,\n        durationInMinutes: data.durationInMinutes,\n        status: data.status,\n        consultationType: data.appointmentType === 'consultation' ? data.consultationType : undefined,\n        interestedTreatments: data.appointmentType === 'consultation' ? data.interestedTreatments : undefined,\n      }\n\n      let result;\n      if (isEditing) {\n        result = await appointmentsApi.update(appointment.id, appointmentData)\n        appointmentNotifications.appointment.updated(result)\n      } else {\n        result = await appointmentsApi.create(appointmentData)\n        appointmentNotifications.appointment.created(result)\n      }\n\n      onSuccess?.()\n      onOpenChange(false)\n      form.reset()\n    } catch (error) {\n      console.error('Failed to save appointment:', error)\n      appointmentNotifications.error.saveFailed(isEditing)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent className=\"sm:max-w-[600px]\">\n        <DialogHeader>\n          <DialogTitle>\n            {isEditing ? 'Edit Appointment' : 'New Appointment'}\n          </DialogTitle>\n          <DialogDescription>\n            {isEditing\n              ? 'Update the appointment details below.'\n              : 'Fill in the details to schedule a new appointment.'}\n          </DialogDescription>\n        </DialogHeader>\n\n        <Form {...form}>\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\n            <div className=\"grid grid-cols-2 gap-4\">\n              {/* Patient Selection */}\n              <FormField\n                control={form.control}\n                name=\"patientId\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Patient</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"Select patient\" />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {patients.map((patient) => (\n                          <SelectItem key={patient.id} value={patient.id}>\n                            {patient.fullName}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Treatment Selection */}\n              <FormField\n                control={form.control}\n                name=\"treatmentId\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Treatment</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue placeholder=\"Select treatment\" />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        {treatments.map((treatment) => (\n                          <SelectItem key={treatment.id} value={treatment.id}>\n                            {treatment.name}\n                          </SelectItem>\n                        ))}\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Appointment Date */}\n            <FormField\n              control={form.control}\n              name=\"appointmentDate\"\n              render={({ field }) => (\n                <FormItem className=\"flex flex-col\">\n                  <FormLabel>Appointment Date & Time</FormLabel>\n                  <Popover>\n                    <PopoverTrigger asChild>\n                      <FormControl>\n                        <Button\n                          variant=\"outline\"\n                          className={cn(\n                            'w-full pl-3 text-left font-normal',\n                            !field.value && 'text-muted-foreground'\n                          )}\n                        >\n                          {field.value ? (\n                            format(field.value, 'PPP p')\n                          ) : (\n                            <span>Pick a date and time</span>\n                          )}\n                          <CalendarIcon className=\"ml-auto h-4 w-4 opacity-50\" />\n                        </Button>\n                      </FormControl>\n                    </PopoverTrigger>\n                    <PopoverContent className=\"w-auto p-0\" align=\"start\">\n                      <Calendar\n                        mode=\"single\"\n                        selected={field.value}\n                        onSelect={field.onChange}\n                        disabled={(date) =>\n                          date < new Date() || date < new Date('1900-01-01')\n                        }\n                        initialFocus\n                      />\n                    </PopoverContent>\n                  </Popover>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            <div className=\"grid grid-cols-3 gap-4\">\n              {/* Price */}\n              <FormField\n                control={form.control}\n                name=\"price\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Price ($)</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"number\"\n                        step=\"0.01\"\n                        {...field}\n                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Duration */}\n              <FormField\n                control={form.control}\n                name=\"durationInMinutes\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Duration (min)</FormLabel>\n                    <FormControl>\n                      <Input\n                        type=\"number\"\n                        {...field}\n                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}\n                      />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              {/* Status */}\n              <FormField\n                control={form.control}\n                name=\"status\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Status</FormLabel>\n                    <Select onValueChange={field.onChange} defaultValue={field.value}>\n                      <FormControl>\n                        <SelectTrigger>\n                          <SelectValue />\n                        </SelectTrigger>\n                      </FormControl>\n                      <SelectContent>\n                        <SelectItem value=\"scheduled\">Scheduled</SelectItem>\n                        <SelectItem value=\"completed\">Completed</SelectItem>\n                        <SelectItem value=\"cancelled\">Cancelled</SelectItem>\n                      </SelectContent>\n                    </Select>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n            </div>\n\n            {/* Practitioner Selection */}\n            <FormField\n              control={form.control}\n              name=\"practitionerId\"\n              render={({ field }) => (\n                <FormItem>\n                  <FormLabel>Practitioner</FormLabel>\n                  <Select onValueChange={field.onChange} defaultValue={field.value}>\n                    <FormControl>\n                      <SelectTrigger>\n                        <SelectValue placeholder=\"Select practitioner\" />\n                      </SelectTrigger>\n                    </FormControl>\n                    <SelectContent>\n                      {practitioners.map((practitioner) => (\n                        <SelectItem key={practitioner.id} value={practitioner.id}>\n                          {practitioner.firstName} {practitioner.lastName}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <FormMessage />\n                </FormItem>\n              )}\n            />\n\n            <DialogFooter>\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={() => onOpenChange(false)}\n                disabled={loading}\n              >\n                Cancel\n              </Button>\n              <Button type=\"submit\" disabled={loading}>\n                {loading ? 'Saving...' : isEditing ? 'Update' : 'Create'}\n              </Button>\n            </DialogFooter>\n          </form>\n        </Form>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAQA;AAQA;AACA;AAOA;AAKA;AACA;AACA;AAEA;AACA;AACA;;;AA7CA;;;;;;;;;;;;;;;;;;;AAgDA,MAAM,oBAAoB,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,EAAE;IACjC,iBAAiB,CAAA,GAAA,wLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAgB;KAAY,EAAE;QACrD,gBAAgB;IAClB;IACA,iBAAiB,CAAA,GAAA,wLAAA,CAAA,OAAM,AAAD,EAAE;QACtB,gBAAgB;IAClB,GAAG,MAAM,CAAC,CAAC;QACT,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,OAAO,mCAAmC;QAClG,OAAO,QAAQ;IACjB,GAAG;QACD,SAAS;IACX;IACA,WAAW,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,IACf,GAAG,CAAC,GAAG;IACV,aAAa,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,IACjB,QAAQ;IACX,gBAAgB,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,IACpB,GAAG,CAAC,GAAG;IACV,OAAO,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,IACX,GAAG,CAAC,GAAG,+BACP,GAAG,CAAC,OAAO,+BACX,UAAU,CAAC,MAAM,yCACjB,QAAQ;IACX,mBAAmB,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,IACvB,GAAG,CAAC,GAAG,uCACP,GAAG,CAAC,KAAK,gDACT,GAAG,CAAC;IACP,QAAQ,CAAA,GAAA,wLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAa;QAAa;QAAa;QAAa;KAAU,EAAE;QAC9E,gBAAgB;IAClB;IACA,kBAAkB,CAAA,GAAA,wLAAA,CAAA,OAAM,AAAD,EAAE;QAAC;QAAW;QAAa;KAAgB,EAAE,QAAQ;IAC5E,sBAAsB,CAAA,GAAA,wLAAA,CAAA,QAAO,AAAD,EAAE,CAAA,GAAA,wLAAA,CAAA,SAAQ,AAAD,KAAK,QAAQ;AACpD,GAAG,MAAM,CAAC,CAAC;IACT,wDAAwD;IACxD,IAAI,KAAK,eAAe,KAAK,eAAe,CAAC,KAAK,WAAW,EAAE;QAC7D,OAAO;IACT;IACA,OAAO;AACT,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAc;AACvB;AAWO,SAAS,sBAAsB,EACpC,IAAI,EACJ,YAAY,EACZ,WAAW,EACX,SAAS,EACkB;;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE9D,MAAM,YAAY,CAAC,CAAC;IAEpB,MAAM,OAAO,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAuB;QACxC,UAAU,CAAA,GAAA,2QAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,iBAAiB,aAAa,mBAAmB;YACjD,iBAAiB,cAAc,IAAI,KAAK,YAAY,eAAe,IAAI,IAAI;YAC3E,WAAW,aAAa,QAAQ,MAAM;YACtC,aAAa,aAAa,WAAW,MAAM;YAC3C,gBAAgB,aAAa,aAAa,MAAM;YAChD,OAAO,aAAa,SAAS;YAC7B,mBAAmB,aAAa,qBAAqB;YACrD,QAAQ,aAAa,UAAU;YAC/B,kBAAkB,aAAa,oBAAoB;YACnD,sBAAsB,aAAa,sBAAsB;uDAAI,CAAA,IAAK,EAAE,EAAE;yDAAK,EAAE;QAC/E;IACF;IAEA,mCAAmC;IACnC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,MAAM;gBACR;gBACA,IAAI,aAAa;oBACf,KAAK,KAAK,CAAC;wBACT,iBAAiB,IAAI,KAAK,YAAY,eAAe;wBACrD,WAAW,YAAY,OAAO,CAAC,EAAE;wBACjC,aAAa,YAAY,SAAS,CAAC,EAAE;wBACrC,gBAAgB,YAAY,YAAY,CAAC,EAAE;wBAC3C,OAAO,YAAY,KAAK;wBACxB,mBAAmB,YAAY,iBAAiB;wBAChD,QAAQ,YAAY,MAAM;oBAC5B;gBACF;YACF;QACF;0CAAG;QAAC;QAAM;QAAa;KAAK;IAE5B,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,CAAC,kBAAkB,oBAAoB,qBAAqB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACrF,oHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAChC,oHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAI;gBAClC,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAK;aACtC;YAED,YAAY,iBAAiB,IAAI;YACjC,cAAc,mBAAmB,IAAI;YACrC,wBAAwB,qBAAqB,IAAI;YAEjD,8CAA8C;YAC9C,qDAAqD;YACrD,iBAAiB;gBACf;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,SAAS;oBACT,WAAW;oBACX,UAAU;oBACV,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;aACD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,sDAAsD;IACtD,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,kBAAkB,CAAC,WAAW;gBAChC,MAAM,oBAAoB,WAAW,IAAI;yEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gBACxD,IAAI,mBAAmB;oBACrB,KAAK,QAAQ,CAAC,SAAS,kBAAkB,YAAY;oBACrD,KAAK,QAAQ,CAAC,qBAAqB,kBAAkB,wBAAwB;gBAC/E;YACF;QACF;0CAAG;QAAC;QAAgB;QAAY;QAAM;KAAU;IAEhD,8DAA8D;IAC9D,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,MAAM,oBAAoB,KAAK,KAAK,CAAC;IACrC,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAElC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2CAAE;YAER,IAAI,mBAAmB,qBAAqB,kBAAkB,qBAAqB,MAAM,GAAG,GAAG;gBAC7F,8BAA8B;gBAC9B,MAAM,mBAAmB,CAAA,GAAA,qIAAA,CAAA,4BAAyB,AAAD,EAAE;gBACnD,IAAI,CAAC,iBAAiB,OAAO,EAAE;oBAC7B,mBAAmB,iBAAiB,OAAO,IAAI;oBAC/C,kBAAkB,EAAE;oBACpB;gBACF;gBAEA,sBAAsB;gBACtB,MAAM,gBAAgB,CAAA,GAAA,qIAAA,CAAA,4BAAyB,AAAD,EAC5C;oBACE;oBACA;oBACA;oBACA,IAAI,aAAa;gBACnB,GACA;gBAGF,IAAI,cAAc,WAAW,EAAE;oBAC7B,mBAAmB,cAAc,OAAO,IAAI;oBAE5C,4BAA4B;oBAC5B,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,0BAAuB,AAAD,EACzC,iBACA,gBACA,mBACA,sBACA;oBAEF,kBAAkB;gBACpB,OAAO;oBACL,mBAAmB;oBACnB,kBAAkB,EAAE;gBACtB;YACF,OAAO;gBACL,mBAAmB;gBACnB,kBAAkB,EAAE;YACtB;QACF;0CAAG;QAAC;QAAiB;QAAmB;QAAgB;QAAsB,aAAa;KAAG;IAE9F,MAAM,WAAW,OAAO;QACtB,WAAW;QACX,IAAI;YACF,MAAM,kBAAkB;gBACtB,iBAAiB,KAAK,eAAe;gBACrC,iBAAiB,KAAK,eAAe,CAAC,WAAW;gBACjD,SAAS,KAAK,SAAS;gBACvB,WAAW,KAAK,WAAW,IAAI;gBAC/B,cAAc,KAAK,cAAc;gBACjC,OAAO,KAAK,KAAK,IAAI;gBACrB,mBAAmB,KAAK,iBAAiB;gBACzC,QAAQ,KAAK,MAAM;gBACnB,kBAAkB,KAAK,eAAe,KAAK,iBAAiB,KAAK,gBAAgB,GAAG;gBACpF,sBAAsB,KAAK,eAAe,KAAK,iBAAiB,KAAK,oBAAoB,GAAG;YAC9F;YAEA,IAAI;YACJ,IAAI,WAAW;gBACb,SAAS,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;gBACtD,6IAAA,CAAA,2BAAwB,CAAC,WAAW,CAAC,OAAO,CAAC;YAC/C,OAAO;gBACL,SAAS,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;gBACtC,6IAAA,CAAA,2BAAwB,CAAC,WAAW,CAAC,OAAO,CAAC;YAC/C;YAEA;YACA,aAAa;YACb,KAAK,KAAK;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6IAAA,CAAA,2BAAwB,CAAC,KAAK,CAAC,UAAU,CAAC;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,4SAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,4SAAC,qIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,4SAAC,qIAAA,CAAA,eAAY;;sCACX,4SAAC,qIAAA,CAAA,cAAW;sCACT,YAAY,qBAAqB;;;;;;sCAEpC,4SAAC,qIAAA,CAAA,oBAAiB;sCACf,YACG,0CACA;;;;;;;;;;;;8BAIR,4SAAC,mIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,4SAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CACrD,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;kEACP,4SAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,4SAAC,qIAAA,CAAA,SAAM;wDAAC,eAAe,MAAM,QAAQ;wDAAE,cAAc,MAAM,KAAK;;0EAC9D,4SAAC,mIAAA,CAAA,cAAW;0EACV,cAAA,4SAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,4SAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;;;;;;0EAG7B,4SAAC,qIAAA,CAAA,gBAAa;0EACX,SAAS,GAAG,CAAC,CAAC,wBACb,4SAAC,qIAAA,CAAA,aAAU;wEAAkB,OAAO,QAAQ,EAAE;kFAC3C,QAAQ,QAAQ;uEADF,QAAQ,EAAE;;;;;;;;;;;;;;;;kEAMjC,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,4SAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;kEACP,4SAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,4SAAC,qIAAA,CAAA,SAAM;wDAAC,eAAe,MAAM,QAAQ;wDAAE,cAAc,MAAM,KAAK;;0EAC9D,4SAAC,mIAAA,CAAA,cAAW;0EACV,cAAA,4SAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,4SAAC,qIAAA,CAAA,cAAW;wEAAC,aAAY;;;;;;;;;;;;;;;;0EAG7B,4SAAC,qIAAA,CAAA,gBAAa;0EACX,WAAW,GAAG,CAAC,CAAC,0BACf,4SAAC,qIAAA,CAAA,aAAU;wEAAoB,OAAO,UAAU,EAAE;kFAC/C,UAAU,IAAI;uEADA,UAAU,EAAE;;;;;;;;;;;;;;;;kEAMnC,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,4SAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,4SAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4SAAC,sIAAA,CAAA,UAAO;;kEACN,4SAAC,sIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,4SAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,4SAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,CAAC,MAAM,KAAK,IAAI;;oEAGjB,MAAM,KAAK,GACV,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,EAAE,yBAEpB,4SAAC;kFAAK;;;;;;kFAER,4SAAC,qSAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAI9B,4SAAC,sIAAA,CAAA,iBAAc;wDAAC,WAAU;wDAAa,OAAM;kEAC3C,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4DACP,MAAK;4DACL,UAAU,MAAM,KAAK;4DACrB,UAAU,MAAM,QAAQ;4DACxB,UAAU,CAAC,OACT,OAAO,IAAI,UAAU,OAAO,IAAI,KAAK;4DAEvC,YAAY;;;;;;;;;;;;;;;;;0DAIlB,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;kEACP,4SAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,4SAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,4SAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACL,MAAK;4DACJ,GAAG,KAAK;4DACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAGlE,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,4SAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;kEACP,4SAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,4SAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,4SAAC,oIAAA,CAAA,QAAK;4DACJ,MAAK;4DACJ,GAAG,KAAK;4DACT,UAAU,CAAC,IAAM,MAAM,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;;;;;;;;;;;kEAGhE,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAMlB,4SAAC,mIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;kEACP,4SAAC,mIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,4SAAC,qIAAA,CAAA,SAAM;wDAAC,eAAe,MAAM,QAAQ;wDAAE,cAAc,MAAM,KAAK;;0EAC9D,4SAAC,mIAAA,CAAA,cAAW;0EACV,cAAA,4SAAC,qIAAA,CAAA,gBAAa;8EACZ,cAAA,4SAAC,qIAAA,CAAA,cAAW;;;;;;;;;;;;;;;0EAGhB,4SAAC,qIAAA,CAAA,gBAAa;;kFACZ,4SAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,4SAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;kFAC9B,4SAAC,qIAAA,CAAA,aAAU;wEAAC,OAAM;kFAAY;;;;;;;;;;;;;;;;;;kEAGlC,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAOpB,4SAAC,mIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,4SAAC,mIAAA,CAAA,WAAQ;;0DACP,4SAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,4SAAC,qIAAA,CAAA,SAAM;gDAAC,eAAe,MAAM,QAAQ;gDAAE,cAAc,MAAM,KAAK;;kEAC9D,4SAAC,mIAAA,CAAA,cAAW;kEACV,cAAA,4SAAC,qIAAA,CAAA,gBAAa;sEACZ,cAAA,4SAAC,qIAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;;;;;;kEAG7B,4SAAC,qIAAA,CAAA,gBAAa;kEACX,cAAc,GAAG,CAAC,CAAC,6BAClB,4SAAC,qIAAA,CAAA,aAAU;gEAAuB,OAAO,aAAa,EAAE;;oEACrD,aAAa,SAAS;oEAAC;oEAAE,aAAa,QAAQ;;+DADhC,aAAa,EAAE;;;;;;;;;;;;;;;;0DAMtC,4SAAC,mIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,4SAAC,qIAAA,CAAA,eAAY;;kDACX,4SAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa;wCAC5B,UAAU;kDACX;;;;;;kDAGD,4SAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAS,UAAU;kDAC7B,UAAU,cAAc,YAAY,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhE;GAjZgB;;QAgBD,0PAAA,CAAA,UAAO;;;KAhBN", "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card'\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-header'\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-title'\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-action'\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-content'\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='card-footer'\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 5370, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointment-calendar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useMemo, useCallback } from 'react'\nimport { Calendar, dateFnsLocalizer, View } from 'react-big-calendar'\nimport { format, parse, startOfWeek, getDay } from 'date-fns'\nimport { zhCN } from 'date-fns/locale'\nimport 'react-big-calendar/lib/css/react-big-calendar.css'\nimport '@/styles/calendar.css'\n\nimport { Button } from '@/components/ui/button'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport {\n  IconCalendar,\n  IconList,\n  IconChevronLeft,\n  IconChevronRight,\n  IconPlus,\n  IconEdit,\n  IconTrash,\n  IconRefresh\n} from '@tabler/icons-react'\nimport { cn } from '@/lib/utils'\nimport { Appointment, CalendarEvent } from '@/types/clinic'\nimport { appointmentsApi } from '@/lib/api'\nimport { toast } from 'sonner'\nimport { formatApiError } from '@/lib/error-utils'\nimport { appointmentNotifications } from '@/lib/appointment-notifications'\nimport {\n  appointmentsToCalendarEvents,\n  getStatusColor,\n  getAppointmentTypeColor,\n  calendarLabels,\n  monthNames,\n  dayNames,\n  dayNamesShort\n} from '@/lib/calendar-utils'\n\n// Setup the localizer for react-big-calendar with Chinese locale\nconst locales = {\n  'zh-CN': zhCN,\n}\n\nconst localizer = dateFnsLocalizer({\n  format,\n  parse,\n  startOfWeek: () => startOfWeek(new Date(), { locale: zhCN }),\n  getDay,\n  locales,\n})\n\ninterface AppointmentCalendarProps {\n  onNewAppointment?: () => void\n  onEditAppointment?: (appointment: Appointment) => void\n  onDeleteAppointment?: (appointment: Appointment) => void\n}\n\nexport function AppointmentCalendar({\n  onNewAppointment,\n  onEditAppointment,\n  onDeleteAppointment,\n}: AppointmentCalendarProps) {\n  const [appointments, setAppointments] = useState<Appointment[]>([])\n  const [loading, setLoading] = useState(true)\n  const [view, setView] = useState<View>('month')\n  const [date, setDate] = useState(new Date())\n\n  // Convert appointments to calendar events\n  const events = useMemo(() => {\n    return appointmentsToCalendarEvents(appointments)\n  }, [appointments])\n\n  // Fetch appointments\n  const fetchAppointments = useCallback(async () => {\n    try {\n      setLoading(true)\n      const response = await appointmentsApi.getAll({ limit: 1000 })\n      setAppointments(response.docs)\n    } catch (error) {\n      console.error('Failed to fetch appointments:', error)\n      appointmentNotifications.error.fetchFailed()\n    } finally {\n      setLoading(false)\n    }\n  }, [])\n\n  useEffect(() => {\n    fetchAppointments()\n  }, [fetchAppointments])\n\n  // Handle event selection\n  const handleSelectEvent = useCallback((event: CalendarEvent) => {\n    onEditAppointment?.(event.resource)\n  }, [onEditAppointment])\n\n  // Handle slot selection (for creating new appointments)\n  const handleSelectSlot = useCallback(({ start }: { start: Date }) => {\n    // You can pass the selected date to the new appointment form\n    onNewAppointment?.()\n  }, [onNewAppointment])\n\n  // Custom event style getter\n  const eventStyleGetter = useCallback((event: CalendarEvent) => {\n    const backgroundColor = getStatusColor(event.status)\n    const borderColor = getAppointmentTypeColor(event.appointmentType)\n\n    return {\n      style: {\n        backgroundColor,\n        borderColor,\n        borderWidth: '2px',\n        borderStyle: 'solid',\n        color: 'white',\n        fontSize: '12px',\n        padding: '2px 4px',\n      }\n    }\n  }, [])\n\n  return (\n    <Card className=\"h-full\">\n      <CardHeader>\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"flex items-center gap-2\">\n            <IconCalendar className=\"h-5 w-5\" />\n            预约日历\n          </CardTitle>\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={fetchAppointments}\n              disabled={loading}\n            >\n              <IconRefresh className=\"h-4 w-4 mr-2\" />\n              刷新\n            </Button>\n            <Button\n              size=\"sm\"\n              onClick={onNewAppointment}\n            >\n              <IconPlus className=\"h-4 w-4 mr-2\" />\n              新建预约\n            </Button>\n          </div>\n        </div>\n      </CardHeader>\n      <CardContent className=\"p-4\">\n        {loading ? (\n          <div className=\"flex items-center justify-center py-12\">\n            <div className=\"text-center\">\n              <IconCalendar className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground animate-pulse\" />\n              <p className=\"text-muted-foreground\">加载预约数据中...</p>\n            </div>\n          </div>\n        ) : (\n          <div className=\"h-[600px]\">\n            <Calendar\n              localizer={localizer}\n              events={events}\n              startAccessor=\"start\"\n              endAccessor=\"end\"\n              view={view}\n              onView={setView}\n              date={date}\n              onNavigate={setDate}\n              onSelectEvent={handleSelectEvent}\n              onSelectSlot={handleSelectSlot}\n              selectable\n              eventPropGetter={eventStyleGetter}\n              messages={calendarLabels}\n              culture=\"zh-CN\"\n              formats={{\n                monthHeaderFormat: 'yyyy年MM月',\n                dayHeaderFormat: 'MM月dd日',\n                dayRangeHeaderFormat: ({ start, end }) =>\n                  `${format(start, 'MM月dd日', { locale: zhCN })} - ${format(end, 'MM月dd日', { locale: zhCN })}`,\n                agendaDateFormat: 'MM月dd日',\n                agendaTimeFormat: 'HH:mm',\n                agendaTimeRangeFormat: ({ start, end }) =>\n                  `${format(start, 'HH:mm', { locale: zhCN })} - ${format(end, 'HH:mm', { locale: zhCN })}`,\n              }}\n              components={{\n                month: {\n                  dateHeader: ({ date, label }) => (\n                    <span className=\"text-sm font-medium\">{label}</span>\n                  ),\n                },\n                week: {\n                  header: ({ date, label }) => (\n                    <span className=\"text-sm font-medium\">{label}</span>\n                  ),\n                },\n                event: ({ event }) => (\n                  <div className=\"text-xs\">\n                    <div className=\"font-medium truncate\">{event.title}</div>\n                    <div className=\"opacity-75\">\n                      {event.appointmentType === 'consultation' ? '咨询' : '治疗'}\n                    </div>\n                  </div>\n                ),\n              }}\n            />\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAIA;AAEA;AACA;AAAA;AAAA;AAYA;AAGA;AACA;;;AA5BA;;;;;;;;;;;;;AAsCA,iEAAiE;AACjE,MAAM,UAAU;IACd,SAAS,4MAAA,CAAA,OAAI;AACf;AAEA,MAAM,YAAY,CAAA,GAAA,+RAAA,CAAA,mBAAgB,AAAD,EAAE;IACjC,QAAA,gNAAA,CAAA,SAAM;IACN,OAAA,+MAAA,CAAA,QAAK;IACL,aAAa,IAAM,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,IAAI,QAAQ;YAAE,QAAQ,4MAAA,CAAA,OAAI;QAAC;IAC1D,QAAA,gMAAA,CAAA,SAAM;IACN;AACF;AAQO,SAAS,oBAAoB,EAClC,gBAAgB,EAChB,iBAAiB,EACjB,mBAAmB,EACM;;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAQ;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IAErC,0CAA0C;IAC1C,MAAM,SAAS,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;+CAAE;YACrB,OAAO,CAAA,GAAA,kIAAA,CAAA,+BAA4B,AAAD,EAAE;QACtC;8CAAG;QAAC;KAAa;IAEjB,qBAAqB;IACrB,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;8DAAE;YACpC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;oBAAE,OAAO;gBAAK;gBAC5D,gBAAgB,SAAS,IAAI;YAC/B,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C,6IAAA,CAAA,2BAAwB,CAAC,KAAK,CAAC,WAAW;YAC5C,SAAU;gBACR,WAAW;YACb;QACF;6DAAG,EAAE;IAEL,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;yCAAE;YACR;QACF;wCAAG;QAAC;KAAkB;IAEtB,yBAAyB;IACzB,MAAM,oBAAoB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;8DAAE,CAAC;YACrC,oBAAoB,MAAM,QAAQ;QACpC;6DAAG;QAAC;KAAkB;IAEtB,wDAAwD;IACxD,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;6DAAE,CAAC,EAAE,KAAK,EAAmB;YAC9D,6DAA6D;YAC7D;QACF;4DAAG;QAAC;KAAiB;IAErB,4BAA4B;IAC5B,MAAM,mBAAmB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;6DAAE,CAAC;YACpC,MAAM,kBAAkB,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,MAAM;YACnD,MAAM,cAAc,CAAA,GAAA,kIAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,eAAe;YAEjE,OAAO;gBACL,OAAO;oBACL;oBACA;oBACA,aAAa;oBACb,aAAa;oBACb,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;YACF;QACF;4DAAG,EAAE;IAEL,qBACE,4SAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,4SAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,4SAAC,+TAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAGtC,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU;;sDAEV,4SAAC,6TAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAG1C,4SAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;;sDAET,4SAAC,uTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAM7C,4SAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACpB,wBACC,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,+TAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,4SAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;yCAIzC,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,+RAAA,CAAA,WAAQ;wBACP,WAAW;wBACX,QAAQ;wBACR,eAAc;wBACd,aAAY;wBACZ,MAAM;wBACN,QAAQ;wBACR,MAAM;wBACN,YAAY;wBACZ,eAAe;wBACf,cAAc;wBACd,UAAU;wBACV,iBAAiB;wBACjB,UAAU,kIAAA,CAAA,iBAAc;wBACxB,SAAQ;wBACR,SAAS;4BACP,mBAAmB;4BACnB,iBAAiB;4BACjB,sBAAsB,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GACnC,GAAG,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,UAAU;oCAAE,QAAQ,4MAAA,CAAA,OAAI;gCAAC,GAAG,GAAG,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,KAAK,UAAU;oCAAE,QAAQ,4MAAA,CAAA,OAAI;gCAAC,IAAI;4BAC7F,kBAAkB;4BAClB,kBAAkB;4BAClB,uBAAuB,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,GACpC,GAAG,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAS;oCAAE,QAAQ,4MAAA,CAAA,OAAI;gCAAC,GAAG,GAAG,EAAE,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,KAAK,SAAS;oCAAE,QAAQ,4MAAA,CAAA,OAAI;gCAAC,IAAI;wBAC7F;wBACA,YAAY;4BACV,OAAO;gCACL,YAAY,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBAC1B,4SAAC;wCAAK,WAAU;kDAAuB;;;;;;4BAE3C;4BACA,MAAM;gCACJ,QAAQ,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,iBACtB,4SAAC;wCAAK,WAAU;kDAAuB;;;;;;4BAE3C;4BACA,OAAO,CAAC,EAAE,KAAK,EAAE,iBACf,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDAAwB,MAAM,KAAK;;;;;;sDAClD,4SAAC;4CAAI,WAAU;sDACZ,MAAM,eAAe,KAAK,iBAAiB,OAAO;;;;;;;;;;;;wBAI3D;;;;;;;;;;;;;;;;;;;;;;AAOd;GAvJgB;KAAA", "debugId": null}}, {"offset": {"line": 5719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { buttonVariants } from '@/components/ui/button';\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot='alert-dialog' {...props} />;\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot='alert-dialog-trigger' {...props} />\r\n  );\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot='alert-dialog-portal' {...props} />\r\n  );\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot='alert-dialog-overlay'\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot='alert-dialog-content'\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  );\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-dialog-header'\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot='alert-dialog-footer'\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot='alert-dialog-title'\r\n      className={cn('text-lg font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot='alert-dialog-description'\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: 'outline' }), className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGA;AAEA;AACA;AANA;;;;;AAQA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,4SAAC,qRAAA,CAAA,OAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,4SAAC,qRAAA,CAAA,UAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,4SAAC,qRAAA,CAAA,SAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,4SAAC,qRAAA,CAAA,UAA4B;QAC3B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,4SAAC;;0BACC,4SAAC;;;;;0BACD,4SAAC,qRAAA,CAAA,UAA4B;gBAC3B,aAAU;gBACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,4SAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,4SAAC,qRAAA,CAAA,QAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,4SAAC,qRAAA,CAAA,cAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,4SAAC,qRAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,4SAAC,qRAAA,CAAA,SAA2B;QAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 5905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointment-status-manager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n  DropdownMenuSeparator,\n} from '@/components/ui/dropdown-menu';\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog';\nimport { \n  IconChevronDown, \n  IconCheck, \n  IconX, \n  IconClock,\n  IconCalendarCheck,\n  IconUserX\n} from '@tabler/icons-react';\nimport { Appointment } from '@/types/clinic';\nimport { appointmentsApi } from '@/lib/api';\nimport { appointmentNotifications } from '@/lib/appointment-notifications';\nimport { useRole } from '@/lib/role-context';\n\ninterface AppointmentStatusManagerProps {\n  appointment: Appointment;\n  onStatusChange?: (appointment: Appointment, newStatus: string) => void;\n  compact?: boolean;\n}\n\nconst statusConfig = {\n  'scheduled': {\n    label: '已预约',\n    color: 'bg-blue-100 text-blue-800 hover:bg-blue-200',\n    icon: IconClock,\n    nextStates: ['confirmed', 'cancelled'],\n  },\n  'confirmed': {\n    label: '已确认',\n    color: 'bg-green-100 text-green-800 hover:bg-green-200',\n    icon: IconCalendarCheck,\n    nextStates: ['completed', 'no-show', 'cancelled'],\n  },\n  'completed': {\n    label: '已完成',\n    color: 'bg-gray-100 text-gray-800 hover:bg-gray-200',\n    icon: IconCheck,\n    nextStates: [],\n  },\n  'cancelled': {\n    label: '已取消',\n    color: 'bg-red-100 text-red-800 hover:bg-red-200',\n    icon: IconX,\n    nextStates: ['scheduled'],\n  },\n  'no-show': {\n    label: '未到诊',\n    color: 'bg-amber-100 text-amber-800 hover:bg-amber-200',\n    icon: IconUserX,\n    nextStates: ['scheduled', 'cancelled'],\n  },\n};\n\nconst statusTransitionLabels = {\n  'scheduled': '预约',\n  'confirmed': '确认',\n  'completed': '完成',\n  'cancelled': '取消',\n  'no-show': '标记未到诊',\n};\n\nexport function AppointmentStatusManager({\n  appointment,\n  onStatusChange,\n  compact = false,\n}: AppointmentStatusManagerProps) {\n  const { hasPermission } = useRole();\n  const [loading, setLoading] = useState(false);\n  const [confirmDialog, setConfirmDialog] = useState<{\n    open: boolean;\n    newStatus: string;\n    title: string;\n    description: string;\n  }>({\n    open: false,\n    newStatus: '',\n    title: '',\n    description: '',\n  });\n\n  const currentStatus = appointment.status;\n  const config = statusConfig[currentStatus as keyof typeof statusConfig];\n  const StatusIcon = config?.icon || IconClock;\n\n  const canChangeStatus = hasPermission('canUpdateAppointments');\n\n  const handleStatusChange = async (newStatus: string) => {\n    if (!canChangeStatus) {\n      appointmentNotifications.error.permissionDenied('更改预约状态');\n      return;\n    }\n\n    // Show confirmation dialog for certain status changes\n    if (newStatus === 'cancelled' || newStatus === 'no-show' || newStatus === 'completed') {\n      const statusLabel = statusTransitionLabels[newStatus as keyof typeof statusTransitionLabels];\n      setConfirmDialog({\n        open: true,\n        newStatus,\n        title: `确认${statusLabel}预约`,\n        description: `您确定要将 ${appointment.patient.fullName} 的预约状态更改为\"${statusConfig[newStatus as keyof typeof statusConfig].label}\"吗？`,\n      });\n      return;\n    }\n\n    // Direct status change for non-destructive actions\n    await performStatusChange(newStatus);\n  };\n\n  const performStatusChange = async (newStatus: string) => {\n    setLoading(true);\n    try {\n      const updatedAppointment = await appointmentsApi.update(appointment.id, {\n        status: newStatus as any,\n      });\n\n      appointmentNotifications.appointment.statusChanged(\n        updatedAppointment,\n        currentStatus,\n        newStatus\n      );\n\n      onStatusChange?.(updatedAppointment, newStatus);\n    } catch (error) {\n      console.error('Failed to update appointment status:', error);\n      appointmentNotifications.error.saveFailed(true);\n    } finally {\n      setLoading(false);\n      setConfirmDialog({ ...confirmDialog, open: false });\n    }\n  };\n\n  const handleConfirmStatusChange = () => {\n    performStatusChange(confirmDialog.newStatus);\n  };\n\n  if (compact) {\n    return (\n      <div className=\"flex items-center gap-2\">\n        <Badge className={config?.color}>\n          <StatusIcon className=\"h-3 w-3 mr-1\" />\n          {config?.label}\n        </Badge>\n        \n        {canChangeStatus && config?.nextStates.length > 0 && (\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                disabled={loading}\n                className=\"h-6 w-6 p-0\"\n              >\n                <IconChevronDown className=\"h-3 w-3\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              {config.nextStates.map((status) => {\n                const nextConfig = statusConfig[status as keyof typeof statusConfig];\n                const NextIcon = nextConfig.icon;\n                return (\n                  <DropdownMenuItem\n                    key={status}\n                    onClick={() => handleStatusChange(status)}\n                  >\n                    <NextIcon className=\"h-4 w-4 mr-2\" />\n                    {statusTransitionLabels[status as keyof typeof statusTransitionLabels]}\n                  </DropdownMenuItem>\n                );\n              })}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        )}\n\n        <AlertDialog open={confirmDialog.open} onOpenChange={(open) => \n          setConfirmDialog({ ...confirmDialog, open })\n        }>\n          <AlertDialogContent>\n            <AlertDialogHeader>\n              <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>\n              <AlertDialogDescription>\n                {confirmDialog.description}\n              </AlertDialogDescription>\n            </AlertDialogHeader>\n            <AlertDialogFooter>\n              <AlertDialogCancel>取消</AlertDialogCancel>\n              <AlertDialogAction\n                onClick={handleConfirmStatusChange}\n                disabled={loading}\n              >\n                确认\n              </AlertDialogAction>\n            </AlertDialogFooter>\n          </AlertDialogContent>\n        </AlertDialog>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex items-center gap-3\">\n      <div className=\"flex items-center gap-2\">\n        <Badge className={config?.color}>\n          <StatusIcon className=\"h-4 w-4 mr-2\" />\n          {config?.label}\n        </Badge>\n        \n        <div className=\"text-sm text-muted-foreground\">\n          预约时间: {new Date(appointment.appointmentDate).toLocaleString('zh-CN')}\n        </div>\n      </div>\n\n      {canChangeStatus && config?.nextStates.length > 0 && (\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              disabled={loading}\n            >\n              更改状态\n              <IconChevronDown className=\"h-4 w-4 ml-2\" />\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\">\n            <div className=\"px-2 py-1.5 text-sm font-medium text-muted-foreground\">\n              可用操作\n            </div>\n            <DropdownMenuSeparator />\n            {config.nextStates.map((status) => {\n              const nextConfig = statusConfig[status as keyof typeof statusConfig];\n              const NextIcon = nextConfig.icon;\n              return (\n                <DropdownMenuItem\n                  key={status}\n                  onClick={() => handleStatusChange(status)}\n                >\n                  <NextIcon className=\"h-4 w-4 mr-2\" />\n                  {statusTransitionLabels[status as keyof typeof statusTransitionLabels]}\n                </DropdownMenuItem>\n              );\n            })}\n          </DropdownMenuContent>\n        </DropdownMenu>\n      )}\n\n      <AlertDialog open={confirmDialog.open} onOpenChange={(open) => \n        setConfirmDialog({ ...confirmDialog, open })\n      }>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>\n            <AlertDialogDescription>\n              {confirmDialog.description}\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>取消</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={handleConfirmStatusChange}\n              disabled={loading}\n            >\n              确认\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAOA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;;;AAjCA;;;;;;;;;;AAyCA,MAAM,eAAe;IACnB,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM,yTAAA,CAAA,YAAS;QACf,YAAY;YAAC;YAAa;SAAY;IACxC;IACA,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM,yUAAA,CAAA,oBAAiB;QACvB,YAAY;YAAC;YAAa;YAAW;SAAY;IACnD;IACA,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM,yTAAA,CAAA,YAAS;QACf,YAAY,EAAE;IAChB;IACA,aAAa;QACX,OAAO;QACP,OAAO;QACP,MAAM,iTAAA,CAAA,QAAK;QACX,YAAY;YAAC;SAAY;IAC3B;IACA,WAAW;QACT,OAAO;QACP,OAAO;QACP,MAAM,yTAAA,CAAA,YAAS;QACf,YAAY;YAAC;YAAa;SAAY;IACxC;AACF;AAEA,MAAM,yBAAyB;IAC7B,aAAa;IACb,aAAa;IACb,aAAa;IACb,aAAa;IACb,WAAW;AACb;AAEO,SAAS,yBAAyB,EACvC,WAAW,EACX,cAAc,EACd,UAAU,KAAK,EACe;;IAC9B,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAChC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAK9C;QACD,MAAM;QACN,WAAW;QACX,OAAO;QACP,aAAa;IACf;IAEA,MAAM,gBAAgB,YAAY,MAAM;IACxC,MAAM,SAAS,YAAY,CAAC,cAA2C;IACvE,MAAM,aAAa,QAAQ,QAAQ,yTAAA,CAAA,YAAS;IAE5C,MAAM,kBAAkB,cAAc;IAEtC,MAAM,qBAAqB,OAAO;QAChC,IAAI,CAAC,iBAAiB;YACpB,6IAAA,CAAA,2BAAwB,CAAC,KAAK,CAAC,gBAAgB,CAAC;YAChD;QACF;QAEA,sDAAsD;QACtD,IAAI,cAAc,eAAe,cAAc,aAAa,cAAc,aAAa;YACrF,MAAM,cAAc,sBAAsB,CAAC,UAAiD;YAC5F,iBAAiB;gBACf,MAAM;gBACN;gBACA,OAAO,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC;gBAC3B,aAAa,CAAC,MAAM,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,UAAU,EAAE,YAAY,CAAC,UAAuC,CAAC,KAAK,CAAC,GAAG,CAAC;YAChI;YACA;QACF;QAEA,mDAAmD;QACnD,MAAM,oBAAoB;IAC5B;IAEA,MAAM,sBAAsB,OAAO;QACjC,WAAW;QACX,IAAI;YACF,MAAM,qBAAqB,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;gBACtE,QAAQ;YACV;YAEA,6IAAA,CAAA,2BAAwB,CAAC,WAAW,CAAC,aAAa,CAChD,oBACA,eACA;YAGF,iBAAiB,oBAAoB;QACvC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,6IAAA,CAAA,2BAAwB,CAAC,KAAK,CAAC,UAAU,CAAC;QAC5C,SAAU;YACR,WAAW;YACX,iBAAiB;gBAAE,GAAG,aAAa;gBAAE,MAAM;YAAM;QACnD;IACF;IAEA,MAAM,4BAA4B;QAChC,oBAAoB,cAAc,SAAS;IAC7C;IAEA,IAAI,SAAS;QACX,qBACE,4SAAC;YAAI,WAAU;;8BACb,4SAAC,oIAAA,CAAA,QAAK;oBAAC,WAAW,QAAQ;;sCACxB,4SAAC;4BAAW,WAAU;;;;;;wBACrB,QAAQ;;;;;;;gBAGV,mBAAmB,QAAQ,WAAW,SAAS,mBAC9C,4SAAC,+IAAA,CAAA,eAAY;;sCACX,4SAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAO;sCAC1B,cAAA,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,UAAU;gCACV,WAAU;0CAEV,cAAA,4SAAC,qUAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;sCAG/B,4SAAC,+IAAA,CAAA,sBAAmB;4BAAC,OAAM;sCACxB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;gCACtB,MAAM,aAAa,YAAY,CAAC,OAAoC;gCACpE,MAAM,WAAW,WAAW,IAAI;gCAChC,qBACE,4SAAC,+IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,mBAAmB;;sDAElC,4SAAC;4CAAS,WAAU;;;;;;wCACnB,sBAAsB,CAAC,OAA8C;;mCAJjE;;;;;4BAOX;;;;;;;;;;;;8BAKN,4SAAC,8IAAA,CAAA,cAAW;oBAAC,MAAM,cAAc,IAAI;oBAAE,cAAc,CAAC,OACpD,iBAAiB;4BAAE,GAAG,aAAa;4BAAE;wBAAK;8BAE1C,cAAA,4SAAC,8IAAA,CAAA,qBAAkB;;0CACjB,4SAAC,8IAAA,CAAA,oBAAiB;;kDAChB,4SAAC,8IAAA,CAAA,mBAAgB;kDAAE,cAAc,KAAK;;;;;;kDACtC,4SAAC,8IAAA,CAAA,yBAAsB;kDACpB,cAAc,WAAW;;;;;;;;;;;;0CAG9B,4SAAC,8IAAA,CAAA,oBAAiB;;kDAChB,4SAAC,8IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,4SAAC,8IAAA,CAAA,oBAAiB;wCAChB,SAAS;wCACT,UAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQb;IAEA,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAI,WAAU;;kCACb,4SAAC,oIAAA,CAAA,QAAK;wBAAC,WAAW,QAAQ;;0CACxB,4SAAC;gCAAW,WAAU;;;;;;4BACrB,QAAQ;;;;;;;kCAGX,4SAAC;wBAAI,WAAU;;4BAAgC;4BACtC,IAAI,KAAK,YAAY,eAAe,EAAE,cAAc,CAAC;;;;;;;;;;;;;YAI/D,mBAAmB,QAAQ,WAAW,SAAS,mBAC9C,4SAAC,+IAAA,CAAA,eAAY;;kCACX,4SAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAO;kCAC1B,cAAA,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,UAAU;;gCACX;8CAEC,4SAAC,qUAAA,CAAA,kBAAe;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAG/B,4SAAC,+IAAA,CAAA,sBAAmB;wBAAC,OAAM;;0CACzB,4SAAC;gCAAI,WAAU;0CAAwD;;;;;;0CAGvE,4SAAC,+IAAA,CAAA,wBAAqB;;;;;4BACrB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC;gCACtB,MAAM,aAAa,YAAY,CAAC,OAAoC;gCACpE,MAAM,WAAW,WAAW,IAAI;gCAChC,qBACE,4SAAC,+IAAA,CAAA,mBAAgB;oCAEf,SAAS,IAAM,mBAAmB;;sDAElC,4SAAC;4CAAS,WAAU;;;;;;wCACnB,sBAAsB,CAAC,OAA8C;;mCAJjE;;;;;4BAOX;;;;;;;;;;;;;0BAKN,4SAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM,cAAc,IAAI;gBAAE,cAAc,CAAC,OACpD,iBAAiB;wBAAE,GAAG,aAAa;wBAAE;oBAAK;0BAE1C,cAAA,4SAAC,8IAAA,CAAA,qBAAkB;;sCACjB,4SAAC,8IAAA,CAAA,oBAAiB;;8CAChB,4SAAC,8IAAA,CAAA,mBAAgB;8CAAE,cAAc,KAAK;;;;;;8CACtC,4SAAC,8IAAA,CAAA,yBAAsB;8CACpB,cAAc,WAAW;;;;;;;;;;;;sCAG9B,4SAAC,8IAAA,CAAA,oBAAiB;;8CAChB,4SAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,4SAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA/MgB;;QAKY,iIAAA,CAAA,UAAO;;;KALnB", "debugId": null}}, {"offset": {"line": 6400, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/switch.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as SwitchPrimitive from '@radix-ui/react-switch';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Switch({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SwitchPrimitive.Root>) {\r\n  return (\r\n    <SwitchPrimitive.Root\r\n      data-slot='switch'\r\n      className={cn(\r\n        'peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <SwitchPrimitive.Thumb\r\n        data-slot='switch-thumb'\r\n        className={cn(\r\n          'bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0'\r\n        )}\r\n      />\r\n    </SwitchPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Switch };\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,4SAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6WACA;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,kRAAA,CAAA,QAAqB;YACpB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKV;KArBS", "debugId": null}}, {"offset": {"line": 6442, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/checkbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as CheckboxPrimitive from '@radix-ui/react-checkbox';\r\nimport { CheckIcon } from 'lucide-react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Checkbox({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\r\n  return (\r\n    <CheckboxPrimitive.Root\r\n      data-slot='checkbox'\r\n      className={cn(\r\n        'peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50',\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <CheckboxPrimitive.Indicator\r\n        data-slot='checkbox-indicator'\r\n        className='flex items-center justify-center text-current transition-none'\r\n      >\r\n        <CheckIcon className='size-3.5' />\r\n      </CheckboxPrimitive.Indicator>\r\n    </CheckboxPrimitive.Root>\r\n  );\r\n}\r\n\r\nexport { Checkbox };\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,4SAAC,iRAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,4SAAC,iRAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,4SAAC,+RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 6493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/appointment-reminders.ts"], "sourcesContent": ["// Appointment reminder system for medical clinic\n// Handles scheduling and managing appointment reminders\n\nimport { Appointment } from '@/types/clinic';\nimport { appointmentNotifications } from './appointment-notifications';\nimport { formatDateTime } from './calendar-utils';\n\nexport interface ReminderSettings {\n  enabled: boolean;\n  reminderTimes: number[]; // Minutes before appointment\n  methods: ('notification' | 'email' | 'sms')[];\n}\n\nexport interface AppointmentReminder {\n  id: string;\n  appointmentId: string;\n  reminderTime: Date;\n  method: 'notification' | 'email' | 'sms';\n  status: 'pending' | 'sent' | 'failed';\n  createdAt: Date;\n}\n\nclass AppointmentReminderService {\n  private reminders: Map<string, AppointmentReminder[]> = new Map();\n  private timers: Map<string, NodeJS.Timeout> = new Map();\n  private defaultSettings: ReminderSettings = {\n    enabled: true,\n    reminderTimes: [60, 15], // 1 hour and 15 minutes before\n    methods: ['notification'],\n  };\n\n  /**\n   * Schedule reminders for an appointment\n   */\n  scheduleReminders(appointment: Appointment, settings?: Partial<ReminderSettings>): void {\n    const reminderSettings = { ...this.defaultSettings, ...settings };\n    \n    if (!reminderSettings.enabled) {\n      return;\n    }\n\n    // Clear existing reminders for this appointment\n    this.clearReminders(appointment.id);\n\n    const appointmentTime = new Date(appointment.appointmentDate);\n    const now = new Date();\n\n    reminderSettings.reminderTimes.forEach((minutesBefore) => {\n      const reminderTime = new Date(appointmentTime.getTime() - minutesBefore * 60 * 1000);\n      \n      // Only schedule if reminder time is in the future\n      if (reminderTime > now) {\n        reminderSettings.methods.forEach((method) => {\n          const reminder: AppointmentReminder = {\n            id: `${appointment.id}-${minutesBefore}-${method}`,\n            appointmentId: appointment.id,\n            reminderTime,\n            method,\n            status: 'pending',\n            createdAt: new Date(),\n          };\n\n          this.scheduleReminder(reminder, appointment);\n        });\n      }\n    });\n  }\n\n  /**\n   * Schedule a single reminder\n   */\n  private scheduleReminder(reminder: AppointmentReminder, appointment: Appointment): void {\n    const now = new Date();\n    const delay = reminder.reminderTime.getTime() - now.getTime();\n\n    if (delay <= 0) {\n      // Send immediately if time has passed\n      this.sendReminder(reminder, appointment);\n      return;\n    }\n\n    const timer = setTimeout(() => {\n      this.sendReminder(reminder, appointment);\n    }, delay);\n\n    // Store the timer for potential cancellation\n    this.timers.set(reminder.id, timer);\n\n    // Store the reminder\n    const appointmentReminders = this.reminders.get(appointment.id) || [];\n    appointmentReminders.push(reminder);\n    this.reminders.set(appointment.id, appointmentReminders);\n\n    console.log(`Scheduled reminder for appointment ${appointment.id} at ${reminder.reminderTime.toISOString()}`);\n  }\n\n  /**\n   * Send a reminder\n   */\n  private async sendReminder(reminder: AppointmentReminder, appointment: Appointment): Promise<void> {\n    try {\n      const minutesUntil = Math.floor(\n        (new Date(appointment.appointmentDate).getTime() - new Date().getTime()) / (1000 * 60)\n      );\n\n      switch (reminder.method) {\n        case 'notification':\n          this.sendNotificationReminder(appointment, minutesUntil);\n          break;\n        case 'email':\n          await this.sendEmailReminder(appointment, minutesUntil);\n          break;\n        case 'sms':\n          await this.sendSMSReminder(appointment, minutesUntil);\n          break;\n      }\n\n      reminder.status = 'sent';\n      appointmentNotifications.reminder.reminderSet(\n        appointment,\n        formatDateTime(reminder.reminderTime)\n      );\n    } catch (error) {\n      console.error('Failed to send reminder:', error);\n      reminder.status = 'failed';\n    } finally {\n      // Clean up timer\n      const timer = this.timers.get(reminder.id);\n      if (timer) {\n        clearTimeout(timer);\n        this.timers.delete(reminder.id);\n      }\n    }\n  }\n\n  /**\n   * Send browser notification reminder\n   */\n  private sendNotificationReminder(appointment: Appointment, minutesUntil: number): void {\n    // Check if notifications are supported and permitted\n    if ('Notification' in window) {\n      if (Notification.permission === 'granted') {\n        const notification = new Notification('预约提醒', {\n          body: `${appointment.patient.fullName} 的预约将在 ${minutesUntil} 分钟后开始`,\n          icon: '/favicon.ico',\n          tag: `appointment-${appointment.id}`,\n        });\n\n        // Auto-close after 10 seconds\n        setTimeout(() => notification.close(), 10000);\n      } else if (Notification.permission !== 'denied') {\n        Notification.requestPermission().then((permission) => {\n          if (permission === 'granted') {\n            this.sendNotificationReminder(appointment, minutesUntil);\n          }\n        });\n      }\n    }\n\n    // Also show toast notification\n    appointmentNotifications.reminder.upcoming(appointment, minutesUntil);\n  }\n\n  /**\n   * Send email reminder (placeholder - would integrate with email service)\n   */\n  private async sendEmailReminder(appointment: Appointment, minutesUntil: number): Promise<void> {\n    // This would integrate with an email service like SendGrid, AWS SES, etc.\n    console.log(`Email reminder would be sent for appointment ${appointment.id}`);\n    \n    // Placeholder implementation\n    const emailData = {\n      to: appointment.patient.email,\n      subject: '预约提醒',\n      body: `您好 ${appointment.patient.fullName}，您的预约将在 ${minutesUntil} 分钟后开始。`,\n      appointmentDetails: {\n        date: formatDateTime(new Date(appointment.appointmentDate)),\n        treatment: appointment.treatment?.name || '咨询',\n        practitioner: `${appointment.practitioner.firstName} ${appointment.practitioner.lastName}`,\n      },\n    };\n\n    // TODO: Implement actual email sending\n    console.log('Email data:', emailData);\n  }\n\n  /**\n   * Send SMS reminder (placeholder - would integrate with SMS service)\n   */\n  private async sendSMSReminder(appointment: Appointment, minutesUntil: number): Promise<void> {\n    // This would integrate with an SMS service like Twilio, AWS SNS, etc.\n    console.log(`SMS reminder would be sent for appointment ${appointment.id}`);\n    \n    // Placeholder implementation\n    const smsData = {\n      to: appointment.patient.phone,\n      message: `预约提醒：${appointment.patient.fullName}，您的预约将在 ${minutesUntil} 分钟后开始。时间：${formatDateTime(new Date(appointment.appointmentDate))}`,\n    };\n\n    // TODO: Implement actual SMS sending\n    console.log('SMS data:', smsData);\n  }\n\n  /**\n   * Clear all reminders for an appointment\n   */\n  clearReminders(appointmentId: string): void {\n    const reminders = this.reminders.get(appointmentId) || [];\n    \n    reminders.forEach((reminder) => {\n      const timer = this.timers.get(reminder.id);\n      if (timer) {\n        clearTimeout(timer);\n        this.timers.delete(reminder.id);\n      }\n    });\n\n    this.reminders.delete(appointmentId);\n    console.log(`Cleared reminders for appointment ${appointmentId}`);\n  }\n\n  /**\n   * Get reminders for an appointment\n   */\n  getReminders(appointmentId: string): AppointmentReminder[] {\n    return this.reminders.get(appointmentId) || [];\n  }\n\n  /**\n   * Check for overdue appointments and send notifications\n   */\n  checkOverdueAppointments(appointments: Appointment[]): void {\n    const now = new Date();\n    \n    appointments.forEach((appointment) => {\n      const appointmentTime = new Date(appointment.appointmentDate);\n      const isOverdue = appointmentTime < now && \n        (appointment.status === 'scheduled' || appointment.status === 'confirmed');\n      \n      if (isOverdue) {\n        appointmentNotifications.reminder.overdue(appointment);\n      }\n    });\n  }\n\n  /**\n   * Request notification permission\n   */\n  async requestNotificationPermission(): Promise<boolean> {\n    if ('Notification' in window) {\n      const permission = await Notification.requestPermission();\n      return permission === 'granted';\n    }\n    return false;\n  }\n\n  /**\n   * Check if notifications are supported and permitted\n   */\n  isNotificationSupported(): boolean {\n    return 'Notification' in window && Notification.permission === 'granted';\n  }\n\n  /**\n   * Update reminder settings\n   */\n  updateSettings(settings: Partial<ReminderSettings>): void {\n    this.defaultSettings = { ...this.defaultSettings, ...settings };\n  }\n\n  /**\n   * Get current reminder settings\n   */\n  getSettings(): ReminderSettings {\n    return { ...this.defaultSettings };\n  }\n}\n\n// Export singleton instance\nexport const appointmentReminderService = new AppointmentReminderService();\n\n// Export utility functions\nexport const scheduleAppointmentReminders = (\n  appointment: Appointment,\n  settings?: Partial<ReminderSettings>\n) => {\n  appointmentReminderService.scheduleReminders(appointment, settings);\n};\n\nexport const clearAppointmentReminders = (appointmentId: string) => {\n  appointmentReminderService.clearReminders(appointmentId);\n};\n\nexport const checkOverdueAppointments = (appointments: Appointment[]) => {\n  appointmentReminderService.checkOverdueAppointments(appointments);\n};\n"], "names": [], "mappings": "AAAA,iDAAiD;AACjD,wDAAwD;;;;;;;AAGxD;AACA;;;AAiBA,MAAM;IACI,YAAgD,IAAI,MAAM;IAC1D,SAAsC,IAAI,MAAM;IAChD,kBAAoC;QAC1C,SAAS;QACT,eAAe;YAAC;YAAI;SAAG;QACvB,SAAS;YAAC;SAAe;IAC3B,EAAE;IAEF;;GAEC,GACD,kBAAkB,WAAwB,EAAE,QAAoC,EAAQ;QACtF,MAAM,mBAAmB;YAAE,GAAG,IAAI,CAAC,eAAe;YAAE,GAAG,QAAQ;QAAC;QAEhE,IAAI,CAAC,iBAAiB,OAAO,EAAE;YAC7B;QACF;QAEA,gDAAgD;QAChD,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE;QAElC,MAAM,kBAAkB,IAAI,KAAK,YAAY,eAAe;QAC5D,MAAM,MAAM,IAAI;QAEhB,iBAAiB,aAAa,CAAC,OAAO,CAAC,CAAC;YACtC,MAAM,eAAe,IAAI,KAAK,gBAAgB,OAAO,KAAK,gBAAgB,KAAK;YAE/E,kDAAkD;YAClD,IAAI,eAAe,KAAK;gBACtB,iBAAiB,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChC,MAAM,WAAgC;wBACpC,IAAI,GAAG,YAAY,EAAE,CAAC,CAAC,EAAE,cAAc,CAAC,EAAE,QAAQ;wBAClD,eAAe,YAAY,EAAE;wBAC7B;wBACA;wBACA,QAAQ;wBACR,WAAW,IAAI;oBACjB;oBAEA,IAAI,CAAC,gBAAgB,CAAC,UAAU;gBAClC;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,iBAAiB,QAA6B,EAAE,WAAwB,EAAQ;QACtF,MAAM,MAAM,IAAI;QAChB,MAAM,QAAQ,SAAS,YAAY,CAAC,OAAO,KAAK,IAAI,OAAO;QAE3D,IAAI,SAAS,GAAG;YACd,sCAAsC;YACtC,IAAI,CAAC,YAAY,CAAC,UAAU;YAC5B;QACF;QAEA,MAAM,QAAQ,WAAW;YACvB,IAAI,CAAC,YAAY,CAAC,UAAU;QAC9B,GAAG;QAEH,6CAA6C;QAC7C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE;QAE7B,qBAAqB;QACrB,MAAM,uBAAuB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,KAAK,EAAE;QACrE,qBAAqB,IAAI,CAAC;QAC1B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE;QAEnC,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,YAAY,EAAE,CAAC,IAAI,EAAE,SAAS,YAAY,CAAC,WAAW,IAAI;IAC9G;IAEA;;GAEC,GACD,MAAc,aAAa,QAA6B,EAAE,WAAwB,EAAiB;QACjG,IAAI;YACF,MAAM,eAAe,KAAK,KAAK,CAC7B,CAAC,IAAI,KAAK,YAAY,eAAe,EAAE,OAAO,KAAK,IAAI,OAAO,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;YAGvF,OAAQ,SAAS,MAAM;gBACrB,KAAK;oBACH,IAAI,CAAC,wBAAwB,CAAC,aAAa;oBAC3C;gBACF,KAAK;oBACH,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa;oBAC1C;gBACF,KAAK;oBACH,MAAM,IAAI,CAAC,eAAe,CAAC,aAAa;oBACxC;YACJ;YAEA,SAAS,MAAM,GAAG;YAClB,6IAAA,CAAA,2BAAwB,CAAC,QAAQ,CAAC,WAAW,CAC3C,aACA,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,YAAY;QAExC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,SAAS,MAAM,GAAG;QACpB,SAAU;YACR,iBAAiB;YACjB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE;YACzC,IAAI,OAAO;gBACT,aAAa;gBACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;YAChC;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,yBAAyB,WAAwB,EAAE,YAAoB,EAAQ;QACrF,qDAAqD;QACrD,IAAI,kBAAkB,QAAQ;YAC5B,IAAI,aAAa,UAAU,KAAK,WAAW;gBACzC,MAAM,eAAe,IAAI,aAAa,QAAQ;oBAC5C,MAAM,GAAG,YAAY,OAAO,CAAC,QAAQ,CAAC,OAAO,EAAE,aAAa,MAAM,CAAC;oBACnE,MAAM;oBACN,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,EAAE;gBACtC;gBAEA,8BAA8B;gBAC9B,WAAW,IAAM,aAAa,KAAK,IAAI;YACzC,OAAO,IAAI,aAAa,UAAU,KAAK,UAAU;gBAC/C,aAAa,iBAAiB,GAAG,IAAI,CAAC,CAAC;oBACrC,IAAI,eAAe,WAAW;wBAC5B,IAAI,CAAC,wBAAwB,CAAC,aAAa;oBAC7C;gBACF;YACF;QACF;QAEA,+BAA+B;QAC/B,6IAAA,CAAA,2BAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa;IAC1D;IAEA;;GAEC,GACD,MAAc,kBAAkB,WAAwB,EAAE,YAAoB,EAAiB;QAC7F,0EAA0E;QAC1E,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,YAAY,EAAE,EAAE;QAE5E,6BAA6B;QAC7B,MAAM,YAAY;YAChB,IAAI,YAAY,OAAO,CAAC,KAAK;YAC7B,SAAS;YACT,MAAM,CAAC,GAAG,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,OAAO,CAAC;YACxE,oBAAoB;gBAClB,MAAM,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,YAAY,eAAe;gBACzD,WAAW,YAAY,SAAS,EAAE,QAAQ;gBAC1C,cAAc,GAAG,YAAY,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,YAAY,YAAY,CAAC,QAAQ,EAAE;YAC5F;QACF;QAEA,uCAAuC;QACvC,QAAQ,GAAG,CAAC,eAAe;IAC7B;IAEA;;GAEC,GACD,MAAc,gBAAgB,WAAwB,EAAE,YAAoB,EAAiB;QAC3F,sEAAsE;QACtE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,YAAY,EAAE,EAAE;QAE1E,6BAA6B;QAC7B,MAAM,UAAU;YACd,IAAI,YAAY,OAAO,CAAC,KAAK;YAC7B,SAAS,CAAC,KAAK,EAAE,YAAY,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,aAAa,UAAU,EAAE,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,KAAK,YAAY,eAAe,IAAI;QAC1I;QAEA,qCAAqC;QACrC,QAAQ,GAAG,CAAC,aAAa;IAC3B;IAEA;;GAEC,GACD,eAAe,aAAqB,EAAQ;QAC1C,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE;QAEzD,UAAU,OAAO,CAAC,CAAC;YACjB,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE;YACzC,IAAI,OAAO;gBACT,aAAa;gBACb,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE;YAChC;QACF;QAEA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACtB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,eAAe;IAClE;IAEA;;GAEC,GACD,aAAa,aAAqB,EAAyB;QACzD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,EAAE;IAChD;IAEA;;GAEC,GACD,yBAAyB,YAA2B,EAAQ;QAC1D,MAAM,MAAM,IAAI;QAEhB,aAAa,OAAO,CAAC,CAAC;YACpB,MAAM,kBAAkB,IAAI,KAAK,YAAY,eAAe;YAC5D,MAAM,YAAY,kBAAkB,OAClC,CAAC,YAAY,MAAM,KAAK,eAAe,YAAY,MAAM,KAAK,WAAW;YAE3E,IAAI,WAAW;gBACb,6IAAA,CAAA,2BAAwB,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC5C;QACF;IACF;IAEA;;GAEC,GACD,MAAM,gCAAkD;QACtD,IAAI,kBAAkB,QAAQ;YAC5B,MAAM,aAAa,MAAM,aAAa,iBAAiB;YACvD,OAAO,eAAe;QACxB;QACA,OAAO;IACT;IAEA;;GAEC,GACD,0BAAmC;QACjC,OAAO,kBAAkB,UAAU,aAAa,UAAU,KAAK;IACjE;IAEA;;GAEC,GACD,eAAe,QAAmC,EAAQ;QACxD,IAAI,CAAC,eAAe,GAAG;YAAE,GAAG,IAAI,CAAC,eAAe;YAAE,GAAG,QAAQ;QAAC;IAChE;IAEA;;GAEC,GACD,cAAgC;QAC9B,OAAO;YAAE,GAAG,IAAI,CAAC,eAAe;QAAC;IACnC;AACF;AAGO,MAAM,6BAA6B,IAAI;AAGvC,MAAM,+BAA+B,CAC1C,aACA;IAEA,2BAA2B,iBAAiB,CAAC,aAAa;AAC5D;AAEO,MAAM,4BAA4B,CAAC;IACxC,2BAA2B,cAAc,CAAC;AAC5C;AAEO,MAAM,2BAA2B,CAAC;IACvC,2BAA2B,wBAAwB,CAAC;AACtD", "debugId": null}}, {"offset": {"line": 6737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointment-reminder-settings.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { Input } from '@/components/ui/input';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n} from '@/components/ui/dialog';\nimport { Badge } from '@/components/ui/badge';\nimport { \n  IconBell, \n  IconMail, \n  IconMessage, \n  IconSettings,\n  IconPlus,\n  IconTrash\n} from '@tabler/icons-react';\nimport { \n  appointmentReminderService, \n  ReminderSettings \n} from '@/lib/appointment-reminders';\nimport { toast } from 'sonner';\n\ninterface AppointmentReminderSettingsProps {\n  onSettingsChange?: (settings: ReminderSettings) => void;\n}\n\nexport function AppointmentReminderSettings({\n  onSettingsChange,\n}: AppointmentReminderSettingsProps) {\n  const [open, setOpen] = useState(false);\n  const [settings, setSettings] = useState<ReminderSettings>(\n    appointmentReminderService.getSettings()\n  );\n  const [newReminderTime, setNewReminderTime] = useState<string>('');\n  const [notificationPermission, setNotificationPermission] = useState<boolean>(\n    appointmentReminderService.isNotificationSupported()\n  );\n\n  useEffect(() => {\n    // Check notification permission on mount\n    setNotificationPermission(appointmentReminderService.isNotificationSupported());\n  }, []);\n\n  const handleSettingsChange = (newSettings: Partial<ReminderSettings>) => {\n    const updatedSettings = { ...settings, ...newSettings };\n    setSettings(updatedSettings);\n    appointmentReminderService.updateSettings(updatedSettings);\n    onSettingsChange?.(updatedSettings);\n  };\n\n  const handleAddReminderTime = () => {\n    const minutes = parseInt(newReminderTime);\n    if (isNaN(minutes) || minutes <= 0) {\n      toast.error('请输入有效的提醒时间（分钟）');\n      return;\n    }\n\n    if (settings.reminderTimes.includes(minutes)) {\n      toast.error('该提醒时间已存在');\n      return;\n    }\n\n    const newTimes = [...settings.reminderTimes, minutes].sort((a, b) => b - a);\n    handleSettingsChange({ reminderTimes: newTimes });\n    setNewReminderTime('');\n    toast.success('提醒时间添加成功');\n  };\n\n  const handleRemoveReminderTime = (minutes: number) => {\n    const newTimes = settings.reminderTimes.filter(time => time !== minutes);\n    handleSettingsChange({ reminderTimes: newTimes });\n    toast.success('提醒时间删除成功');\n  };\n\n  const handleMethodChange = (method: 'notification' | 'email' | 'sms', checked: boolean) => {\n    let newMethods = [...settings.methods];\n    \n    if (checked) {\n      if (!newMethods.includes(method)) {\n        newMethods.push(method);\n      }\n    } else {\n      newMethods = newMethods.filter(m => m !== method);\n    }\n\n    handleSettingsChange({ methods: newMethods });\n  };\n\n  const requestNotificationPermission = async () => {\n    const granted = await appointmentReminderService.requestNotificationPermission();\n    setNotificationPermission(granted);\n    \n    if (granted) {\n      toast.success('通知权限已授予');\n    } else {\n      toast.error('通知权限被拒绝');\n    }\n  };\n\n  const formatReminderTime = (minutes: number): string => {\n    if (minutes < 60) {\n      return `${minutes} 分钟前`;\n    } else {\n      const hours = Math.floor(minutes / 60);\n      const remainingMinutes = minutes % 60;\n      if (remainingMinutes === 0) {\n        return `${hours} 小时前`;\n      } else {\n        return `${hours} 小时 ${remainingMinutes} 分钟前`;\n      }\n    }\n  };\n\n  return (\n    <Dialog open={open} onOpenChange={setOpen}>\n      <DialogTrigger asChild>\n        <Button variant=\"outline\" size=\"sm\">\n          <IconSettings className=\"h-4 w-4 mr-2\" />\n          提醒设置\n        </Button>\n      </DialogTrigger>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            <IconBell className=\"h-5 w-5\" />\n            预约提醒设置\n          </DialogTitle>\n          <DialogDescription>\n            配置预约提醒的时间和方式\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-6\">\n          {/* Enable/Disable Reminders */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"space-y-0.5\">\n              <Label htmlFor=\"enable-reminders\">启用预约提醒</Label>\n              <p className=\"text-sm text-muted-foreground\">\n                自动发送预约提醒通知\n              </p>\n            </div>\n            <Switch\n              id=\"enable-reminders\"\n              checked={settings.enabled}\n              onCheckedChange={(checked) => handleSettingsChange({ enabled: checked })}\n            />\n          </div>\n\n          {settings.enabled && (\n            <>\n              {/* Reminder Times */}\n              <div className=\"space-y-3\">\n                <Label>提醒时间</Label>\n                <div className=\"flex flex-wrap gap-2\">\n                  {settings.reminderTimes.map((minutes) => (\n                    <Badge\n                      key={minutes}\n                      variant=\"secondary\"\n                      className=\"flex items-center gap-1\"\n                    >\n                      {formatReminderTime(minutes)}\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        className=\"h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground\"\n                        onClick={() => handleRemoveReminderTime(minutes)}\n                      >\n                        <IconTrash className=\"h-3 w-3\" />\n                      </Button>\n                    </Badge>\n                  ))}\n                </div>\n                \n                <div className=\"flex gap-2\">\n                  <Input\n                    type=\"number\"\n                    placeholder=\"分钟\"\n                    value={newReminderTime}\n                    onChange={(e) => setNewReminderTime(e.target.value)}\n                    className=\"flex-1\"\n                    min=\"1\"\n                    max=\"1440\"\n                  />\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={handleAddReminderTime}\n                    disabled={!newReminderTime}\n                  >\n                    <IconPlus className=\"h-4 w-4\" />\n                  </Button>\n                </div>\n              </div>\n\n              {/* Reminder Methods */}\n              <div className=\"space-y-3\">\n                <Label>提醒方式</Label>\n                \n                <div className=\"space-y-3\">\n                  {/* Browser Notification */}\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <IconBell className=\"h-4 w-4\" />\n                      <div>\n                        <Label htmlFor=\"method-notification\">浏览器通知</Label>\n                        {!notificationPermission && (\n                          <p className=\"text-xs text-muted-foreground\">\n                            需要授权通知权限\n                          </p>\n                        )}\n                      </div>\n                    </div>\n                    <div className=\"flex items-center gap-2\">\n                      {!notificationPermission && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={requestNotificationPermission}\n                        >\n                          授权\n                        </Button>\n                      )}\n                      <Checkbox\n                        id=\"method-notification\"\n                        checked={settings.methods.includes('notification')}\n                        onCheckedChange={(checked) => \n                          handleMethodChange('notification', checked as boolean)\n                        }\n                        disabled={!notificationPermission}\n                      />\n                    </div>\n                  </div>\n\n                  {/* Email */}\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <IconMail className=\"h-4 w-4\" />\n                      <div>\n                        <Label htmlFor=\"method-email\">邮件提醒</Label>\n                        <p className=\"text-xs text-muted-foreground\">\n                          即将推出\n                        </p>\n                      </div>\n                    </div>\n                    <Checkbox\n                      id=\"method-email\"\n                      checked={settings.methods.includes('email')}\n                      onCheckedChange={(checked) => \n                        handleMethodChange('email', checked as boolean)\n                      }\n                      disabled // Temporarily disabled\n                    />\n                  </div>\n\n                  {/* SMS */}\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center gap-2\">\n                      <IconMessage className=\"h-4 w-4\" />\n                      <div>\n                        <Label htmlFor=\"method-sms\">短信提醒</Label>\n                        <p className=\"text-xs text-muted-foreground\">\n                          即将推出\n                        </p>\n                      </div>\n                    </div>\n                    <Checkbox\n                      id=\"method-sms\"\n                      checked={settings.methods.includes('sms')}\n                      onCheckedChange={(checked) => \n                        handleMethodChange('sms', checked as boolean)\n                      }\n                      disabled // Temporarily disabled\n                    />\n                  </div>\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        <DialogFooter>\n          <Button onClick={() => setOpen(false)}>\n            完成\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AASA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAIA;;;AA/BA;;;;;;;;;;;;AAqCO,SAAS,4BAA4B,EAC1C,gBAAgB,EACiB;;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EACrC,yIAAA,CAAA,6BAA0B,CAAC,WAAW;IAExC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EACjE,yIAAA,CAAA,6BAA0B,CAAC,uBAAuB;IAGpD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iDAAE;YACR,yCAAyC;YACzC,0BAA0B,yIAAA,CAAA,6BAA0B,CAAC,uBAAuB;QAC9E;gDAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,MAAM,kBAAkB;YAAE,GAAG,QAAQ;YAAE,GAAG,WAAW;QAAC;QACtD,YAAY;QACZ,yIAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC;QAC1C,mBAAmB;IACrB;IAEA,MAAM,wBAAwB;QAC5B,MAAM,UAAU,SAAS;QACzB,IAAI,MAAM,YAAY,WAAW,GAAG;YAClC,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,IAAI,SAAS,aAAa,CAAC,QAAQ,CAAC,UAAU;YAC5C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,WAAW;eAAI,SAAS,aAAa;YAAE;SAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;QACzE,qBAAqB;YAAE,eAAe;QAAS;QAC/C,mBAAmB;QACnB,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,2BAA2B,CAAC;QAChC,MAAM,WAAW,SAAS,aAAa,CAAC,MAAM,CAAC,CAAA,OAAQ,SAAS;QAChE,qBAAqB;YAAE,eAAe;QAAS;QAC/C,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,MAAM,qBAAqB,CAAC,QAA0C;QACpE,IAAI,aAAa;eAAI,SAAS,OAAO;SAAC;QAEtC,IAAI,SAAS;YACX,IAAI,CAAC,WAAW,QAAQ,CAAC,SAAS;gBAChC,WAAW,IAAI,CAAC;YAClB;QACF,OAAO;YACL,aAAa,WAAW,MAAM,CAAC,CAAA,IAAK,MAAM;QAC5C;QAEA,qBAAqB;YAAE,SAAS;QAAW;IAC7C;IAEA,MAAM,gCAAgC;QACpC,MAAM,UAAU,MAAM,yIAAA,CAAA,6BAA0B,CAAC,6BAA6B;QAC9E,0BAA0B;QAE1B,IAAI,SAAS;YACX,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,OAAO;YACL,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,IAAI;YAChB,OAAO,GAAG,QAAQ,IAAI,CAAC;QACzB,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;YACnC,MAAM,mBAAmB,UAAU;YACnC,IAAI,qBAAqB,GAAG;gBAC1B,OAAO,GAAG,MAAM,IAAI,CAAC;YACvB,OAAO;gBACL,OAAO,GAAG,MAAM,IAAI,EAAE,iBAAiB,IAAI,CAAC;YAC9C;QACF;IACF;IAEA,qBACE,4SAAC,qIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,4SAAC,qIAAA,CAAA,gBAAa;gBAAC,OAAO;0BACpB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,MAAK;;sCAC7B,4SAAC,+TAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAI7C,4SAAC,qIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,4SAAC,qIAAA,CAAA,eAAY;;0CACX,4SAAC,qIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,4SAAC,uTAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,4SAAC,qIAAA,CAAA,oBAAiB;0CAAC;;;;;;;;;;;;kCAKrB,4SAAC;wBAAI,WAAU;;0CAEb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,oIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAmB;;;;;;0DAClC,4SAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAI/C,4SAAC,qIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS,SAAS,OAAO;wCACzB,iBAAiB,CAAC,UAAY,qBAAqB;gDAAE,SAAS;4CAAQ;;;;;;;;;;;;4BAIzE,SAAS,OAAO,kBACf;;kDAEE,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DACP,4SAAC;gDAAI,WAAU;0DACZ,SAAS,aAAa,CAAC,GAAG,CAAC,CAAC,wBAC3B,4SAAC,oIAAA,CAAA,QAAK;wDAEJ,SAAQ;wDACR,WAAU;;4DAET,mBAAmB;0EACpB,4SAAC,qIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,SAAS,IAAM,yBAAyB;0EAExC,cAAA,4SAAC,yTAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;;;;;;;uDAXlB;;;;;;;;;;0DAiBX,4SAAC;gDAAI,WAAU;;kEACb,4SAAC,oIAAA,CAAA,QAAK;wDACJ,MAAK;wDACL,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDAClD,WAAU;wDACV,KAAI;wDACJ,KAAI;;;;;;kEAEN,4SAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,UAAU,CAAC;kEAEX,cAAA,4SAAC,uTAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kDAM1B,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,oIAAA,CAAA,QAAK;0DAAC;;;;;;0DAEP,4SAAC;gDAAI,WAAU;;kEAEb,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAI,WAAU;;kFACb,4SAAC,uTAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,4SAAC;;0FACC,4SAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAsB;;;;;;4EACpC,CAAC,wCACA,4SAAC;gFAAE,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;0EAMnD,4SAAC;gEAAI,WAAU;;oEACZ,CAAC,wCACA,4SAAC,qIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,MAAK;wEACL,SAAS;kFACV;;;;;;kFAIH,4SAAC,uIAAA,CAAA,WAAQ;wEACP,IAAG;wEACH,SAAS,SAAS,OAAO,CAAC,QAAQ,CAAC;wEACnC,iBAAiB,CAAC,UAChB,mBAAmB,gBAAgB;wEAErC,UAAU,CAAC;;;;;;;;;;;;;;;;;;kEAMjB,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAI,WAAU;;kFACb,4SAAC,uTAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;kFACpB,4SAAC;;0FACC,4SAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAe;;;;;;0FAC9B,4SAAC;gFAAE,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;0EAKjD,4SAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS,SAAS,OAAO,CAAC,QAAQ,CAAC;gEACnC,iBAAiB,CAAC,UAChB,mBAAmB,SAAS;gEAE9B,QAAQ;;;;;;;;;;;;kEAKZ,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAI,WAAU;;kFACb,4SAAC,6TAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;kFACvB,4SAAC;;0FACC,4SAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAa;;;;;;0FAC5B,4SAAC;gFAAE,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;0EAKjD,4SAAC,uIAAA,CAAA,WAAQ;gEACP,IAAG;gEACH,SAAS,SAAS,OAAO,CAAC,QAAQ,CAAC;gEACnC,iBAAiB,CAAC,UAChB,mBAAmB,OAAO;gEAE5B,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAStB,4SAAC,qIAAA,CAAA,eAAY;kCACX,cAAA,4SAAC,qIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,QAAQ;sCAAQ;;;;;;;;;;;;;;;;;;;;;;;AAOjD;GAtQgB;KAAA", "debugId": null}}, {"offset": {"line": 7329, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/confirmation-dialog.tsx"], "sourcesContent": ["'use client'\n\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\n\ninterface ConfirmationDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  variant?: 'default' | 'destructive'\n  onConfirm: () => void\n  loading?: boolean\n}\n\nexport function ConfirmationDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = '确认',\n  cancelText = '取消',\n  variant = 'default',\n  onConfirm,\n  loading = false,\n}: ConfirmationDialogProps) {\n  return (\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\n      <AlertDialogContent>\n        <AlertDialogHeader>\n          <AlertDialogTitle>{title}</AlertDialogTitle>\n          <AlertDialogDescription>{description}</AlertDialogDescription>\n        </AlertDialogHeader>\n        <AlertDialogFooter>\n          <AlertDialogCancel disabled={loading}>{cancelText}</AlertDialogCancel>\n          <AlertDialogAction\n            onClick={onConfirm}\n            disabled={loading}\n            className={variant === 'destructive' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}\n          >\n            {loading ? '加载中...' : confirmText}\n          </AlertDialogAction>\n        </AlertDialogFooter>\n      </AlertDialogContent>\n    </AlertDialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAyBO,SAAS,mBAAmB,EACjC,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,IAAI,EAClB,aAAa,IAAI,EACjB,UAAU,SAAS,EACnB,SAAS,EACT,UAAU,KAAK,EACS;IACxB,qBACE,4SAAC,8IAAA,CAAA,cAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,4SAAC,8IAAA,CAAA,qBAAkB;;8BACjB,4SAAC,8IAAA,CAAA,oBAAiB;;sCAChB,4SAAC,8IAAA,CAAA,mBAAgB;sCAAE;;;;;;sCACnB,4SAAC,8IAAA,CAAA,yBAAsB;sCAAE;;;;;;;;;;;;8BAE3B,4SAAC,8IAAA,CAAA,oBAAiB;;sCAChB,4SAAC,8IAAA,CAAA,oBAAiB;4BAAC,UAAU;sCAAU;;;;;;sCACvC,4SAAC,8IAAA,CAAA,oBAAiB;4BAChB,SAAS;4BACT,UAAU;4BACV,WAAW,YAAY,gBAAgB,uEAAuE;sCAE7G,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;;AAMlC;KA/BgB", "debugId": null}}, {"offset": {"line": 7415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointment-filters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { CalendarIcon } from 'lucide-react'\nimport { format } from 'date-fns'\n\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from '@/components/ui/select'\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from '@/components/ui/popover'\nimport { Calendar } from '@/components/ui/calendar'\nimport { cn } from '@/lib/utils'\nimport { t } from '@/lib/translations'\n\nexport interface AppointmentFilters {\n  search: string\n  status: string\n  dateFrom: Date | undefined\n  dateTo: Date | undefined\n}\n\ninterface AppointmentFiltersProps {\n  filters: AppointmentFilters\n  onFiltersChange: (filters: AppointmentFilters) => void\n}\n\nexport function AppointmentFiltersComponent({\n  filters,\n  onFiltersChange,\n}: AppointmentFiltersProps) {\n  const [dateFromOpen, setDateFromOpen] = useState(false)\n  const [dateToOpen, setDateToOpen] = useState(false)\n\n  const updateFilter = (key: keyof AppointmentFilters, value: any) => {\n    onFiltersChange({\n      ...filters,\n      [key]: value,\n    })\n  }\n\n  const clearFilters = () => {\n    onFiltersChange({\n      search: '',\n      status: 'all',\n      dateFrom: undefined,\n      dateTo: undefined,\n    })\n  }\n\n  const hasActiveFilters =\n    filters.search ||\n    (filters.status && filters.status !== 'all') ||\n    filters.dateFrom ||\n    filters.dateTo\n\n  return (\n    <div className=\"space-y-4 p-4 border rounded-lg bg-muted/50\">\n      <div className=\"flex items-center justify-between\">\n        <h3 className=\"text-sm font-medium\">筛选</h3>\n        {hasActiveFilters && (\n          <Button variant=\"ghost\" size=\"sm\" onClick={clearFilters}>\n            清除全部\n          </Button>\n        )}\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        {/* Search */}\n        <div className=\"space-y-2\">\n          <Label htmlFor=\"search\">搜索</Label>\n          <Input\n            id=\"search\"\n            placeholder=\"患者姓名、治疗项目...\"\n            value={filters.search}\n            onChange={(e) => updateFilter('search', e.target.value)}\n          />\n        </div>\n\n        {/* Status Filter */}\n        <div className=\"space-y-2\">\n          <Label>状态</Label>\n          <Select\n            value={filters.status}\n            onValueChange={(value) => updateFilter('status', value)}\n          >\n            <SelectTrigger>\n              <SelectValue placeholder=\"所有状态\" />\n            </SelectTrigger>\n            <SelectContent>\n              <SelectItem value=\"all\">所有状态</SelectItem>\n              <SelectItem value=\"scheduled\">已安排</SelectItem>\n              <SelectItem value=\"completed\">已完成</SelectItem>\n              <SelectItem value=\"cancelled\">已取消</SelectItem>\n            </SelectContent>\n          </Select>\n        </div>\n\n        {/* Date From */}\n        <div className=\"space-y-2\">\n          <Label>开始日期</Label>\n          <Popover open={dateFromOpen} onOpenChange={setDateFromOpen}>\n            <PopoverTrigger asChild>\n              <Button\n                variant=\"outline\"\n                className={cn(\n                  'w-full justify-start text-left font-normal',\n                  !filters.dateFrom && 'text-muted-foreground'\n                )}\n              >\n                <CalendarIcon className=\"mr-2 h-4 w-4\" />\n                {filters.dateFrom ? (\n                  format(filters.dateFrom, 'PPP')\n                ) : (\n                  <span>选择日期</span>\n                )}\n              </Button>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-auto p-0\" align=\"start\">\n              <Calendar\n                mode=\"single\"\n                selected={filters.dateFrom}\n                onSelect={(date) => {\n                  updateFilter('dateFrom', date)\n                  setDateFromOpen(false)\n                }}\n                initialFocus\n              />\n            </PopoverContent>\n          </Popover>\n        </div>\n\n        {/* Date To */}\n        <div className=\"space-y-2\">\n          <Label>结束日期</Label>\n          <Popover open={dateToOpen} onOpenChange={setDateToOpen}>\n            <PopoverTrigger asChild>\n              <Button\n                variant=\"outline\"\n                className={cn(\n                  'w-full justify-start text-left font-normal',\n                  !filters.dateTo && 'text-muted-foreground'\n                )}\n              >\n                <CalendarIcon className=\"mr-2 h-4 w-4\" />\n                {filters.dateTo ? (\n                  format(filters.dateTo, 'PPP')\n                ) : (\n                  <span>选择日期</span>\n                )}\n              </Button>\n            </PopoverTrigger>\n            <PopoverContent className=\"w-auto p-0\" align=\"start\">\n              <Calendar\n                mode=\"single\"\n                selected={filters.dateTo}\n                onSelect={(date) => {\n                  updateFilter('dateTo', date)\n                  setDateToOpen(false)\n                }}\n                disabled={(date) =>\n                  filters.dateFrom ? date < filters.dateFrom : false\n                }\n                initialFocus\n              />\n            </PopoverContent>\n          </Popover>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAKA;AACA;;;AAtBA;;;;;;;;;;;AAqCO,SAAS,4BAA4B,EAC1C,OAAO,EACP,eAAe,EACS;;IACxB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe,CAAC,KAA+B;QACnD,gBAAgB;YACd,GAAG,OAAO;YACV,CAAC,IAAI,EAAE;QACT;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,QAAQ;QACV;IACF;IAEA,MAAM,mBACJ,QAAQ,MAAM,IACb,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,SACtC,QAAQ,QAAQ,IAChB,QAAQ,MAAM;IAEhB,qBACE,4SAAC;QAAI,WAAU;;0BACb,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCAAsB;;;;;;oBACnC,kCACC,4SAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAQ,MAAK;wBAAK,SAAS;kCAAc;;;;;;;;;;;;0BAM7D,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAS;;;;;;0CACxB,4SAAC,oIAAA,CAAA,QAAK;gCACJ,IAAG;gCACH,aAAY;gCACZ,OAAO,QAAQ,MAAM;gCACrB,UAAU,CAAC,IAAM,aAAa,UAAU,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAK1D,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,4SAAC,qIAAA,CAAA,SAAM;gCACL,OAAO,QAAQ,MAAM;gCACrB,eAAe,CAAC,QAAU,aAAa,UAAU;;kDAEjD,4SAAC,qIAAA,CAAA,gBAAa;kDACZ,cAAA,4SAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,4SAAC,qIAAA,CAAA,gBAAa;;0DACZ,4SAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,4SAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,4SAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;0DAC9B,4SAAC,qIAAA,CAAA,aAAU;gDAAC,OAAM;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;kCAMpC,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,4SAAC,sIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAc,cAAc;;kDACzC,4SAAC,sIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,QAAQ,QAAQ,IAAI;;8DAGvB,4SAAC,qSAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,QAAQ,QAAQ,GACf,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,QAAQ,EAAE,uBAEzB,4SAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIZ,4SAAC,sIAAA,CAAA,iBAAc;wCAAC,WAAU;wCAAa,OAAM;kDAC3C,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4CACP,MAAK;4CACL,UAAU,QAAQ,QAAQ;4CAC1B,UAAU,CAAC;gDACT,aAAa,YAAY;gDACzB,gBAAgB;4CAClB;4CACA,YAAY;;;;;;;;;;;;;;;;;;;;;;;kCAOpB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,oIAAA,CAAA,QAAK;0CAAC;;;;;;0CACP,4SAAC,sIAAA,CAAA,UAAO;gCAAC,MAAM;gCAAY,cAAc;;kDACvC,4SAAC,sIAAA,CAAA,iBAAc;wCAAC,OAAO;kDACrB,cAAA,4SAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,QAAQ,MAAM,IAAI;;8DAGrB,4SAAC,qSAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDACvB,QAAQ,MAAM,GACb,CAAA,GAAA,gNAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,MAAM,EAAE,uBAEvB,4SAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIZ,4SAAC,sIAAA,CAAA,iBAAc;wCAAC,WAAU;wCAAa,OAAM;kDAC3C,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4CACP,MAAK;4CACL,UAAU,QAAQ,MAAM;4CACxB,UAAU,CAAC;gDACT,aAAa,UAAU;gDACvB,cAAc;4CAChB;4CACA,UAAU,CAAC,OACT,QAAQ,QAAQ,GAAG,OAAO,QAAQ,QAAQ,GAAG;4CAE/C,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5B;GAhJgB;KAAA", "debugId": null}}, {"offset": {"line": 7783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/ui/tabs.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot='tabs'\r\n      className={cn('flex flex-col gap-2', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot='tabs-list'\r\n      className={cn(\r\n        'bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot='tabs-trigger'\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot='tabs-content'\r\n      className={cn('flex-1 outline-none', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,4SAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,4SAAC,mRAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,4SAAC,mRAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 7859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/lib/translations.ts"], "sourcesContent": ["// 中文翻译文件\nexport const translations = {\n  // 导航菜单\n  nav: {\n    dashboard: '仪表板',\n    appointments: '预约管理',\n    patients: '患者管理',\n    treatments: '治疗项目',\n    admin: '系统管理',\n    account: '账户',\n    profile: '个人资料',\n    login: '登录',\n    overview: '概览'\n  },\n\n  // 仪表板\n  dashboard: {\n    title: '诊所控制台 🏥',\n    subtitle: '欢迎使用您的诊所管理系统',\n    metrics: {\n      todayAppointments: '今日预约',\n      recentPatients: '近期患者',\n      totalPatients: '患者总数',\n      activetreatments: '可用治疗',\n      scheduledForToday: '今日安排',\n      appointmentsScheduledForToday: '今日安排的预约',\n      newPatientsThisWeek: '本周新患者',\n      patientsRegisteredInLast7Days: '过去7天注册的患者',\n      totalRegisteredPatients: '注册患者总数',\n      completePatientDatabase: '完整患者数据库',\n      treatmentOptionsAvailable: '可用治疗选项',\n      fullServiceCatalog: '完整服务目录',\n      active: '活跃',\n      last7Days: '过去7天',\n      allTime: '全部时间',\n      available: '可用'\n    },\n    errors: {\n      loadingDashboard: '加载仪表板时出错',\n      failedToLoadMetrics: '无法加载仪表板数据'\n    }\n  },\n\n  // 预约管理\n  appointments: {\n    title: '预约管理',\n    subtitle: '管理患者预约和排程',\n    newAppointment: '新建预约',\n    editAppointment: '编辑预约',\n    appointmentDetails: '预约详情',\n    appointmentsCount: '个预约',\n    loadingAppointments: '加载预约中...',\n    noAppointments: '暂无预约',\n    filters: {\n      all: '全部',\n      today: '今天',\n      thisWeek: '本周',\n      thisMonth: '本月',\n      status: '状态',\n      dateRange: '日期范围'\n    },\n    status: {\n      scheduled: '已安排',\n      confirmed: '已确认',\n      inProgress: '进行中',\n      completed: '已完成',\n      cancelled: '已取消',\n      noShow: '未到场'\n    },\n    form: {\n      patient: '患者',\n      selectPatient: '选择患者',\n      treatment: '治疗项目',\n      selectTreatment: '选择治疗项目',\n      date: '日期',\n      time: '时间',\n      notes: '备注',\n      notesPlaceholder: '预约备注（可选）',\n      status: '状态'\n    }\n  },\n\n  // 患者管理\n  patients: {\n    title: '患者管理',\n    subtitle: '管理患者信息和病历',\n    newPatient: '新建患者',\n    editPatient: '编辑患者',\n    patientDetails: '患者详情',\n    patientsCount: '位患者',\n    loadingPatients: '加载患者中...',\n    noPatients: '暂无患者',\n    searchPlaceholder: '按姓名、电话或邮箱搜索患者',\n    form: {\n      fullName: '姓名',\n      fullNamePlaceholder: '请输入患者姓名',\n      phone: '电话',\n      phonePlaceholder: '请输入电话号码',\n      email: '邮箱',\n      emailPlaceholder: '请输入邮箱地址（可选）',\n      medicalNotes: '病历备注',\n      medicalNotesPlaceholder: '请输入病历备注（可选）'\n    }\n  },\n\n  // 治疗项目\n  treatments: {\n    title: '治疗项目',\n    subtitle: '管理诊所治疗服务',\n    newTreatment: '新建治疗',\n    editTreatment: '编辑治疗',\n    treatmentDetails: '治疗详情',\n    treatmentsCount: '个治疗项目',\n    loadingTreatments: '加载治疗项目中...',\n    noTreatments: '暂无治疗项目',\n    form: {\n      name: '治疗名称',\n      namePlaceholder: '请输入治疗名称',\n      description: '治疗描述',\n      descriptionPlaceholder: '请输入治疗描述',\n      duration: '治疗时长',\n      durationPlaceholder: '请输入治疗时长（分钟）',\n      price: '价格',\n      pricePlaceholder: '请输入价格'\n    }\n  },\n\n  // 系统管理\n  admin: {\n    title: '系统管理',\n    subtitle: '管理用户权限和系统设置',\n    userManagement: '用户管理',\n    roleManagement: '角色管理',\n    systemSettings: '系统设置',\n    users: '用户',\n    roles: {\n      admin: '管理员',\n      doctor: '医生',\n      frontDesk: '前台'\n    }\n  },\n\n  // 通用文本\n  common: {\n    // 操作按钮\n    actions: {\n      save: '保存',\n      cancel: '取消',\n      edit: '编辑',\n      delete: '删除',\n      view: '查看',\n      search: '搜索',\n      filter: '筛选',\n      reset: '重置',\n      submit: '提交',\n      close: '关闭',\n      confirm: '确认',\n      back: '返回',\n      next: '下一步',\n      previous: '上一步',\n      add: '添加',\n      remove: '移除',\n      update: '更新',\n      create: '创建'\n    },\n\n    // 状态\n    status: {\n      loading: '加载中...',\n      success: '成功',\n      error: '错误',\n      warning: '警告',\n      info: '信息',\n      pending: '待处理',\n      active: '活跃',\n      inactive: '非活跃',\n      enabled: '已启用',\n      disabled: '已禁用'\n    },\n\n    // 时间相关\n    time: {\n      today: '今天',\n      yesterday: '昨天',\n      tomorrow: '明天',\n      thisWeek: '本周',\n      lastWeek: '上周',\n      nextWeek: '下周',\n      thisMonth: '本月',\n      lastMonth: '上月',\n      nextMonth: '下月',\n      thisYear: '今年',\n      lastYear: '去年',\n      nextYear: '明年'\n    },\n\n    // 确认对话框\n    confirmDialog: {\n      title: '确认操作',\n      deleteTitle: '确认删除',\n      deleteMessage: '您确定要删除这个项目吗？此操作无法撤销。',\n      cancelTitle: '确认取消',\n      cancelMessage: '您确定要取消吗？未保存的更改将丢失。',\n      saveTitle: '确认保存',\n      saveMessage: '您确定要保存这些更改吗？'\n    }\n  },\n\n  // 表单验证\n  validation: {\n    required: '此字段为必填项',\n    email: '请输入有效的邮箱地址',\n    phone: '请输入有效的电话号码',\n    minLength: '至少需要 {min} 个字符',\n    maxLength: '最多允许 {max} 个字符',\n    number: '请输入有效的数字',\n    positive: '请输入正数',\n    date: '请选择有效的日期',\n    time: '请选择有效的时间'\n  },\n\n  // 错误消息\n  errors: {\n    general: '发生了未知错误，请稍后重试',\n    network: '网络连接错误，请检查您的网络连接',\n    unauthorized: '您没有权限执行此操作',\n    notFound: '请求的资源未找到',\n    serverError: '服务器错误，请稍后重试',\n    validationError: '输入数据验证失败',\n    loadFailed: '加载数据失败',\n    saveFailed: '保存数据失败',\n    deleteFailed: '删除数据失败',\n    updateFailed: '更新数据失败',\n    createFailed: '创建数据失败'\n  },\n\n  // 成功消息\n  success: {\n    saved: '保存成功',\n    deleted: '删除成功',\n    updated: '更新成功',\n    created: '创建成功',\n    sent: '发送成功',\n    uploaded: '上传成功',\n    downloaded: '下载成功'\n  }\n} as const;\n\n// 翻译工具函数\nexport function t(key: string, params?: Record<string, string | number>): string {\n  const keys = key.split('.');\n  let value: any = translations;\n  \n  for (const k of keys) {\n    if (value && typeof value === 'object' && k in value) {\n      value = value[k];\n    } else {\n      console.warn(`Translation key not found: ${key}`);\n      return key; // 返回原始key作为fallback\n    }\n  }\n  \n  if (typeof value !== 'string') {\n    console.warn(`Translation value is not a string: ${key}`);\n    return key;\n  }\n  \n  // 处理参数替换\n  if (params) {\n    return value.replace(/\\{(\\w+)\\}/g, (match, paramKey) => {\n      return params[paramKey]?.toString() || match;\n    });\n  }\n  \n  return value;\n}\n\n// 类型定义\nexport type TranslationKey = keyof typeof translations;\n"], "names": [], "mappings": "AAAA,SAAS;;;;;AACF,MAAM,eAAe;IAC1B,OAAO;IACP,KAAK;QACH,WAAW;QACX,cAAc;QACd,UAAU;QACV,YAAY;QACZ,OAAO;QACP,SAAS;QACT,SAAS;QACT,OAAO;QACP,UAAU;IACZ;IAEA,MAAM;IACN,WAAW;QACT,OAAO;QACP,UAAU;QACV,SAAS;YACP,mBAAmB;YACnB,gBAAgB;YAChB,eAAe;YACf,kBAAkB;YAClB,mBAAmB;YACnB,+BAA+B;YAC/B,qBAAqB;YACrB,+BAA+B;YAC/B,yBAAyB;YACzB,yBAAyB;YACzB,2BAA2B;YAC3B,oBAAoB;YACpB,QAAQ;YACR,WAAW;YACX,SAAS;YACT,WAAW;QACb;QACA,QAAQ;YACN,kBAAkB;YAClB,qBAAqB;QACvB;IACF;IAEA,OAAO;IACP,cAAc;QACZ,OAAO;QACP,UAAU;QACV,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QACpB,mBAAmB;QACnB,qBAAqB;QACrB,gBAAgB;QAChB,SAAS;YACP,KAAK;YACL,OAAO;YACP,UAAU;YACV,WAAW;YACX,QAAQ;YACR,WAAW;QACb;QACA,QAAQ;YACN,WAAW;YACX,WAAW;YACX,YAAY;YACZ,WAAW;YACX,WAAW;YACX,QAAQ;QACV;QACA,MAAM;YACJ,SAAS;YACT,eAAe;YACf,WAAW;YACX,iBAAiB;YACjB,MAAM;YACN,MAAM;YACN,OAAO;YACP,kBAAkB;YAClB,QAAQ;QACV;IACF;IAEA,OAAO;IACP,UAAU;QACR,OAAO;QACP,UAAU;QACV,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,eAAe;QACf,iBAAiB;QACjB,YAAY;QACZ,mBAAmB;QACnB,MAAM;YACJ,UAAU;YACV,qBAAqB;YACrB,OAAO;YACP,kBAAkB;YAClB,OAAO;YACP,kBAAkB;YAClB,cAAc;YACd,yBAAyB;QAC3B;IACF;IAEA,OAAO;IACP,YAAY;QACV,OAAO;QACP,UAAU;QACV,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,cAAc;QACd,MAAM;YACJ,MAAM;YACN,iBAAiB;YACjB,aAAa;YACb,wBAAwB;YACxB,UAAU;YACV,qBAAqB;YACrB,OAAO;YACP,kBAAkB;QACpB;IACF;IAEA,OAAO;IACP,OAAO;QACL,OAAO;QACP,UAAU;QACV,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,OAAO;QACP,OAAO;YACL,OAAO;YACP,QAAQ;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,QAAQ;QACN,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,MAAM;YACN,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,UAAU;YACV,KAAK;YACL,QAAQ;YACR,QAAQ;YACR,QAAQ;QACV;QAEA,KAAK;QACL,QAAQ;YACN,SAAS;YACT,SAAS;YACT,OAAO;YACP,SAAS;YACT,MAAM;YACN,SAAS;YACT,QAAQ;YACR,UAAU;YACV,SAAS;YACT,UAAU;QACZ;QAEA,OAAO;QACP,MAAM;YACJ,OAAO;YACP,WAAW;YACX,UAAU;YACV,UAAU;YACV,UAAU;YACV,UAAU;YACV,WAAW;YACX,WAAW;YACX,WAAW;YACX,UAAU;YACV,UAAU;YACV,UAAU;QACZ;QAEA,QAAQ;QACR,eAAe;YACb,OAAO;YACP,aAAa;YACb,eAAe;YACf,aAAa;YACb,eAAe;YACf,WAAW;YACX,aAAa;QACf;IACF;IAEA,OAAO;IACP,YAAY;QACV,UAAU;QACV,OAAO;QACP,OAAO;QACP,WAAW;QACX,WAAW;QACX,QAAQ;QACR,UAAU;QACV,MAAM;QACN,MAAM;IACR;IAEA,OAAO;IACP,QAAQ;QACN,SAAS;QACT,SAAS;QACT,cAAc;QACd,UAAU;QACV,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,cAAc;QACd,cAAc;IAChB;IAEA,OAAO;IACP,SAAS;QACP,OAAO;QACP,SAAS;QACT,SAAS;QACT,SAAS;QACT,MAAM;QACN,UAAU;QACV,YAAY;IACd;AACF;AAGO,SAAS,EAAE,GAAW,EAAE,MAAwC;IACrE,MAAM,OAAO,IAAI,KAAK,CAAC;IACvB,IAAI,QAAa;IAEjB,KAAK,MAAM,KAAK,KAAM;QACpB,IAAI,SAAS,OAAO,UAAU,YAAY,KAAK,OAAO;YACpD,QAAQ,KAAK,CAAC,EAAE;QAClB,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,2BAA2B,EAAE,KAAK;YAChD,OAAO,KAAK,oBAAoB;QAClC;IACF;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,KAAK;QACxD,OAAO;IACT;IAEA,SAAS;IACT,IAAI,QAAQ;QACV,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO;YACzC,OAAO,MAAM,CAAC,SAAS,EAAE,cAAc;QACzC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 8130, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/nord-coast/frontend-nextjs/src/components/appointments/appointments-list.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { useRole, PermissionGate } from '@/lib/role-context';\nimport { IconPlus, IconCalendar, IconEdit, IconTrash, IconCheck, IconX, IconList, IconTable } from '@tabler/icons-react';\nimport { appointmentsApi } from '@/lib/api';\nimport { Appointment } from '@/types/clinic';\nimport { DataTable } from '@/components/ui/table/data-table';\nimport { DataTableToolbar } from '@/components/ui/table/data-table-toolbar';\nimport { ColumnDef } from '@tanstack/react-table';\nimport { useDataTable } from '@/hooks/use-data-table';\nimport { AppointmentFormDialog } from './appointment-form-dialog';\nimport { AppointmentCalendar } from './appointment-calendar';\nimport { AppointmentStatusManager } from './appointment-status-manager';\nimport { AppointmentReminderSettings } from './appointment-reminder-settings';\nimport { ConfirmationDialog } from '@/components/ui/confirmation-dialog';\nimport { AppointmentFiltersComponent, AppointmentFilters } from './appointment-filters';\nimport { toast } from 'sonner';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { t } from '@/lib/translations';\n\n// Status badge component\nconst StatusBadge = ({ status }: { status: string }) => {\n  const getStatusColor = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'scheduled':\n        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';\n      case 'confirmed':\n        return 'bg-green-100 text-green-800 hover:bg-green-200';\n      case 'completed':\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n      case 'cancelled':\n        return 'bg-red-100 text-red-800 hover:bg-red-200';\n      case 'no-show':\n        return 'bg-amber-100 text-amber-800 hover:bg-amber-200';\n      default:\n        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status.toLowerCase()) {\n      case 'scheduled':\n        return '已预约';\n      case 'confirmed':\n        return '已确认';\n      case 'completed':\n        return '已完成';\n      case 'cancelled':\n        return '已取消';\n      case 'no-show':\n        return '未到诊';\n      default:\n        return status;\n    }\n  };\n\n  return (\n    <Badge className={getStatusColor(status)}>\n      {getStatusText(status)}\n    </Badge>\n  );\n};\n\n// Table columns definition - will be created inside component to access handlers\nconst createColumns = (\n  onEdit: (appointment: Appointment) => void,\n  onDelete: (appointment: Appointment) => void,\n  onStatusChange: (appointment: Appointment, status: 'completed' | 'cancelled') => void\n): ColumnDef<Appointment>[] => [\n  {\n    accessorKey: 'patient',\n    header: '患者',\n    cell: ({ row }) => {\n      const patient = row.getValue('patient') as any;\n      return patient?.fullName || '未知患者';\n    },\n  },\n  {\n    accessorKey: 'treatment',\n    header: '治疗项目',\n    cell: ({ row }) => {\n      const treatment = row.getValue('treatment') as any;\n      return treatment?.name || '未知治疗';\n    },\n  },\n  {\n    accessorKey: 'appointmentDate',\n    header: '日期时间',\n    cell: ({ row }) => {\n      const date = row.getValue('appointmentDate') as string;\n      const dateObj = new Date(date);\n      return (\n        <div>\n          <div>{dateObj.toLocaleDateString()}</div>\n          <div className=\"text-sm text-muted-foreground\">\n            {dateObj.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n          </div>\n        </div>\n      );\n    },\n  },\n  {\n    accessorKey: 'price',\n    header: '价格',\n    cell: ({ row }) => {\n      const price = row.getValue('price') as number;\n      return `¥${price}`;\n    },\n  },\n  {\n    accessorKey: 'status',\n    header: '状态',\n    cell: ({ row }) => {\n      const appointment = row.original;\n      return (\n        <AppointmentStatusManager\n          appointment={appointment}\n          onStatusChange={(updatedAppointment) => {\n            // Update the appointment in the local state\n            setAppointments(prev =>\n              prev.map(apt =>\n                apt.id === updatedAppointment.id ? updatedAppointment : apt\n              )\n            );\n          }}\n          compact\n        />\n      );\n    },\n  },\n  {\n    id: 'actions',\n    header: '操作',\n    cell: ({ row }) => {\n      const appointment = row.original;\n\n      return (\n        <div className=\"flex items-center gap-2\">\n          {appointment.status === 'scheduled' && (\n            <>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onStatusChange(appointment, 'completed')}\n                className=\"text-green-600 hover:text-green-700\"\n              >\n                <IconCheck className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={() => onStatusChange(appointment, 'cancelled')}\n                className=\"text-red-600 hover:text-red-700\"\n              >\n                <IconX className=\"h-4 w-4\" />\n              </Button>\n            </>\n          )}\n\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"outline\" size=\"sm\">\n                <IconEdit className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent>\n              <DropdownMenuItem onClick={() => onEdit(appointment)}>\n                <IconEdit className=\"h-4 w-4 mr-2\" />\n                {t('common.actions.edit')}\n              </DropdownMenuItem>\n              <DropdownMenuItem\n                onClick={() => onDelete(appointment)}\n                className=\"text-red-600\"\n              >\n                <IconTrash className=\"h-4 w-4 mr-2\" />\n                {t('common.actions.delete')}\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      );\n    },\n  },\n];\n\nexport function AppointmentsList() {\n  const { hasPermission, user } = useRole();\n  const [appointments, setAppointments] = useState<Appointment[]>([]);\n  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [formDialogOpen, setFormDialogOpen] = useState(false);\n  const [editingAppointment, setEditingAppointment] = useState<Appointment | undefined>();\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);\n  const [appointmentToDelete, setAppointmentToDelete] = useState<Appointment | undefined>();\n  const [actionLoading, setActionLoading] = useState(false);\n  const [filters, setFilters] = useState<AppointmentFilters>({\n    search: '',\n    status: 'all',\n    dateFrom: undefined,\n    dateTo: undefined,\n  });\n  const [currentView, setCurrentView] = useState<'table' | 'calendar'>('table');\n\n  const fetchAppointments = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const response = await appointmentsApi.getAll({ limit: 100 });\n      setAppointments(response.docs);\n    } catch (err) {\n      console.error('Failed to fetch appointments:', err);\n      setError('Failed to load appointments. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Filter appointments based on current filters\n  useEffect(() => {\n    let filtered = [...appointments];\n\n    // Search filter\n    if (filters.search) {\n      const searchLower = filters.search.toLowerCase();\n      filtered = filtered.filter(appointment =>\n        appointment.patient.fullName.toLowerCase().includes(searchLower) ||\n        appointment.treatment.name.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Status filter\n    if (filters.status && filters.status !== 'all') {\n      filtered = filtered.filter(appointment => appointment.status === filters.status);\n    }\n\n    // Date range filter\n    if (filters.dateFrom) {\n      filtered = filtered.filter(appointment =>\n        new Date(appointment.appointmentDate) >= filters.dateFrom!\n      );\n    }\n\n    if (filters.dateTo) {\n      const endOfDay = new Date(filters.dateTo);\n      endOfDay.setHours(23, 59, 59, 999);\n      filtered = filtered.filter(appointment =>\n        new Date(appointment.appointmentDate) <= endOfDay\n      );\n    }\n\n    setFilteredAppointments(filtered);\n  }, [appointments, filters]);\n\n  useEffect(() => {\n    fetchAppointments();\n  }, []);\n\n  const handleEdit = (appointment: Appointment) => {\n    setEditingAppointment(appointment);\n    setFormDialogOpen(true);\n  };\n\n  const handleDelete = (appointment: Appointment) => {\n    setAppointmentToDelete(appointment);\n    setDeleteDialogOpen(true);\n  };\n\n  const handleStatusChange = async (appointment: Appointment, status: 'completed' | 'cancelled') => {\n    setActionLoading(true);\n    try {\n      await appointmentsApi.update(appointment.id, { status });\n      toast.success(`Appointment ${status} successfully`);\n      fetchAppointments(); // Refresh the list\n    } catch (error) {\n      console.error('Failed to update appointment status:', error);\n      toast.error('Failed to update appointment status');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const handleDeleteConfirm = async () => {\n    if (!appointmentToDelete) return;\n\n    setActionLoading(true);\n    try {\n      await appointmentsApi.delete(appointmentToDelete.id);\n      toast.success('Appointment deleted successfully');\n      setDeleteDialogOpen(false);\n      setAppointmentToDelete(undefined);\n      fetchAppointments(); // Refresh the list\n    } catch (error) {\n      console.error('Failed to delete appointment:', error);\n      toast.error('Failed to delete appointment');\n    } finally {\n      setActionLoading(false);\n    }\n  };\n\n  const handleFormSuccess = () => {\n    setFormDialogOpen(false);\n    setEditingAppointment(undefined);\n    fetchAppointments(); // Refresh the list\n  };\n\n  const handleNewAppointment = () => {\n    setEditingAppointment(undefined);\n    setFormDialogOpen(true);\n  };\n\n  const columns = createColumns(handleEdit, handleDelete, handleStatusChange);\n\n  // Create table using useDataTable hook\n  const { table } = useDataTable({\n    data: filteredAppointments,\n    columns,\n    pageCount: Math.ceil(filteredAppointments.length / 10), // Assuming 10 items per page\n    shallow: true, // Use shallow routing to prevent infinite loops\n    debounceMs: 500\n  });\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4\"></div>\n          <p className=\"text-muted-foreground\">{t('appointments.loadingAppointments')}</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"flex items-center justify-center py-8\">\n        <div className=\"text-center\">\n          <p className=\"text-red-600 mb-4\">{error}</p>\n          <Button onClick={fetchAppointments} variant=\"outline\">\n            重试\n          </Button>\n        </div>\n      </div>\n    );\n  }\n\n  if (appointments.length === 0) {\n    return (\n      <>\n        <div className=\"flex items-center justify-center py-8\">\n          <div className=\"text-center\">\n            <IconCalendar className=\"h-12 w-12 text-muted-foreground mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium mb-2\">{t('appointments.noAppointments')}</h3>\n            <p className=\"text-muted-foreground mb-4\">\n              开始安排您的第一个预约。\n            </p>\n            <PermissionGate permission=\"canCreateAppointments\">\n              <Button onClick={handleNewAppointment}>\n                <IconPlus className=\"h-4 w-4 mr-2\" />\n                {t('appointments.newAppointment')}\n              </Button>\n            </PermissionGate>\n          </div>\n        </div>\n\n        <AppointmentFormDialog\n          open={formDialogOpen}\n          onOpenChange={setFormDialogOpen}\n          appointment={editingAppointment}\n          onSuccess={handleFormSuccess}\n        />\n      </>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"space-y-4\">\n        {/* Header with View Toggle and New Appointment button */}\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <h3 className=\"text-lg font-medium\">\n              {filteredAppointments.length} / {appointments.length} {t('appointments.appointmentsCount')}\n            </h3>\n\n            {/* View Toggle */}\n            <Tabs value={currentView} onValueChange={(value) => setCurrentView(value as 'table' | 'calendar')}>\n              <TabsList className=\"grid w-full grid-cols-2\">\n                <TabsTrigger value=\"table\" className=\"flex items-center gap-2\">\n                  <IconTable className=\"h-4 w-4\" />\n                  表格视图\n                </TabsTrigger>\n                <TabsTrigger value=\"calendar\" className=\"flex items-center gap-2\">\n                  <IconCalendar className=\"h-4 w-4\" />\n                  日历视图\n                </TabsTrigger>\n              </TabsList>\n            </Tabs>\n          </div>\n\n          <div className=\"flex items-center gap-2\">\n            <AppointmentReminderSettings />\n            <PermissionGate permission=\"canCreateAppointments\">\n              <Button onClick={handleNewAppointment}>\n                <IconPlus className=\"h-4 w-4 mr-2\" />\n                {t('appointments.newAppointment')}\n              </Button>\n            </PermissionGate>\n          </div>\n        </div>\n\n        {/* Filters - only show for table view */}\n        {currentView === 'table' && (\n          <AppointmentFiltersComponent\n            filters={filters}\n            onFiltersChange={setFilters}\n          />\n        )}\n\n        {/* Content based on current view */}\n        {currentView === 'table' ? (\n          <>\n            {/* Data Table */}\n            <DataTable table={table}>\n              <DataTableToolbar table={table} />\n            </DataTable>\n          </>\n        ) : (\n          /* Calendar View */\n          <AppointmentCalendar\n            onNewAppointment={handleNewAppointment}\n            onEditAppointment={handleEditAppointment}\n            onDeleteAppointment={handleDeleteAppointment}\n          />\n        )}\n\n        {filteredAppointments.length === 0 && appointments.length > 0 && (\n          <div className=\"text-center py-8\">\n            <p className=\"text-muted-foreground\">没有预约符合当前筛选条件。</p>\n          </div>\n        )}\n      </div>\n\n      <AppointmentFormDialog\n        open={formDialogOpen}\n        onOpenChange={setFormDialogOpen}\n        appointment={editingAppointment}\n        onSuccess={handleFormSuccess}\n      />\n\n      <ConfirmationDialog\n        open={deleteDialogOpen}\n        onOpenChange={setDeleteDialogOpen}\n        title=\"删除预约\"\n        description={`您确定要删除 ${appointmentToDelete?.patient.fullName} 的预约吗？此操作无法撤销。`}\n        confirmText=\"删除\"\n        variant=\"destructive\"\n        onConfirm={handleDeleteConfirm}\n        loading={actionLoading}\n      />\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;;;AA3BA;;;;;;;;;;;;;;;;;;;;AA6BA,yBAAyB;AACzB,MAAM,cAAc,CAAC,EAAE,MAAM,EAAsB;IACjD,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,WAAW;YACxB,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,4SAAC,oIAAA,CAAA,QAAK;QAAC,WAAW,eAAe;kBAC9B,cAAc;;;;;;AAGrB;KAxCM;AA0CN,iFAAiF;AACjF,MAAM,gBAAgB,CACpB,QACA,UACA,iBAC6B;QAC7B;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ,CAAC;gBAC7B,OAAO,SAAS,YAAY;YAC9B;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,YAAY,IAAI,QAAQ,CAAC;gBAC/B,OAAO,WAAW,QAAQ;YAC5B;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,OAAO,IAAI,QAAQ,CAAC;gBAC1B,MAAM,UAAU,IAAI,KAAK;gBACzB,qBACE,4SAAC;;sCACC,4SAAC;sCAAK,QAAQ,kBAAkB;;;;;;sCAChC,4SAAC;4BAAI,WAAU;sCACZ,QAAQ,kBAAkB,CAAC,EAAE,EAAE;gCAAE,MAAM;gCAAW,QAAQ;4BAAU;;;;;;;;;;;;YAI7E;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,QAAQ,IAAI,QAAQ,CAAC;gBAC3B,OAAO,CAAC,CAAC,EAAE,OAAO;YACpB;QACF;QACA;YACE,aAAa;YACb,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAChC,qBACE,4SAAC,yKAAA,CAAA,2BAAwB;oBACvB,aAAa;oBACb,gBAAgB,CAAC;wBACf,4CAA4C;wBAC5C,gBAAgB,CAAA,OACd,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,mBAAmB,EAAE,GAAG,qBAAqB;oBAG9D;oBACA,OAAO;;;;;;YAGb;QACF;QACA;YACE,IAAI;YACJ,QAAQ;YACR,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,cAAc,IAAI,QAAQ;gBAEhC,qBACE,4SAAC;oBAAI,WAAU;;wBACZ,YAAY,MAAM,KAAK,6BACtB;;8CACE,4SAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,aAAa;oCAC3C,WAAU;8CAEV,cAAA,4SAAC,yTAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,4SAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS,IAAM,eAAe,aAAa;oCAC3C,WAAU;8CAEV,cAAA,4SAAC,iTAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;sCAKvB,4SAAC,+IAAA,CAAA,eAAY;;8CACX,4SAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,4SAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC7B,cAAA,4SAAC,uTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAGxB,4SAAC,+IAAA,CAAA,sBAAmB;;sDAClB,4SAAC,+IAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO;;8DACtC,4SAAC,uTAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;sDAEL,4SAAC,+IAAA,CAAA,mBAAgB;4CACf,SAAS,IAAM,SAAS;4CACxB,WAAU;;8DAEV,4SAAC,yTAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDACpB,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAMf;QACF;KACD;AAEM,SAAS;;IACd,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACtC,MAAM,CAAC,cAAc,iBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAsB;QACzD,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,QAAQ;IACV;IACA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAwB;IAErE,MAAM,oBAAoB;QACxB,IAAI;YACF,WAAW;YACX,SAAS;YACT,MAAM,WAAW,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC;gBAAE,OAAO;YAAI;YAC3D,iBAAgB,SAAS,IAAI;QAC/B,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,+CAA+C;IAC/C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,WAAW;mBAAI;aAAa;YAEhC,gBAAgB;YAChB,IAAI,QAAQ,MAAM,EAAE;gBAClB,MAAM,cAAc,QAAQ,MAAM,CAAC,WAAW;gBAC9C,WAAW,SAAS,MAAM;kDAAC,CAAA,cACzB,YAAY,OAAO,CAAC,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,gBACpD,YAAY,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAEtD;YAEA,gBAAgB;YAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,OAAO;gBAC9C,WAAW,SAAS,MAAM;kDAAC,CAAA,cAAe,YAAY,MAAM,KAAK,QAAQ,MAAM;;YACjF;YAEA,oBAAoB;YACpB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,WAAW,SAAS,MAAM;kDAAC,CAAA,cACzB,IAAI,KAAK,YAAY,eAAe,KAAK,QAAQ,QAAQ;;YAE7D;YAEA,IAAI,QAAQ,MAAM,EAAE;gBAClB,MAAM,WAAW,IAAI,KAAK,QAAQ,MAAM;gBACxC,SAAS,QAAQ,CAAC,IAAI,IAAI,IAAI;gBAC9B,WAAW,SAAS,MAAM;kDAAC,CAAA,cACzB,IAAI,KAAK,YAAY,eAAe,KAAK;;YAE7C;YAEA,wBAAwB;QAC1B;qCAAG;QAAC;QAAc;KAAQ;IAE1B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,aAAa,CAAC;QAClB,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,uBAAuB;QACvB,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,OAAO,aAA0B;QAC1D,iBAAiB;QACjB,IAAI;YACF,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,YAAY,EAAE,EAAE;gBAAE;YAAO;YACtD,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,OAAO,aAAa,CAAC;YAClD,qBAAqB,mBAAmB;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,qBAAqB;QAE1B,iBAAiB;QACjB,IAAI;YACF,MAAM,oHAAA,CAAA,kBAAe,CAAC,MAAM,CAAC,oBAAoB,EAAE;YACnD,2QAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,oBAAoB;YACpB,uBAAuB;YACvB,qBAAqB,mBAAmB;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,2QAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,oBAAoB;QACxB,kBAAkB;QAClB,sBAAsB;QACtB,qBAAqB,mBAAmB;IAC1C;IAEA,MAAM,uBAAuB;QAC3B,sBAAsB;QACtB,kBAAkB;IACpB;IAEA,MAAM,UAAU,cAAc,YAAY,cAAc;IAExD,uCAAuC;IACvC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,uIAAA,CAAA,eAAY,AAAD,EAAE;QAC7B,MAAM;QACN;QACA,WAAW,KAAK,IAAI,CAAC,qBAAqB,MAAM,GAAG;QACnD,SAAS;QACT,YAAY;IACd;IAEA,IAAI,SAAS;QACX,qBACE,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;;;;;kCACf,4SAAC;wBAAE,WAAU;kCAAyB,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;;;;;;;;;;;IAIhD;IAEA,IAAI,OAAO;QACT,qBACE,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,4SAAC,qIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAmB,SAAQ;kCAAU;;;;;;;;;;;;;;;;;IAM9D;IAEA,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,qBACE;;8BACE,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,+TAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;0CACxB,4SAAC;gCAAG,WAAU;0CAA4B,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;0CAC5C,4SAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,4SAAC,iIAAA,CAAA,iBAAc;gCAAC,YAAW;0CACzB,cAAA,4SAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;;sDACf,4SAAC,uTAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;8BAMX,4SAAC,sKAAA,CAAA,wBAAqB;oBACpB,MAAM;oBACN,cAAc;oBACd,aAAa;oBACb,WAAW;;;;;;;;IAInB;IAEA,qBACE;;0BACE,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAG,WAAU;;4CACX,qBAAqB,MAAM;4CAAC;4CAAI,aAAa,MAAM;4CAAC;4CAAE,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;kDAI3D,4SAAC,mIAAA,CAAA,OAAI;wCAAC,OAAO;wCAAa,eAAe,CAAC,QAAU,eAAe;kDACjE,cAAA,4SAAC,mIAAA,CAAA,WAAQ;4CAAC,WAAU;;8DAClB,4SAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;oDAAQ,WAAU;;sEACnC,4SAAC,yTAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGnC,4SAAC,mIAAA,CAAA,cAAW;oDAAC,OAAM;oDAAW,WAAU;;sEACtC,4SAAC,+TAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAO5C,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,4KAAA,CAAA,8BAA2B;;;;;kDAC5B,4SAAC,iIAAA,CAAA,iBAAc;wCAAC,YAAW;kDACzB,cAAA,4SAAC,qIAAA,CAAA,SAAM;4CAAC,SAAS;;8DACf,4SAAC,uTAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;gDACnB,CAAA,GAAA,6HAAA,CAAA,IAAC,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;oBAOV,gBAAgB,yBACf,4SAAC,+JAAA,CAAA,8BAA2B;wBAC1B,SAAS;wBACT,iBAAiB;;;;;;oBAKpB,gBAAgB,wBACf;kCAEE,cAAA,4SAAC,qJAAA,CAAA,YAAS;4BAAC,OAAO;sCAChB,cAAA,4SAAC,gKAAA,CAAA,mBAAgB;gCAAC,OAAO;;;;;;;;;;;wCAI7B,iBAAiB,iBACjB,4SAAC,gKAAA,CAAA,sBAAmB;wBAClB,kBAAkB;wBAClB,mBAAmB;wBACnB,qBAAqB;;;;;;oBAIxB,qBAAqB,MAAM,KAAK,KAAK,aAAa,MAAM,GAAG,mBAC1D,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAK3C,4SAAC,sKAAA,CAAA,wBAAqB;gBACpB,MAAM;gBACN,cAAc;gBACd,aAAa;gBACb,WAAW;;;;;;0BAGb,4SAAC,qJAAA,CAAA,qBAAkB;gBACjB,MAAM;gBACN,cAAc;gBACd,OAAM;gBACN,aAAa,CAAC,OAAO,EAAE,qBAAqB,QAAQ,SAAS,cAAc,CAAC;gBAC5E,aAAY;gBACZ,SAAQ;gBACR,WAAW;gBACX,SAAS;;;;;;;;AAIjB;GArRgB;;QACkB,iIAAA,CAAA,UAAO;QAgIrB,uIAAA,CAAA,eAAY;;;MAjIhB", "debugId": null}}]}