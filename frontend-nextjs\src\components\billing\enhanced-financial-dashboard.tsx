'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  IconTrendingUp, 
  IconTrendingDown, 
  IconCash, 
  IconCreditCard,
  IconRefresh,
  IconDownload,
  IconCalendar,
  IconUsers,
  IconReceipt,
  IconChartBar,
  IconPigMoney,
  IconWallet,
  IconTarget,
  IconAlertTriangle
} from '@tabler/icons-react';
import { toast } from 'sonner';
import { useRole, PermissionGate } from '@/lib/role-context';

interface AnalyticsData {
  period: {
    startDate: string;
    endDate: string;
    days: number;
  };
  revenue: {
    totalRevenue: number;
    averageTransactionValue: number;
    totalBilled: number;
    totalOutstanding: number;
    collectionRate: number;
  };
  paymentMethods: Record<string, { count: number; amount: number }>;
  billStatus: Record<string, { count: number; amount: number }>;
  treatments: Record<string, { count: number; amount: number }>;
  deposits: {
    totalDeposits: number;
    totalUsedDeposits: number;
    totalRemainingDeposits: number;
    utilizationRate: number;
  };
  patients: {
    newPatients: number;
    consultationUsers: number;
    conversionRate: number;
    averageRevenuePerPatient: number;
  };
  appointments: {
    totalAppointments: number;
    consultationAppointments: number;
    treatmentAppointments: number;
    completedAppointments: number;
    completionRate: number;
  };
  trends: {
    daily: Record<string, {
      revenue: number;
      bills: number;
      payments: number;
      appointments: number;
    }>;
    topMetrics: {
      highestRevenueDay: any;
      mostActiveDay: any;
    };
  };
  summary: {
    totalTransactions: number;
    totalBills: number;
    totalDeposits: number;
    totalAppointments: number;
    totalPatients: number;
  };
}

export function EnhancedFinancialDashboard() {
  const { hasPermission } = useRole();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0],
  });
  const [reportPeriod, setReportPeriod] = useState('30');

  // Check permissions
  if (!hasPermission('canViewDetailedFinancials')) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <IconAlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">权限不足</h3>
          <p className="text-muted-foreground">
            您没有权限查看详细的财务报表
          </p>
        </div>
      </div>
    );
  }

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/reports/analytics?startDate=${dateRange.startDate}&endDate=${dateRange.endDate}&period=${reportPeriod}`);
      if (!response.ok) {
        throw new Error('Failed to fetch analytics');
      }
      const data = await response.json();
      setAnalytics(data);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      toast.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange, reportPeriod]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'USD',
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const exportReport = async () => {
    try {
      const reportData = {
        analytics,
        dateRange,
        generatedAt: new Date().toISOString(),
      };
      
      const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `financial-analytics-${dateRange.startDate}-to-${dateRange.endDate}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast.success('报告导出成功');
    } catch (error) {
      console.error('Error exporting report:', error);
      toast.error('导出报告失败');
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold tracking-tight">高级财务分析</h2>
          <Button disabled>
            <IconRefresh className="mr-2 h-4 w-4 animate-spin" />
            加载中...
          </Button>
        </div>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">加载中...</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">--</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analytics) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">无法加载分析数据</p>
        <Button onClick={fetchAnalytics} className="mt-4">
          <IconRefresh className="mr-2 h-4 w-4" />
          重试
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">高级财务分析</h2>
          <p className="text-muted-foreground">
            {analytics.period.startDate} 至 {analytics.period.endDate} ({analytics.period.days} 天)
          </p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Label htmlFor="start-date">开始日期:</Label>
            <Input
              id="start-date"
              type="date"
              value={dateRange.startDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}
              className="w-auto"
            />
          </div>
          <div className="flex items-center gap-2">
            <Label htmlFor="end-date">结束日期:</Label>
            <Input
              id="end-date"
              type="date"
              value={dateRange.endDate}
              onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}
              className="w-auto"
            />
          </div>
          <Button onClick={fetchAnalytics} size="sm">
            <IconRefresh className="mr-2 h-4 w-4" />
            刷新
          </Button>
          <Button onClick={exportReport} size="sm" variant="outline">
            <IconDownload className="mr-2 h-4 w-4" />
            导出
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总收入</CardTitle>
            <IconCash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.revenue.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              平均交易: {formatCurrency(analytics.revenue.averageTransactionValue)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">回收率</CardTitle>
            <IconTarget className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(analytics.revenue.collectionRate)}</div>
            <p className="text-xs text-muted-foreground">
              未收: {formatCurrency(analytics.revenue.totalOutstanding)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">押金利用率</CardTitle>
            <IconPigMoney className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(analytics.deposits.utilizationRate)}</div>
            <p className="text-xs text-muted-foreground">
              剩余: {formatCurrency(analytics.deposits.totalRemainingDeposits)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">患者转化率</CardTitle>
            <IconUsers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatPercentage(analytics.patients.conversionRate)}</div>
            <p className="text-xs text-muted-foreground">
              新患者: {analytics.patients.newPatients}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
