'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  IconBell, 
  IconMail, 
  IconMessage, 
  IconSettings,
  IconPlus,
  IconTrash
} from '@tabler/icons-react';
import { 
  appointmentReminderService, 
  ReminderSettings 
} from '@/lib/appointment-reminders';
import { toast } from 'sonner';

interface AppointmentReminderSettingsProps {
  onSettingsChange?: (settings: ReminderSettings) => void;
}

export function AppointmentReminderSettings({
  onSettingsChange,
}: AppointmentReminderSettingsProps) {
  const [open, setOpen] = useState(false);
  const [settings, setSettings] = useState<ReminderSettings>(
    appointmentReminderService.getSettings()
  );
  const [newReminderTime, setNewReminderTime] = useState<string>('');
  const [notificationPermission, setNotificationPermission] = useState<boolean>(
    appointmentReminderService.isNotificationSupported()
  );

  useEffect(() => {
    // Check notification permission on mount
    setNotificationPermission(appointmentReminderService.isNotificationSupported());
  }, []);

  const handleSettingsChange = (newSettings: Partial<ReminderSettings>) => {
    const updatedSettings = { ...settings, ...newSettings };
    setSettings(updatedSettings);
    appointmentReminderService.updateSettings(updatedSettings);
    onSettingsChange?.(updatedSettings);
  };

  const handleAddReminderTime = () => {
    const minutes = parseInt(newReminderTime);
    if (isNaN(minutes) || minutes <= 0) {
      toast.error('请输入有效的提醒时间（分钟）');
      return;
    }

    if (settings.reminderTimes.includes(minutes)) {
      toast.error('该提醒时间已存在');
      return;
    }

    const newTimes = [...settings.reminderTimes, minutes].sort((a, b) => b - a);
    handleSettingsChange({ reminderTimes: newTimes });
    setNewReminderTime('');
    toast.success('提醒时间添加成功');
  };

  const handleRemoveReminderTime = (minutes: number) => {
    const newTimes = settings.reminderTimes.filter(time => time !== minutes);
    handleSettingsChange({ reminderTimes: newTimes });
    toast.success('提醒时间删除成功');
  };

  const handleMethodChange = (method: 'notification' | 'email' | 'sms', checked: boolean) => {
    let newMethods = [...settings.methods];
    
    if (checked) {
      if (!newMethods.includes(method)) {
        newMethods.push(method);
      }
    } else {
      newMethods = newMethods.filter(m => m !== method);
    }

    handleSettingsChange({ methods: newMethods });
  };

  const requestNotificationPermission = async () => {
    const granted = await appointmentReminderService.requestNotificationPermission();
    setNotificationPermission(granted);
    
    if (granted) {
      toast.success('通知权限已授予');
    } else {
      toast.error('通知权限被拒绝');
    }
  };

  const formatReminderTime = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes} 分钟前`;
    } else {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (remainingMinutes === 0) {
        return `${hours} 小时前`;
      } else {
        return `${hours} 小时 ${remainingMinutes} 分钟前`;
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <IconSettings className="h-4 w-4 mr-2" />
          提醒设置
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <IconBell className="h-5 w-5" />
            预约提醒设置
          </DialogTitle>
          <DialogDescription>
            配置预约提醒的时间和方式
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Enable/Disable Reminders */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="enable-reminders">启用预约提醒</Label>
              <p className="text-sm text-muted-foreground">
                自动发送预约提醒通知
              </p>
            </div>
            <Switch
              id="enable-reminders"
              checked={settings.enabled}
              onCheckedChange={(checked) => handleSettingsChange({ enabled: checked })}
            />
          </div>

          {settings.enabled && (
            <>
              {/* Reminder Times */}
              <div className="space-y-3">
                <Label>提醒时间</Label>
                <div className="flex flex-wrap gap-2">
                  {settings.reminderTimes.map((minutes) => (
                    <Badge
                      key={minutes}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {formatReminderTime(minutes)}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-destructive hover:text-destructive-foreground"
                        onClick={() => handleRemoveReminderTime(minutes)}
                      >
                        <IconTrash className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ))}
                </div>
                
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="分钟"
                    value={newReminderTime}
                    onChange={(e) => setNewReminderTime(e.target.value)}
                    className="flex-1"
                    min="1"
                    max="1440"
                  />
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleAddReminderTime}
                    disabled={!newReminderTime}
                  >
                    <IconPlus className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Reminder Methods */}
              <div className="space-y-3">
                <Label>提醒方式</Label>
                
                <div className="space-y-3">
                  {/* Browser Notification */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconBell className="h-4 w-4" />
                      <div>
                        <Label htmlFor="method-notification">浏览器通知</Label>
                        {!notificationPermission && (
                          <p className="text-xs text-muted-foreground">
                            需要授权通知权限
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {!notificationPermission && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={requestNotificationPermission}
                        >
                          授权
                        </Button>
                      )}
                      <Checkbox
                        id="method-notification"
                        checked={settings.methods.includes('notification')}
                        onCheckedChange={(checked) => 
                          handleMethodChange('notification', checked as boolean)
                        }
                        disabled={!notificationPermission}
                      />
                    </div>
                  </div>

                  {/* Email */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconMail className="h-4 w-4" />
                      <div>
                        <Label htmlFor="method-email">邮件提醒</Label>
                        <p className="text-xs text-muted-foreground">
                          即将推出
                        </p>
                      </div>
                    </div>
                    <Checkbox
                      id="method-email"
                      checked={settings.methods.includes('email')}
                      onCheckedChange={(checked) => 
                        handleMethodChange('email', checked as boolean)
                      }
                      disabled // Temporarily disabled
                    />
                  </div>

                  {/* SMS */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <IconMessage className="h-4 w-4" />
                      <div>
                        <Label htmlFor="method-sms">短信提醒</Label>
                        <p className="text-xs text-muted-foreground">
                          即将推出
                        </p>
                      </div>
                    </div>
                    <Checkbox
                      id="method-sms"
                      checked={settings.methods.includes('sms')}
                      onCheckedChange={(checked) => 
                        handleMethodChange('sms', checked as boolean)
                      }
                      disabled // Temporarily disabled
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button onClick={() => setOpen(false)}>
            完成
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
