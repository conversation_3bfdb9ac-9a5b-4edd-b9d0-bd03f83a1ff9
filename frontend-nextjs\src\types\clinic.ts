// TypeScript types for medical clinic data structures
// Based on Payload CMS backend collections

export interface Media {
  id: string;
  alt: string;
  url: string;
  filename: string;
  mimeType: string;
  filesize: number;
  width?: number;
  height?: number;
  createdAt: string;
  updatedAt: string;
}

export interface User {
  id: string;
  email: string;
  role: 'admin' | 'front-desk' | 'doctor';
  clerkId: string;
  firstName?: string;
  lastName?: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Treatment {
  id: string;
  name: string;
  description?: string;
  defaultPrice: number;
  defaultDurationInMinutes: number;
  createdAt: string;
  updatedAt: string;
}

export interface Patient {
  id: string;
  fullName: string;
  phone: string;
  email?: string;
  photo?: Media;
  medicalNotes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Appointment {
  id: string;
  appointmentType: 'consultation' | 'treatment';
  appointmentDate: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  treatment?: Treatment;
  price?: number;
  durationInMinutes: number;
  patient: Patient;
  practitioner: User;
  consultationType?: 'initial' | 'follow-up' | 'price-inquiry';
  interestedTreatments?: Treatment[];
  paymentStatus?: 'pending' | 'partial' | 'paid' | 'overdue';
  createdAt: string;
  updatedAt: string;
}

// API Response types for Payload CMS
export interface PayloadResponse<T> {
  docs: T[];
  totalDocs: number;
  limit: number;
  totalPages: number;
  page: number;
  pagingCounter: number;
  hasPrevPage: boolean;
  hasNextPage: boolean;
  prevPage?: number;
  nextPage?: number;
}

// API request types (for creating/updating)
export interface AppointmentCreateData {
  appointmentType: 'consultation' | 'treatment';
  appointmentDate: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  treatment?: string; // ID
  price?: number;
  durationInMinutes: number;
  patient: string; // ID
  practitioner: string; // ID
  consultationType?: 'initial' | 'follow-up' | 'price-inquiry';
  interestedTreatments?: string[]; // IDs
  paymentStatus?: 'pending' | 'partial' | 'paid' | 'overdue';
}

export interface AppointmentUpdateData extends Partial<AppointmentCreateData> {}

// Calendar event types for react-big-calendar
export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  resource: Appointment;
  status: 'scheduled' | 'confirmed' | 'completed' | 'cancelled' | 'no-show';
  appointmentType: 'consultation' | 'treatment';
}

// Time slot types for conflict detection
export interface TimeSlot {
  start: Date;
  end: Date;
}

export interface AppointmentConflict {
  hasConflict: boolean;
  conflictingAppointment?: Appointment;
  message?: string;
}

// Dashboard metrics types
export interface DashboardMetrics {
  todayAppointments: number;
  recentPatients: number;
  totalPatients: number;
  activetreatments: number;
}

// 支付和账单相关类型
export interface Bill {
  id: string;
  billNumber: string;
  patientId: string;
  appointmentId?: string;
  treatmentId?: string;
  billType: 'treatment' | 'consultation' | 'deposit' | 'additional';
  status: 'draft' | 'sent' | 'confirmed' | 'paid' | 'cancelled';
  subtotal: number;
  discountAmount: number;
  taxAmount: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  issueDate: string;
  dueDate: string;
  paidDate?: string;
  description: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
  patient?: Patient;
  appointment?: Appointment;
  treatment?: Treatment;
  items?: BillItem[];
  payments?: Payment[];
}

export interface BillItem {
  id: string;
  billId: string;
  itemType: 'treatment' | 'consultation' | 'material' | 'service';
  itemId?: string;
  itemName: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  discountRate: number;
  totalPrice: number;
  createdAt: string;
}

export interface Payment {
  id: string;
  paymentNumber: string;
  billId: string;
  patientId: string;
  amount: number;
  paymentMethod: 'cash' | 'card' | 'wechat' | 'alipay' | 'transfer' | 'installment';
  paymentStatus: 'pending' | 'completed' | 'failed' | 'refunded';
  transactionId?: string;
  paymentDate: string;
  receivedBy: string;
  notes?: string;
  receiptNumber?: string;
  createdAt: string;
  updatedAt: string;
  bill?: Bill;
  patient?: Patient;
}

export interface Deposit {
  id: string;
  depositNumber: string;
  patientId: string;
  appointmentId?: string;
  treatmentId?: string;
  depositType: 'treatment' | 'appointment' | 'material';
  amount: number;
  status: 'active' | 'used' | 'refunded' | 'expired';
  usedAmount: number;
  remainingAmount: number;
  depositDate: string;
  expiryDate?: string;
  usedDate?: string;
  refundDate?: string;
  purpose: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  patient?: Patient;
  appointment?: Appointment;
  treatment?: Treatment;
}
