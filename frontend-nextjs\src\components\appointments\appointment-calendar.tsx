'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Calendar, dateFnsLocalizer, View } from 'react-big-calendar'
import { format, parse, startOfWeek, getDay } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import 'react-big-calendar/lib/css/react-big-calendar.css'
import '@/styles/calendar.css'

import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  IconCalendar,
  IconList,
  IconChevronLeft,
  IconChevronRight,
  IconPlus,
  IconEdit,
  IconTrash,
  IconRefresh
} from '@tabler/icons-react'
import { cn } from '@/lib/utils'
import { Appointment, CalendarEvent } from '@/types/clinic'
import { appointmentsApi } from '@/lib/api'
import { toast } from 'sonner'
import { formatApiError } from '@/lib/error-utils'
import { appointmentNotifications } from '@/lib/appointment-notifications'
import {
  appointmentsToCalendarEvents,
  getStatusColor,
  getAppointmentTypeColor,
  calendarLabels,
  monthNames,
  dayNames,
  dayNamesShort
} from '@/lib/calendar-utils'

// Setup the localizer for react-big-calendar with Chinese locale
const locales = {
  'zh-CN': zhCN,
}

const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: () => startOfWeek(new Date(), { locale: zhCN }),
  getDay,
  locales,
})

interface AppointmentCalendarProps {
  onNewAppointment?: () => void
  onEditAppointment?: (appointment: Appointment) => void
  onDeleteAppointment?: (appointment: Appointment) => void
}

export function AppointmentCalendar({
  onNewAppointment,
  onEditAppointment,
  onDeleteAppointment,
}: AppointmentCalendarProps) {
  const [appointments, setAppointments] = useState<Appointment[]>([])
  const [loading, setLoading] = useState(true)
  const [view, setView] = useState<View>('month')
  const [date, setDate] = useState(new Date())

  // Convert appointments to calendar events
  const events = useMemo(() => {
    return appointmentsToCalendarEvents(appointments)
  }, [appointments])

  // Fetch appointments
  const fetchAppointments = useCallback(async () => {
    try {
      setLoading(true)
      const response = await appointmentsApi.getAll({ limit: 1000 })
      setAppointments(response.docs)
    } catch (error) {
      console.error('Failed to fetch appointments:', error)
      appointmentNotifications.error.fetchFailed()
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchAppointments()
  }, [fetchAppointments])

  // Handle event selection
  const handleSelectEvent = useCallback((event: CalendarEvent) => {
    onEditAppointment?.(event.resource)
  }, [onEditAppointment])

  // Handle slot selection (for creating new appointments)
  const handleSelectSlot = useCallback(({ start }: { start: Date }) => {
    // You can pass the selected date to the new appointment form
    onNewAppointment?.()
  }, [onNewAppointment])

  // Custom event style getter
  const eventStyleGetter = useCallback((event: CalendarEvent) => {
    const backgroundColor = getStatusColor(event.status)
    const borderColor = getAppointmentTypeColor(event.appointmentType)

    return {
      style: {
        backgroundColor,
        borderColor,
        borderWidth: '2px',
        borderStyle: 'solid',
        color: 'white',
        fontSize: '12px',
        padding: '2px 4px',
      }
    }
  }, [])

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <IconCalendar className="h-5 w-5" />
            预约日历
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={fetchAppointments}
              disabled={loading}
            >
              <IconRefresh className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button
              size="sm"
              onClick={onNewAppointment}
            >
              <IconPlus className="h-4 w-4 mr-2" />
              新建预约
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-4">
        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <IconCalendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground animate-pulse" />
              <p className="text-muted-foreground">加载预约数据中...</p>
            </div>
          </div>
        ) : (
          <div className="h-[600px]">
            <Calendar
              localizer={localizer}
              events={events}
              startAccessor="start"
              endAccessor="end"
              view={view}
              onView={setView}
              date={date}
              onNavigate={setDate}
              onSelectEvent={handleSelectEvent}
              onSelectSlot={handleSelectSlot}
              selectable
              eventPropGetter={eventStyleGetter}
              messages={calendarLabels}
              culture="zh-CN"
              formats={{
                monthHeaderFormat: 'yyyy年MM月',
                dayHeaderFormat: 'MM月dd日',
                dayRangeHeaderFormat: ({ start, end }) =>
                  `${format(start, 'MM月dd日', { locale: zhCN })} - ${format(end, 'MM月dd日', { locale: zhCN })}`,
                agendaDateFormat: 'MM月dd日',
                agendaTimeFormat: 'HH:mm',
                agendaTimeRangeFormat: ({ start, end }) =>
                  `${format(start, 'HH:mm', { locale: zhCN })} - ${format(end, 'HH:mm', { locale: zhCN })}`,
              }}
              components={{
                month: {
                  dateHeader: ({ date, label }) => (
                    <span className="text-sm font-medium">{label}</span>
                  ),
                },
                week: {
                  header: ({ date, label }) => (
                    <span className="text-sm font-medium">{label}</span>
                  ),
                },
                event: ({ event }) => (
                  <div className="text-xs">
                    <div className="font-medium truncate">{event.title}</div>
                    <div className="opacity-75">
                      {event.appointmentType === 'consultation' ? '咨询' : '治疗'}
                    </div>
                  </div>
                ),
              }}
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}
