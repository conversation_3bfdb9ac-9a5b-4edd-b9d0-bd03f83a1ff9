// Calendar utilities for appointment scheduling and data conversion
// Handles conversion between Appointment data and Calendar events

import { format, addMinutes, parseISO } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { Appointment, CalendarEvent, TimeSlot, AppointmentConflict } from '@/types/clinic';

/**
 * Convert appointments to calendar events for react-big-calendar
 */
export function appointmentsToCalendarEvents(appointments: Appointment[]): CalendarEvent[] {
  return appointments.map((appointment) => {
    const startDate = parseISO(appointment.appointmentDate);
    const endDate = addMinutes(startDate, appointment.durationInMinutes);
    
    // Generate title based on appointment type
    const title = generateEventTitle(appointment);
    
    return {
      id: appointment.id,
      title,
      start: startDate,
      end: endDate,
      resource: appointment,
      status: appointment.status,
      appointmentType: appointment.appointmentType,
    };
  });
}

/**
 * Generate event title for calendar display
 */
export function generateEventTitle(appointment: Appointment): string {
  const patientName = appointment.patient.fullName;
  const typeLabel = appointment.appointmentType === 'consultation' ? '咨询' : '治疗';
  
  if (appointment.appointmentType === 'treatment' && appointment.treatment) {
    return `${patientName} - ${appointment.treatment.name}`;
  } else if (appointment.appointmentType === 'consultation') {
    const consultationTypeMap = {
      'initial': '初次咨询',
      'follow-up': '复诊咨询',
      'price-inquiry': '价格咨询'
    };
    const consultationType = appointment.consultationType 
      ? consultationTypeMap[appointment.consultationType] 
      : '咨询';
    return `${patientName} - ${consultationType}`;
  }
  
  return `${patientName} - ${typeLabel}`;
}

/**
 * Get status color for calendar events
 */
export function getStatusColor(status: string): string {
  const statusColors = {
    'scheduled': '#3b82f6', // blue
    'confirmed': '#10b981', // green
    'completed': '#6b7280', // gray
    'cancelled': '#ef4444', // red
    'no-show': '#f59e0b', // amber
  };
  
  return statusColors[status as keyof typeof statusColors] || '#6b7280';
}

/**
 * Get appointment type color
 */
export function getAppointmentTypeColor(type: string): string {
  const typeColors = {
    'consultation': '#8b5cf6', // purple
    'treatment': '#06b6d4', // cyan
  };
  
  return typeColors[type as keyof typeof typeColors] || '#6b7280';
}

/**
 * Create time slot from date and duration
 */
export function createTimeSlot(appointmentDate: Date, durationInMinutes: number): TimeSlot {
  const start = new Date(appointmentDate);
  const end = addMinutes(start, durationInMinutes);
  return { start, end };
}

/**
 * Check if two time slots overlap
 */
export function doTimeSlotsOverlap(slot1: TimeSlot, slot2: TimeSlot): boolean {
  return slot1.start < slot2.end && slot2.start < slot1.end;
}

/**
 * Check for appointment conflicts
 */
export function checkAppointmentConflicts(
  newAppointment: {
    appointmentDate: Date;
    durationInMinutes: number;
    practitionerId: string;
    id?: string; // For editing existing appointments
  },
  existingAppointments: Appointment[]
): AppointmentConflict {
  const newSlot = createTimeSlot(newAppointment.appointmentDate, newAppointment.durationInMinutes);
  
  // Filter appointments for the same practitioner, excluding cancelled/no-show
  const samePractitionerAppointments = existingAppointments.filter(apt => 
    apt.practitioner.id === newAppointment.practitionerId &&
    apt.status !== 'cancelled' &&
    apt.status !== 'no-show' &&
    apt.id !== newAppointment.id // Exclude current appointment when editing
  );
  
  // Check for overlaps
  for (const existingApt of samePractitionerAppointments) {
    const existingSlot = createTimeSlot(
      parseISO(existingApt.appointmentDate),
      existingApt.durationInMinutes
    );
    
    if (doTimeSlotsOverlap(newSlot, existingSlot)) {
      return {
        hasConflict: true,
        conflictingAppointment: existingApt,
        message: `此时间段与现有预约冲突：${existingApt.patient.fullName} (${format(existingSlot.start, 'HH:mm', { locale: zhCN })} - ${format(existingSlot.end, 'HH:mm', { locale: zhCN })})`
      };
    }
  }
  
  return { hasConflict: false };
}

/**
 * Suggest alternative time slots when conflict occurs
 */
export function suggestAlternativeSlots(
  preferredDate: Date,
  practitionerId: string,
  durationInMinutes: number,
  existingAppointments: Appointment[],
  maxSuggestions: number = 3
): Date[] {
  const suggestions: Date[] = [];
  const baseDate = new Date(preferredDate);
  
  // Try slots every 30 minutes for the next 4 hours
  for (let i = 1; i <= 8 && suggestions.length < maxSuggestions; i++) {
    const candidateDate = addMinutes(baseDate, i * 30);
    
    const conflict = checkAppointmentConflicts(
      {
        appointmentDate: candidateDate,
        durationInMinutes,
        practitionerId,
      },
      existingAppointments
    );
    
    if (!conflict.hasConflict) {
      suggestions.push(candidateDate);
    }
  }
  
  return suggestions;
}

/**
 * Format time for display
 */
export function formatTime(date: Date): string {
  return format(date, 'HH:mm', { locale: zhCN });
}

/**
 * Format date for display
 */
export function formatDate(date: Date): string {
  return format(date, 'yyyy年MM月dd日', { locale: zhCN });
}

/**
 * Format date and time for display
 */
export function formatDateTime(date: Date): string {
  return format(date, 'yyyy年MM月dd日 HH:mm', { locale: zhCN });
}

/**
 * Get calendar view labels in Chinese
 */
export const calendarLabels = {
  month: '月视图',
  week: '周视图',
  work_week: '工作周',
  day: '日视图',
  agenda: '议程',
  today: '今天',
  previous: '上一个',
  next: '下一个',
  showMore: (total: number) => `+${total} 更多`,
};

/**
 * Get month names in Chinese
 */
export const monthNames = [
  '一月', '二月', '三月', '四月', '五月', '六月',
  '七月', '八月', '九月', '十月', '十一月', '十二月'
];

/**
 * Get day names in Chinese
 */
export const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

/**
 * Get short day names in Chinese
 */
export const dayNamesShort = ['日', '一', '二', '三', '四', '五', '六'];
