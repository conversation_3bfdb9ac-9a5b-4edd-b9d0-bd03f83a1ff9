import type { CollectionConfig, Access } from 'payload'

export const Bills: CollectionConfig = {
  slug: 'bills',
  admin: {
    useAsTitle: 'billNumber',
    defaultColumns: ['billNumber', 'patient', 'billType', 'status', 'totalAmount', 'remainingAmount'],
    listSearchableFields: ['billNumber', 'patient.fullName', 'description'],
  },
  access: {
    // Read: Admin and Front-desk see all bills, Doctors see only bills related to their appointments
    read: (({ req: { user } }) => {
      if (!user) return false;
      if (user.role === 'admin' || user.role === 'front-desk') {
        return true; // Can see all bills
      }
      if (user.role === 'doctor') {
        return {
          or: [
            {
              'appointment.practitioner': {
                equals: user.id,
              },
            },
            {
              createdBy: {
                equals: user.id,
              },
            },
          ],
        };
      }
      return false;
    }) as Access,

    // Create: Admin and Front-desk can create bills
    create: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Update: Admin and Front-desk can update bills
    update: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin' || user.role === 'front-desk';
    },

    // Delete: Only Admin can delete bills
    delete: ({ req: { user } }) => {
      if (!user) return false;
      return user.role === 'admin';
    },
  },
  hooks: {
    beforeChange: [
      ({ data }) => {
        // Auto-generate bill number if not provided
        if (!data.billNumber) {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.billNumber = `BILL-${year}${month}${day}-${timestamp}`;
        }

        // Validate amounts are not negative
        if (data.subtotal !== undefined && data.subtotal < 0) {
          throw new Error('Subtotal cannot be negative');
        }
        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          throw new Error('Total amount cannot be negative');
        }
        if (data.discountAmount !== undefined && data.discountAmount < 0) {
          throw new Error('Discount amount cannot be negative');
        }
        if (data.taxAmount !== undefined && data.taxAmount < 0) {
          throw new Error('Tax amount cannot be negative');
        }
        if (data.paidAmount !== undefined && data.paidAmount < 0) {
          throw new Error('Paid amount cannot be negative');
        }

        // Calculate remaining amount
        const totalAmount = data.totalAmount || 0;
        const paidAmount = data.paidAmount || 0;
        data.remainingAmount = totalAmount - paidAmount;

        // Validate remaining amount is not negative
        if (data.remainingAmount < 0) {
          throw new Error('Paid amount cannot exceed total amount');
        }

        return data;
      },
    ],
  },
  fields: [
    {
      name: 'billNumber',
      type: 'text',
      required: true,
      unique: true,
      label: '账单编号',
      admin: {
        description: '系统自动生成，格式：BILL-YYYYMMDD-XXXXXX',
        readOnly: true,
      },
    },
    
    // 关联信息
    {
      name: 'patient',
      type: 'relationship',
      relationTo: 'patients',
      required: true,
      hasMany: false,
      label: '患者',
    },
    {
      name: 'appointment',
      type: 'relationship',
      relationTo: 'appointments',
      hasMany: false,
      label: '关联预约',
      admin: {
        description: '如果账单来源于预约，请选择对应预约',
      },
    },
    {
      name: 'treatment',
      type: 'relationship',
      relationTo: 'treatments',
      hasMany: false,
      label: '关联治疗',
      admin: {
        description: '如果是治疗账单，请选择对应治疗项目',
      },
    },
    
    // 账单基本信息
    {
      name: 'billType',
      type: 'select',
      required: true,
      options: [
        {
          label: '治疗账单',
          value: 'treatment',
        },
        {
          label: '咨询账单',
          value: 'consultation',
        },
        {
          label: '押金账单',
          value: 'deposit',
        },
        {
          label: '补充账单',
          value: 'additional',
        },
      ],
      defaultValue: 'treatment',
      label: '账单类型',
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        {
          label: '草稿',
          value: 'draft',
        },
        {
          label: '已发送',
          value: 'sent',
        },
        {
          label: '已确认',
          value: 'confirmed',
        },
        {
          label: '已支付',
          value: 'paid',
        },
        {
          label: '已取消',
          value: 'cancelled',
        },
      ],
      defaultValue: 'draft',
      label: '账单状态',
    },
    
    // 金额信息
    {
      name: 'subtotal',
      type: 'number',
      required: true,
      label: '小计金额',
      min: 0,
      admin: {
        description: '税前金额',
      },
    },
    {
      name: 'discountAmount',
      type: 'number',
      defaultValue: 0,
      label: '折扣金额',
      min: 0,
      admin: {
        description: '折扣金额',
      },
    },
    {
      name: 'taxAmount',
      type: 'number',
      defaultValue: 0,
      label: '税费',
      min: 0,
      admin: {
        description: '税费金额',
      },
    },
    {
      name: 'totalAmount',
      type: 'number',
      required: true,
      label: '总金额',
      min: 0,
      admin: {
        description: '最终应付金额 = 小计 + 税费 - 折扣',
      },
    },
    {
      name: 'paidAmount',
      type: 'number',
      defaultValue: 0,
      label: '已支付金额',
      min: 0,
      admin: {
        description: '已支付的金额',
      },
    },
    {
      name: 'remainingAmount',
      type: 'number',
      label: '剩余金额',
      admin: {
        description: '剩余未支付金额（自动计算）',
        readOnly: true,
      },
    },
    
    // 时间信息
    {
      name: 'issueDate',
      type: 'date',
      required: true,
      label: '开票日期',
      defaultValue: () => new Date().toISOString(),
      admin: {
        date: {
          pickerAppearance: 'dayOnly',
        },
      },
    },
    {
      name: 'dueDate',
      type: 'date',
      required: true,
      label: '到期日期',
      admin: {
        date: {
          pickerAppearance: 'dayOnly',
        },
        description: '账单到期日期',
      },
    },
    {
      name: 'paidDate',
      type: 'date',
      label: '支付完成日期',
      admin: {
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: '账单完全支付的日期',
        condition: (data) => data.status === 'paid',
      },
    },
    
    // 详细信息
    {
      name: 'description',
      type: 'text',
      required: true,
      label: '账单描述',
      admin: {
        description: '账单的简要描述',
      },
    },
    {
      name: 'notes',
      type: 'textarea',
      label: '备注',
      admin: {
        description: '账单相关的备注信息',
      },
    },
    
    // 创建人员
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      hasMany: false,
      label: '创建人员',
      admin: {
        description: '创建此账单的工作人员',
      },
    },
  ],
  hooks: {
    beforeChange: [
      async ({ data, req, operation }) => {
        // Auto-generate bill number if not provided
        if (!data.billNumber) {
          const now = new Date();
          const year = now.getFullYear();
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const day = String(now.getDate()).padStart(2, '0');
          const timestamp = now.getTime().toString().slice(-6);
          data.billNumber = `BILL-${year}${month}${day}-${timestamp}`;
        }

        // Validate amounts
        if (data.subtotal !== undefined && data.subtotal < 0) {
          throw new Error('小计金额不能为负数');
        }
        if (data.discountAmount !== undefined && data.discountAmount < 0) {
          throw new Error('折扣金额不能为负数');
        }
        if (data.taxAmount !== undefined && data.taxAmount < 0) {
          throw new Error('税费不能为负数');
        }
        if (data.totalAmount !== undefined && data.totalAmount < 0) {
          throw new Error('总金额不能为负数');
        }
        if (data.paidAmount !== undefined && data.paidAmount < 0) {
          throw new Error('已付金额不能为负数');
        }

        // Auto-calculate total amount if components are provided
        if (data.subtotal !== undefined) {
          const subtotal = data.subtotal || 0;
          const discountAmount = data.discountAmount || 0;
          const taxAmount = data.taxAmount || 0;
          data.totalAmount = subtotal - discountAmount + taxAmount;
        }

        // Auto-calculate remaining amount
        if (data.totalAmount !== undefined) {
          const totalAmount = data.totalAmount;
          const paidAmount = data.paidAmount || 0;
          data.remainingAmount = totalAmount - paidAmount;
        }

        // Validate paid amount doesn't exceed total amount
        if (data.paidAmount !== undefined && data.totalAmount !== undefined) {
          if (data.paidAmount > data.totalAmount) {
            throw new Error('已付金额不能超过总金额');
          }
        }

        // Set created by if not provided (for new bills)
        if (operation === 'create' && !data.createdBy && req.user) {
          data.createdBy = req.user.id;
        }

        // Auto-set paid date when status changes to paid
        if (data.status === 'paid' && !data.paidDate) {
          data.paidDate = new Date().toISOString();
        }

        // Clear paid date if status is not paid
        if (data.status !== 'paid' && data.paidDate) {
          data.paidDate = null;
        }

        return data;
      },
    ],
    afterChange: [
      async ({ doc, req, operation, previousDoc }) => {
        // Skip hook during testing to avoid timeouts
        if (process.env.NODE_ENV === 'test') {
          return;
        }

        // Update bill items if this is a new bill or bill amount changed
        if (operation === 'create' || (previousDoc && doc.totalAmount !== previousDoc.totalAmount)) {
          try {
            // Add timeout to prevent hanging
            const timeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error('Hook timeout')), 5000);
            });

            const updatePromise = (async () => {
              // If bill has associated appointment, update appointment payment status
              if (doc.appointment) {
                const appointmentId = typeof doc.appointment === 'object' ? doc.appointment.id : doc.appointment;

                let paymentStatus = 'pending';
                if (doc.status === 'paid') {
                  paymentStatus = 'paid';
                } else if (doc.paidAmount > 0) {
                  paymentStatus = 'partial';
                }

                await req.payload.update({
                  collection: 'appointments',
                  id: appointmentId,
                  data: {
                    paymentStatus: paymentStatus,
                  }
                });
              }

              // Log financial operation for audit trail
              console.log(`Bill ${doc.billNumber} ${operation}d - Total: $${doc.totalAmount}, Remaining: $${doc.remainingAmount}, Status: ${doc.status}`);
            })();

            await Promise.race([updatePromise, timeoutPromise]);
          } catch (error) {
            console.error('Error in Bills afterChange hook:', error);
            // Don't throw error to prevent blocking the main operation
          }
        }
      },
    ],
  },
}
