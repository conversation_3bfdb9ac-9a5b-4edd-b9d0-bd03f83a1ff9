import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * POST /api/payments/process-payment - Process complex payment scenarios
 * Handles multiple payment methods, partial payments, deposit applications, etc.
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - admin and front-desk can process payments
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to process payments' },
        { status: 403 }
      );
    }

    // Parse request body
    const {
      billId,
      paymentMethods, // Array of payment methods with amounts
      applyDeposits, // Array of deposits to apply
      notes,
      generateReceipt = true
    } = await request.json();

    if (!billId) {
      return NextResponse.json(
        { error: 'Bill ID is required' },
        { status: 400 }
      );
    }

    if (!paymentMethods || !Array.isArray(paymentMethods) || paymentMethods.length === 0) {
      return NextResponse.json(
        { error: 'At least one payment method is required' },
        { status: 400 }
      );
    }

    // Validate payment methods
    const validPaymentMethods = ['cash', 'card', 'wechat', 'alipay', 'bank-transfer', 'installment', 'deposit'];
    for (const method of paymentMethods) {
      if (!method.method || !validPaymentMethods.includes(method.method)) {
        return NextResponse.json(
          { error: `Invalid payment method: ${method.method}` },
          { status: 400 }
        );
      }
      if (!method.amount || method.amount <= 0) {
        return NextResponse.json(
          { error: 'Payment amount must be positive' },
          { status: 400 }
        );
      }
    }

    // Get bill details
    const bill = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'findByID',
      {
        id: billId,
        depth: 2,
      }
    );

    if (!bill) {
      return NextResponse.json(
        { error: 'Bill not found' },
        { status: 404 }
      );
    }

    // Calculate total payment amount
    const totalPaymentAmount = paymentMethods.reduce((sum, method) => sum + method.amount, 0);
    let totalDepositAmount = 0;

    // Validate and process deposit applications
    if (applyDeposits && Array.isArray(applyDeposits)) {
      for (const depositApplication of applyDeposits) {
        const deposit = await makeAuthenticatedPayloadRequest(
          authContext,
          'deposits',
          'findByID',
          {
            id: depositApplication.depositId,
          }
        );

        if (!deposit) {
          return NextResponse.json(
            { error: `Deposit not found: ${depositApplication.depositId}` },
            { status: 404 }
          );
        }

        if (deposit.status !== 'active') {
          return NextResponse.json(
            { error: `Deposit ${deposit.depositNumber} is not active` },
            { status: 400 }
          );
        }

        if (depositApplication.amount > deposit.remainingAmount) {
          return NextResponse.json(
            { error: `Deposit application amount exceeds remaining balance` },
            { status: 400 }
          );
        }

        totalDepositAmount += depositApplication.amount;
      }
    }

    const totalAmount = totalPaymentAmount + totalDepositAmount;

    // Validate total amount doesn't exceed bill balance
    if (totalAmount > bill.remainingAmount) {
      return NextResponse.json(
        { error: `Total payment amount (${totalAmount}) exceeds bill balance (${bill.remainingAmount})` },
        { status: 400 }
      );
    }

    const results = {
      payments: [],
      depositApplications: [],
      updatedBill: null,
      receipts: []
    };

    // Process each payment method
    for (const method of paymentMethods) {
      const paymentData = {
        bill: billId,
        patient: bill.patient.id || bill.patient,
        amount: method.amount,
        paymentMethod: method.method,
        paymentStatus: 'completed',
        transactionId: method.transactionId || `TXN-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        notes: method.notes || notes,
        receivedBy: authContext.user.id,
        paymentDate: new Date().toISOString(),
      };

      const payment = await makeAuthenticatedPayloadRequest(
        authContext,
        'payments',
        'create',
        { data: paymentData }
      );

      results.payments.push(payment);

      // Generate receipt if requested
      if (generateReceipt) {
        try {
          const receiptResponse = await fetch(`${request.nextUrl.origin}/api/payments/${payment.id}/receipt`, {
            headers: {
              'Authorization': request.headers.get('Authorization') || '',
              'Content-Type': 'application/json',
            },
          });
          
          if (receiptResponse.ok) {
            const receipt = await receiptResponse.json();
            results.receipts.push(receipt);
          }
        } catch (error) {
          console.error('Error generating receipt:', error);
        }
      }
    }

    // Process deposit applications
    if (applyDeposits && Array.isArray(applyDeposits)) {
      for (const depositApplication of applyDeposits) {
        try {
          const applyResponse = await fetch(`${request.nextUrl.origin}/api/deposits/apply-to-bill`, {
            method: 'POST',
            headers: {
              'Authorization': request.headers.get('Authorization') || '',
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              depositId: depositApplication.depositId,
              billId: billId,
              amount: depositApplication.amount,
            }),
          });

          if (applyResponse.ok) {
            const result = await applyResponse.json();
            results.depositApplications.push(result);
          }
        } catch (error) {
          console.error('Error applying deposit:', error);
        }
      }
    }

    // Get updated bill information
    const updatedBill = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'findByID',
      {
        id: billId,
        depth: 2,
      }
    );

    results.updatedBill = updatedBill;

    return NextResponse.json({
      success: true,
      message: `Payment processed successfully. Total amount: $${totalAmount}`,
      data: results,
    });

  } catch (error) {
    console.error('Error processing payment:', error);
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    );
  }
}
