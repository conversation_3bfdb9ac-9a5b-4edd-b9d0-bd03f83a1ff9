// Unit tests for appointment system functionality
// Tests calendar utils, conflict detection, and reminder system

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  appointmentsToCalendarEvents,
  generateEventTitle,
  getStatusColor,
  getAppointmentTypeColor,
  createTimeSlot,
  doTimeSlotsOverlap,
  checkAppointmentConflicts,
  suggestAlternativeSlots,
  formatTime,
  formatDate,
  formatDateTime
} from '@/lib/calendar-utils';
import { 
  appointmentReminderService,
  scheduleAppointmentReminders,
  clearAppointmentReminders
} from '@/lib/appointment-reminders';
import { Appointment, CalendarEvent } from '@/types/clinic';

// Mock appointment data
const mockAppointment: Appointment = {
  id: '1',
  appointmentType: 'treatment',
  appointmentDate: '2024-01-15T10:00:00Z',
  status: 'scheduled',
  treatment: {
    id: '1',
    name: '牙齿清洁',
    description: '专业牙齿清洁服务',
    defaultPrice: 200,
    defaultDurationInMinutes: 60,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  price: 200,
  durationInMinutes: 60,
  patient: {
    id: '1',
    fullName: '张三',
    phone: '13800138000',
    email: '<EMAIL>',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  practitioner: {
    id: '1',
    email: '<EMAIL>',
    role: 'doctor' as const,
    clerkId: 'clerk_123',
    firstName: '李',
    lastName: '医生',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
  },
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
};

const mockConsultationAppointment: Appointment = {
  ...mockAppointment,
  id: '2',
  appointmentType: 'consultation',
  consultationType: 'initial',
  treatment: undefined,
  price: 0,
  durationInMinutes: 30,
};

describe('Calendar Utils', () => {
  describe('appointmentsToCalendarEvents', () => {
    it('should convert appointments to calendar events', () => {
      const appointments = [mockAppointment, mockConsultationAppointment];
      const events = appointmentsToCalendarEvents(appointments);

      expect(events).toHaveLength(2);
      expect(events[0]).toMatchObject({
        id: '1',
        title: '张三 - 牙齿清洁',
        status: 'scheduled',
        appointmentType: 'treatment',
      });
      expect(events[1]).toMatchObject({
        id: '2',
        title: '张三 - 初次咨询',
        status: 'scheduled',
        appointmentType: 'consultation',
      });
    });
  });

  describe('generateEventTitle', () => {
    it('should generate correct title for treatment appointment', () => {
      const title = generateEventTitle(mockAppointment);
      expect(title).toBe('张三 - 牙齿清洁');
    });

    it('should generate correct title for consultation appointment', () => {
      const title = generateEventTitle(mockConsultationAppointment);
      expect(title).toBe('张三 - 初次咨询');
    });
  });

  describe('getStatusColor', () => {
    it('should return correct colors for different statuses', () => {
      expect(getStatusColor('scheduled')).toBe('#3b82f6');
      expect(getStatusColor('confirmed')).toBe('#10b981');
      expect(getStatusColor('completed')).toBe('#6b7280');
      expect(getStatusColor('cancelled')).toBe('#ef4444');
      expect(getStatusColor('no-show')).toBe('#f59e0b');
    });
  });

  describe('getAppointmentTypeColor', () => {
    it('should return correct colors for appointment types', () => {
      expect(getAppointmentTypeColor('consultation')).toBe('#8b5cf6');
      expect(getAppointmentTypeColor('treatment')).toBe('#06b6d4');
    });
  });
});

describe('Time Slot Management', () => {
  describe('createTimeSlot', () => {
    it('should create correct time slot', () => {
      const date = new Date('2024-01-15T10:00:00Z');
      const slot = createTimeSlot(date, 60);
      
      expect(slot.start).toEqual(date);
      expect(slot.end).toEqual(new Date('2024-01-15T11:00:00Z'));
    });
  });

  describe('doTimeSlotsOverlap', () => {
    it('should detect overlapping slots', () => {
      const slot1 = {
        start: new Date('2024-01-15T10:00:00Z'),
        end: new Date('2024-01-15T11:00:00Z'),
      };
      const slot2 = {
        start: new Date('2024-01-15T10:30:00Z'),
        end: new Date('2024-01-15T11:30:00Z'),
      };
      
      expect(doTimeSlotsOverlap(slot1, slot2)).toBe(true);
    });

    it('should not detect non-overlapping slots', () => {
      const slot1 = {
        start: new Date('2024-01-15T10:00:00Z'),
        end: new Date('2024-01-15T11:00:00Z'),
      };
      const slot2 = {
        start: new Date('2024-01-15T11:00:00Z'),
        end: new Date('2024-01-15T12:00:00Z'),
      };
      
      expect(doTimeSlotsOverlap(slot1, slot2)).toBe(false);
    });
  });
});

describe('Conflict Detection', () => {
  describe('checkAppointmentConflicts', () => {
    it('should detect conflicts with existing appointments', () => {
      const newAppointment = {
        appointmentDate: new Date('2024-01-15T10:30:00Z'),
        durationInMinutes: 60,
        practitionerId: '1',
      };
      
      const existingAppointments = [mockAppointment];
      const result = checkAppointmentConflicts(newAppointment, existingAppointments);
      
      expect(result.hasConflict).toBe(true);
      expect(result.conflictingAppointment).toBeDefined();
      expect(result.message).toContain('冲突');
    });

    it('should not detect conflicts with different practitioners', () => {
      const newAppointment = {
        appointmentDate: new Date('2024-01-15T10:30:00Z'),
        durationInMinutes: 60,
        practitionerId: '2', // Different practitioner
      };
      
      const existingAppointments = [mockAppointment];
      const result = checkAppointmentConflicts(newAppointment, existingAppointments);
      
      expect(result.hasConflict).toBe(false);
    });

    it('should ignore cancelled appointments in conflict detection', () => {
      const cancelledAppointment = {
        ...mockAppointment,
        status: 'cancelled' as const,
      };
      
      const newAppointment = {
        appointmentDate: new Date('2024-01-15T10:30:00Z'),
        durationInMinutes: 60,
        practitionerId: '1',
      };
      
      const result = checkAppointmentConflicts(newAppointment, [cancelledAppointment]);
      
      expect(result.hasConflict).toBe(false);
    });
  });

  describe('suggestAlternativeSlots', () => {
    it('should suggest alternative time slots', () => {
      const preferredDate = new Date('2024-01-15T10:00:00Z');
      const suggestions = suggestAlternativeSlots(
        preferredDate,
        '1',
        60,
        [mockAppointment],
        3
      );
      
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions.length).toBeLessThanOrEqual(3);
    });
  });
});

describe('Date Formatting', () => {
  const testDate = new Date('2024-01-15T10:30:00Z');

  describe('formatTime', () => {
    it('should format time correctly', () => {
      const formatted = formatTime(testDate);
      expect(formatted).toMatch(/\d{2}:\d{2}/);
    });
  });

  describe('formatDate', () => {
    it('should format date correctly', () => {
      const formatted = formatDate(testDate);
      expect(formatted).toContain('2024年');
      expect(formatted).toContain('01月');
      expect(formatted).toContain('15日');
    });
  });

  describe('formatDateTime', () => {
    it('should format date and time correctly', () => {
      const formatted = formatDateTime(testDate);
      expect(formatted).toContain('2024年');
      expect(formatted).toMatch(/\d{2}:\d{2}/);
    });
  });
});

describe('Appointment Reminder Service', () => {
  beforeEach(() => {
    // Clear any existing reminders
    appointmentReminderService.clearReminders(mockAppointment.id);
  });

  describe('scheduleReminders', () => {
    it('should schedule reminders for an appointment', () => {
      const futureAppointment = {
        ...mockAppointment,
        appointmentDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
      };

      scheduleAppointmentReminders(futureAppointment);
      
      const reminders = appointmentReminderService.getReminders(futureAppointment.id);
      expect(reminders.length).toBeGreaterThan(0);
    });
  });

  describe('clearReminders', () => {
    it('should clear all reminders for an appointment', () => {
      const futureAppointment = {
        ...mockAppointment,
        appointmentDate: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
      };

      scheduleAppointmentReminders(futureAppointment);
      clearAppointmentReminders(futureAppointment.id);
      
      const reminders = appointmentReminderService.getReminders(futureAppointment.id);
      expect(reminders.length).toBe(0);
    });
  });

  describe('settings management', () => {
    it('should update and retrieve settings', () => {
      const newSettings = {
        enabled: false,
        reminderTimes: [30, 60],
        methods: ['notification' as const],
      };

      appointmentReminderService.updateSettings(newSettings);
      const retrievedSettings = appointmentReminderService.getSettings();
      
      expect(retrievedSettings.enabled).toBe(false);
      expect(retrievedSettings.reminderTimes).toEqual([30, 60]);
      expect(retrievedSettings.methods).toEqual(['notification']);
    });
  });
});
