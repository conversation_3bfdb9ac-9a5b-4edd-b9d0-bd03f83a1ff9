// Performance tests for billing system
// Tests caching, virtual scrolling, debouncing, and optimization features

import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import { renderHook, act } from '@testing-library/react';
import { BillingCache, PerformanceMonitor, useDebounce, useVirtualScrolling } from '../../lib/billing-performance';

describe('Billing Performance Tests', () => {
  describe('BillingCache', () => {
    let cache: BillingCache;

    beforeEach(() => {
      cache = BillingCache.getInstance();
      cache.clear();
    });

    it('should cache and retrieve data correctly', () => {
      const testData = { id: 'test-1', name: 'Test Data' };
      const cacheKey = 'test-key';

      cache.set(cacheKey, testData);
      const retrievedData = cache.get(cacheKey);

      expect(retrievedData).toEqual(testData);
    });

    it('should return null for non-existent keys', () => {
      const result = cache.get('non-existent-key');
      expect(result).toBeNull();
    });

    it('should respect TTL and expire data', async () => {
      const testData = { id: 'test-1', name: 'Test Data' };
      const cacheKey = 'test-key';
      const shortTTL = 100; // 100ms

      cache.set(cacheKey, testData, shortTTL);
      
      // Should be available immediately
      expect(cache.get(cacheKey)).toEqual(testData);

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should be expired
      expect(cache.get(cacheKey)).toBeNull();
    });

    it('should handle cache size limits', () => {
      // Fill cache beyond limit
      for (let i = 0; i < 1100; i++) {
        cache.set(`key-${i}`, { data: i });
      }

      const stats = cache.getStats();
      expect(stats.size).toBeLessThanOrEqual(1000); // Should not exceed max size
    });

    it('should invalidate patterns correctly', () => {
      cache.set('bills:patient-1', { data: 'bill1' });
      cache.set('bills:patient-2', { data: 'bill2' });
      cache.set('payments:patient-1', { data: 'payment1' });

      cache.invalidatePattern('bills:');

      expect(cache.get('bills:patient-1')).toBeNull();
      expect(cache.get('bills:patient-2')).toBeNull();
      expect(cache.get('payments:patient-1')).not.toBeNull();
    });

    it('should provide accurate cache statistics', () => {
      cache.set('key1', 'data1');
      cache.set('key2', 'data2');
      cache.set('key3', 'data3');

      const stats = cache.getStats();
      expect(stats.size).toBe(3);
      expect(typeof stats.hitRate).toBe('number');
    });
  });

  describe('PerformanceMonitor', () => {
    beforeEach(() => {
      PerformanceMonitor.clearMetrics();
    });

    it('should track timing metrics', () => {
      const endTimer = PerformanceMonitor.startTimer('test-operation');
      
      // Simulate some work
      const start = performance.now();
      while (performance.now() - start < 10) {
        // Busy wait for 10ms
      }
      
      endTimer();

      const metrics = PerformanceMonitor.getMetrics('test-operation');
      expect(metrics).not.toBeNull();
      expect(metrics!.count).toBe(1);
      expect(metrics!.avg).toBeGreaterThan(5); // Should be at least 5ms
    });

    it('should calculate correct statistics', () => {
      const label = 'test-stats';
      
      // Add multiple measurements
      const endTimer1 = PerformanceMonitor.startTimer(label);
      setTimeout(endTimer1, 0); // ~0ms
      
      const endTimer2 = PerformanceMonitor.startTimer(label);
      const start = performance.now();
      while (performance.now() - start < 20) {} // ~20ms
      endTimer2();

      const metrics = PerformanceMonitor.getMetrics(label);
      expect(metrics).not.toBeNull();
      expect(metrics!.count).toBe(2);
      expect(metrics!.min).toBeLessThan(metrics!.max);
    });

    it('should limit stored measurements', () => {
      const label = 'test-limit';
      
      // Add more than 100 measurements
      for (let i = 0; i < 150; i++) {
        const endTimer = PerformanceMonitor.startTimer(label);
        endTimer();
      }

      const metrics = PerformanceMonitor.getMetrics(label);
      expect(metrics!.count).toBeLessThanOrEqual(100);
    });

    it('should return all metrics', () => {
      PerformanceMonitor.startTimer('operation1')();
      PerformanceMonitor.startTimer('operation2')();

      const allMetrics = PerformanceMonitor.getAllMetrics();
      expect(Object.keys(allMetrics)).toContain('operation1');
      expect(Object.keys(allMetrics)).toContain('operation2');
    });
  });

  describe('useDebounce hook', () => {
    it('should debounce function calls', async () => {
      const mockFn = jest.fn();
      const delay = 100;

      const { result } = renderHook(() => useDebounce(mockFn, delay));

      // Call multiple times rapidly
      act(() => {
        result.current('call1');
        result.current('call2');
        result.current('call3');
      });

      // Should not have been called yet
      expect(mockFn).not.toHaveBeenCalled();

      // Wait for debounce delay
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, delay + 10));
      });

      // Should have been called only once with the last value
      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(mockFn).toHaveBeenCalledWith('call3');
    });

    it('should reset debounce timer on new calls', async () => {
      const mockFn = jest.fn();
      const delay = 100;

      const { result } = renderHook(() => useDebounce(mockFn, delay));

      act(() => {
        result.current('call1');
      });

      // Wait half the delay
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, delay / 2));
      });

      // Make another call (should reset timer)
      act(() => {
        result.current('call2');
      });

      // Wait another half delay (total time < full delay since last call)
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, delay / 2));
      });

      // Should not have been called yet
      expect(mockFn).not.toHaveBeenCalled();

      // Wait for remaining time
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, delay / 2 + 10));
      });

      // Should have been called with the last value
      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(mockFn).toHaveBeenCalledWith('call2');
    });
  });

  describe('useVirtualScrolling hook', () => {
    const mockItems = Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` }));
    const itemHeight = 50;
    const containerHeight = 400;

    it('should calculate visible items correctly', () => {
      const { result } = renderHook(() => 
        useVirtualScrolling(mockItems, itemHeight, containerHeight)
      );

      const { visibleItems, startIndex, endIndex } = result.current;

      expect(startIndex).toBe(0);
      expect(endIndex).toBeGreaterThan(startIndex);
      expect(visibleItems.length).toBeGreaterThan(0);
      expect(visibleItems.length).toBeLessThanOrEqual(mockItems.length);
    });

    it('should update visible items on scroll', () => {
      const { result } = renderHook(() => 
        useVirtualScrolling(mockItems, itemHeight, containerHeight)
      );

      const initialStartIndex = result.current.startIndex;

      // Simulate scroll event
      const mockScrollEvent = {
        currentTarget: { scrollTop: 500 }
      } as React.UIEvent<HTMLDivElement>;

      act(() => {
        result.current.handleScroll(mockScrollEvent);
      });

      expect(result.current.startIndex).toBeGreaterThan(initialStartIndex);
    });

    it('should calculate total height correctly', () => {
      const { result } = renderHook(() => 
        useVirtualScrolling(mockItems, itemHeight, containerHeight)
      );

      expect(result.current.totalHeight).toBe(mockItems.length * itemHeight);
    });

    it('should calculate offset correctly', () => {
      const { result } = renderHook(() => 
        useVirtualScrolling(mockItems, itemHeight, containerHeight)
      );

      const { startIndex, offsetY } = result.current;
      expect(offsetY).toBe(startIndex * itemHeight);
    });

    it('should handle empty items array', () => {
      const { result } = renderHook(() => 
        useVirtualScrolling([], itemHeight, containerHeight)
      );

      expect(result.current.visibleItems).toEqual([]);
      expect(result.current.totalHeight).toBe(0);
      expect(result.current.startIndex).toBe(0);
      expect(result.current.endIndex).toBe(-1);
    });
  });

  describe('Performance Benchmarks', () => {
    it('should handle large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: `item-${i}`,
        name: `Item ${i}`,
        amount: Math.random() * 1000,
        date: new Date().toISOString(),
      }));

      const startTime = performance.now();

      // Simulate filtering and sorting operations
      const filtered = largeDataset.filter(item => item.amount > 500);
      const sorted = filtered.sort((a, b) => b.amount - a.amount);
      const paginated = sorted.slice(0, 50);

      const endTime = performance.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(100); // 100ms threshold
      expect(paginated.length).toBeLessThanOrEqual(50);
    });

    it('should cache frequently accessed data', () => {
      const cache = BillingCache.getInstance();
      const testData = { large: 'data'.repeat(1000) };
      const cacheKey = 'performance-test';

      // First access - cache miss
      const startTime1 = performance.now();
      cache.set(cacheKey, testData);
      const result1 = cache.get(cacheKey);
      const duration1 = performance.now() - startTime1;

      // Second access - cache hit
      const startTime2 = performance.now();
      const result2 = cache.get(cacheKey);
      const duration2 = performance.now() - startTime2;

      expect(result1).toEqual(testData);
      expect(result2).toEqual(testData);
      expect(duration2).toBeLessThan(duration1); // Cache hit should be faster
    });

    it('should handle concurrent operations efficiently', async () => {
      const cache = BillingCache.getInstance();
      const operations = [];

      // Create multiple concurrent cache operations
      for (let i = 0; i < 100; i++) {
        operations.push(
          Promise.resolve().then(() => {
            cache.set(`key-${i}`, { data: i });
            return cache.get(`key-${i}`);
          })
        );
      }

      const startTime = performance.now();
      const results = await Promise.all(operations);
      const duration = performance.now() - startTime;

      expect(results).toHaveLength(100);
      expect(duration).toBeLessThan(50); // Should complete quickly
    });
  });

  describe('Memory Management', () => {
    it('should not cause memory leaks with large datasets', () => {
      const cache = BillingCache.getInstance();
      const initialMemory = process.memoryUsage().heapUsed;

      // Add and remove large amounts of data
      for (let i = 0; i < 1000; i++) {
        const largeData = { data: 'x'.repeat(1000), id: i };
        cache.set(`temp-${i}`, largeData);
      }

      // Clear cache
      cache.clear();

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;

      // Memory increase should be reasonable (adjust threshold as needed)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // 10MB threshold
    });
  });

  describe('Error Handling Performance', () => {
    it('should handle errors without performance degradation', () => {
      const cache = BillingCache.getInstance();
      const startTime = performance.now();

      // Perform operations that might cause errors
      for (let i = 0; i < 100; i++) {
        try {
          cache.get(`non-existent-${i}`);
          cache.set(`key-${i}`, null);
          cache.delete(`another-non-existent-${i}`);
        } catch (error) {
          // Ignore errors for this test
        }
      }

      const duration = performance.now() - startTime;
      expect(duration).toBeLessThan(50); // Should handle errors quickly
    });
  });
});
