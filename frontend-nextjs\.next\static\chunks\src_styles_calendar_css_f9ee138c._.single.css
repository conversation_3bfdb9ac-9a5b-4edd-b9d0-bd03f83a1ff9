/* [project]/src/styles/calendar.css [app-client] (css) */
.rbc-calendar {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: inherit;
}

.rbc-header {
  background-color: hsl(var(--muted) / .5);
  color: hsl(var(--muted-foreground));
  border-bottom: 1px solid hsl(var(--border));
  padding: 8px 4px;
  font-size: .875rem;
  font-weight: 500;
}

.rbc-header + .rbc-header {
  border-left: 1px solid hsl(var(--border));
}

.rbc-toolbar {
  background-color: hsl(var(--muted) / .3);
  border-radius: .5rem;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: .5rem;
  display: flex;
}

.rbc-toolbar button {
  color: hsl(var(--foreground));
  background-color: #0000;
  border: 1px solid #0000;
  border-radius: .375rem;
  padding: .375rem .75rem;
  font-size: .875rem;
  font-weight: 500;
  transition: colors .2s;
}

.rbc-toolbar button:hover {
  background-color: hsl(var(--accent));
  color: hsl(var(--accent-foreground));
}

.rbc-toolbar button:focus {
  box-shadow: 0 0 0 2px hsl(var(--ring));
  outline: none;
}

.rbc-toolbar button.rbc-active {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.rbc-toolbar-label {
  color: hsl(var(--foreground));
  font-size: 1.125rem;
  font-weight: 600;
}

.rbc-month-view {
  border: 1px solid hsl(var(--border));
  border-radius: .5rem;
  overflow: hidden;
}

.rbc-month-row {
  border-bottom: 1px solid hsl(var(--border));
}

.rbc-month-row:last-child {
  border-bottom: none;
}

.rbc-date-cell {
  text-align: right;
  padding: .25rem;
}

.rbc-date-cell > a {
  color: hsl(var(--muted-foreground));
  font-size: .875rem;
  transition: color .2s;
}

.rbc-date-cell > a:hover {
  color: hsl(var(--foreground));
}

.rbc-date-cell.rbc-off-range {
  color: hsl(var(--muted-foreground) / .5);
}

.rbc-date-cell.rbc-now {
  font-weight: 600;
}

.rbc-date-cell.rbc-now > a {
  color: hsl(var(--primary));
}

.rbc-day-bg {
  background-color: hsl(var(--background));
  transition: background-color .2s;
}

.rbc-day-bg:hover {
  background-color: hsl(var(--muted) / .3);
}

.rbc-day-bg.rbc-off-range-bg {
  background-color: hsl(var(--muted) / .2);
}

.rbc-day-bg.rbc-today {
  background-color: hsl(var(--primary) / .1);
}

.rbc-day-bg + .rbc-day-bg {
  border-left: 1px solid hsl(var(--border));
}

.rbc-event {
  cursor: pointer;
  border-radius: 4px;
  margin: 1px 2px;
  padding: 2px 4px;
  font-size: .75rem;
  font-weight: 500;
}

.rbc-event:hover {
  opacity: .8;
}

.rbc-event.rbc-selected {
  box-shadow: 0 0 0 2px hsl(var(--ring)), 0 0 0 4px transparent;
}

.rbc-event-label {
  font-size: .75rem;
  font-weight: 500;
}

.rbc-event-overlaps {
  box-shadow: 0 1px 2px #0000000d;
}

.rbc-time-view {
  border: 1px solid hsl(var(--border));
  border-radius: .5rem;
  overflow: hidden;
}

.rbc-time-header {
  border-bottom: 1px solid hsl(var(--border));
  background-color: hsl(var(--muted) / .3);
}

.rbc-time-content {
  background-color: hsl(var(--background));
}

.rbc-time-gutter {
  background-color: hsl(var(--muted) / .2);
  border-right: 1px solid hsl(var(--border));
}

.rbc-time-gutter .rbc-timeslot-group {
  border-bottom: 1px solid hsl(var(--border) / .5);
}

.rbc-time-slot {
  border-bottom: 1px solid hsl(var(--border) / .3);
}

.rbc-time-slot.rbc-now {
  background-color: hsl(var(--primary) / .05);
}

.rbc-current-time-indicator {
  background-color: hsl(var(--primary));
  z-index: 10;
  height: 2px;
}

.rbc-agenda-view {
  border: 1px solid hsl(var(--border));
  border-radius: .5rem;
  overflow: hidden;
}

.rbc-agenda-view table {
  width: 100%;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  background-color: hsl(var(--muted) / .3);
  border-bottom: 1px solid hsl(var(--border));
  padding: .75rem;
  font-size: .875rem;
  font-weight: 500;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  color: hsl(var(--muted-foreground));
  border-bottom: 1px solid hsl(var(--border));
  padding: .75rem;
  font-size: .875rem;
}

.rbc-agenda-view .rbc-agenda-event-cell {
  border-bottom: 1px solid hsl(var(--border));
  padding: .75rem;
}

.rbc-overlay {
  background-color: hsl(var(--popover));
  color: hsl(var(--popover-foreground));
  border: 1px solid hsl(var(--border));
  z-index: 50;
  border-radius: .5rem;
  box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
}

.rbc-overlay-header {
  border-bottom: 1px solid hsl(var(--border));
  padding: .75rem;
  font-weight: 500;
}

.rbc-show-more {
  color: hsl(var(--primary));
  cursor: pointer;
  font-size: .75rem;
  font-weight: 500;
  transition: color .2s;
}

.rbc-show-more:hover {
  color: hsl(var(--primary) / .8);
}

@media (width <= 768px) {
  .rbc-toolbar {
    flex-direction: column;
    gap: .5rem;
  }

  .rbc-toolbar button {
    padding: .25rem .5rem;
    font-size: .75rem;
  }

  .rbc-toolbar-label {
    font-size: 1rem;
  }

  .rbc-event {
    padding: 1px 2px;
    font-size: .75rem;
  }

  .rbc-header {
    padding: 4px 2px;
    font-size: .75rem;
  }
}

.rbc-event.status-scheduled {
  color: #fff;
  background-color: #3b82f6;
}

.rbc-event.status-confirmed {
  color: #fff;
  background-color: #10b981;
}

.rbc-event.status-completed {
  color: #fff;
  background-color: #6b7280;
}

.rbc-event.status-cancelled {
  color: #fff;
  background-color: #ef4444;
}

.rbc-event.status-no-show {
  color: #fff;
  background-color: #f59e0b;
}

.rbc-event.type-consultation {
  border-left: 4px solid #8b5cf6;
}

.rbc-event.type-treatment {
  border-left: 4px solid #06b6d4;
}

.calendar-loading {
  animation: 2s cubic-bezier(.4, 0, .6, 1) infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

.rbc-event.has-conflict {
  box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px #0000;
}

.rbc-event.has-conflict:after {
  content: "⚠️";
  font-size: .75rem;
  position: absolute;
  top: -.25rem;
  right: -.25rem;
}

/*# sourceMappingURL=src_styles_calendar_css_f9ee138c._.single.css.map*/