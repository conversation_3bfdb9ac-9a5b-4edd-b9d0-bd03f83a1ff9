{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/calendar.css"], "sourcesContent": ["/* Custom styles for react-big-calendar to match Shadcn/ui theme */\n\n/* Calendar container */\n.rbc-calendar {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n  font-family: inherit;\n}\n\n/* Header styles */\n.rbc-header {\n  background-color: hsl(var(--muted) / 0.5);\n  color: hsl(var(--muted-foreground));\n  font-weight: 500;\n  font-size: 0.875rem;\n  border-bottom: 1px solid hsl(var(--border));\n  padding: 8px 4px;\n}\n\n.rbc-header + .rbc-header {\n  border-left: 1px solid hsl(var(--border));\n}\n\n/* Toolbar styles */\n.rbc-toolbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 1rem;\n  padding: 0.5rem;\n  background-color: hsl(var(--muted) / 0.3);\n  border-radius: 0.5rem;\n}\n\n.rbc-toolbar button {\n  padding: 0.375rem 0.75rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: 0.375rem;\n  transition: colors 0.2s;\n  border: 1px solid transparent;\n  background-color: transparent;\n  color: hsl(var(--foreground));\n}\n\n.rbc-toolbar button:hover {\n  background-color: hsl(var(--accent));\n  color: hsl(var(--accent-foreground));\n}\n\n.rbc-toolbar button:focus {\n  outline: none;\n  box-shadow: 0 0 0 2px hsl(var(--ring));\n}\n\n.rbc-toolbar button.rbc-active {\n  background-color: hsl(var(--primary));\n  color: hsl(var(--primary-foreground));\n}\n\n.rbc-toolbar-label {\n  font-size: 1.125rem;\n  font-weight: 600;\n  color: hsl(var(--foreground));\n}\n\n/* Month view styles */\n.rbc-month-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-month-row {\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-month-row:last-child {\n  border-bottom: none;\n}\n\n.rbc-date-cell {\n  text-align: right;\n  padding: 0.25rem;\n}\n\n.rbc-date-cell > a {\n  font-size: 0.875rem;\n  color: hsl(var(--muted-foreground));\n  transition: color 0.2s;\n}\n\n.rbc-date-cell > a:hover {\n  color: hsl(var(--foreground));\n}\n\n.rbc-date-cell.rbc-off-range {\n  color: hsl(var(--muted-foreground) / 0.5);\n}\n\n.rbc-date-cell.rbc-now {\n  font-weight: 600;\n}\n\n.rbc-date-cell.rbc-now > a {\n  color: hsl(var(--primary));\n}\n\n/* Day cell styles */\n.rbc-day-bg {\n  background-color: hsl(var(--background));\n  transition: background-color 0.2s;\n}\n\n.rbc-day-bg:hover {\n  background-color: hsl(var(--muted) / 0.3);\n}\n\n.rbc-day-bg.rbc-off-range-bg {\n  background-color: hsl(var(--muted) / 0.2);\n}\n\n.rbc-day-bg.rbc-today {\n  background-color: hsl(var(--primary) / 0.1);\n}\n\n.rbc-day-bg + .rbc-day-bg {\n  border-left: 1px solid hsl(var(--border));\n}\n\n/* Event styles */\n.rbc-event {\n  border-radius: 0.125rem;\n  font-size: 0.75rem;\n  font-weight: 500;\n  padding: 2px 4px;\n  margin: 1px 2px;\n  border-radius: 4px;\n  cursor: pointer;\n}\n\n.rbc-event:hover {\n  opacity: 0.8;\n}\n\n.rbc-event.rbc-selected {\n  box-shadow: 0 0 0 2px hsl(var(--ring)), 0 0 0 4px transparent;\n}\n\n.rbc-event-label {\n  font-size: 0.75rem;\n  font-weight: 500;\n}\n\n.rbc-event-overlaps {\n  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n}\n\n/* Week and day view styles */\n.rbc-time-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-time-header {\n  border-bottom: 1px solid hsl(var(--border));\n  background-color: hsl(var(--muted) / 0.3);\n}\n\n.rbc-time-content {\n  background-color: hsl(var(--background));\n}\n\n.rbc-time-gutter {\n  background-color: hsl(var(--muted) / 0.2);\n  border-right: 1px solid hsl(var(--border));\n}\n\n.rbc-time-gutter .rbc-timeslot-group {\n  border-bottom: 1px solid hsl(var(--border) / 0.5);\n}\n\n.rbc-time-slot {\n  border-bottom: 1px solid hsl(var(--border) / 0.3);\n}\n\n.rbc-time-slot.rbc-now {\n  background-color: hsl(var(--primary) / 0.05);\n}\n\n.rbc-current-time-indicator {\n  background-color: hsl(var(--primary));\n  height: 2px;\n  z-index: 10;\n}\n\n/* Agenda view styles */\n.rbc-agenda-view {\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  overflow: hidden;\n}\n\n.rbc-agenda-view table {\n  width: 100%;\n}\n\n.rbc-agenda-view .rbc-agenda-date-cell {\n  background-color: hsl(var(--muted) / 0.3);\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-agenda-view .rbc-agenda-time-cell {\n  padding: 0.75rem;\n  font-size: 0.875rem;\n  color: hsl(var(--muted-foreground));\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n.rbc-agenda-view .rbc-agenda-event-cell {\n  padding: 0.75rem;\n  border-bottom: 1px solid hsl(var(--border));\n}\n\n/* Popup styles */\n.rbc-overlay {\n  background-color: hsl(var(--popover));\n  color: hsl(var(--popover-foreground));\n  border: 1px solid hsl(var(--border));\n  border-radius: 0.5rem;\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  z-index: 50;\n}\n\n.rbc-overlay-header {\n  padding: 0.75rem;\n  border-bottom: 1px solid hsl(var(--border));\n  font-weight: 500;\n}\n\n/* Show more link */\n.rbc-show-more {\n  color: hsl(var(--primary));\n  font-size: 0.75rem;\n  font-weight: 500;\n  cursor: pointer;\n  transition: color 0.2s;\n}\n\n.rbc-show-more:hover {\n  color: hsl(var(--primary) / 0.8);\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .rbc-toolbar {\n    flex-direction: column;\n    gap: 0.5rem;\n  }\n\n  .rbc-toolbar button {\n    font-size: 0.75rem;\n    padding: 0.25rem 0.5rem;\n  }\n\n  .rbc-toolbar-label {\n    font-size: 1rem;\n  }\n\n  .rbc-event {\n    font-size: 0.75rem;\n    padding: 1px 2px;\n  }\n\n  .rbc-header {\n    font-size: 0.75rem;\n    padding: 4px 2px;\n  }\n}\n\n/* Custom status colors - these will be overridden by eventStyleGetter */\n.rbc-event.status-scheduled {\n  background-color: #3b82f6;\n  color: white;\n}\n\n.rbc-event.status-confirmed {\n  background-color: #10b981;\n  color: white;\n}\n\n.rbc-event.status-completed {\n  background-color: #6b7280;\n  color: white;\n}\n\n.rbc-event.status-cancelled {\n  background-color: #ef4444;\n  color: white;\n}\n\n.rbc-event.status-no-show {\n  background-color: #f59e0b;\n  color: white;\n}\n\n/* Appointment type indicators */\n.rbc-event.type-consultation {\n  border-left: 4px solid #8b5cf6;\n}\n\n.rbc-event.type-treatment {\n  border-left: 4px solid #06b6d4;\n}\n\n/* Loading state */\n.calendar-loading {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: .5;\n  }\n}\n\n/* Conflict warning styles */\n.rbc-event.has-conflict {\n  box-shadow: 0 0 0 2px #ef4444, 0 0 0 4px transparent;\n}\n\n.rbc-event.has-conflict::after {\n  content: '⚠️';\n  position: absolute;\n  top: -0.25rem;\n  right: -0.25rem;\n  font-size: 0.75rem;\n}\n"], "names": [], "mappings": "AAGA;;;;;;AAOA;;;;;;;;;AASA;;;;AAKA;;;;;;;;;;AAUA;;;;;;;;;;;AAWA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAOA;;;;;;AAMA;;;;AAIA;;;;;;;;AAQA;;;;;;;AAOA;;;;;AAMA;;;;;;;;;AASA;;;;;;AAOA;;;;;;;;AAQA;;;;AAKA;EACE;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;;AAOF;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAMA;;;;AAIA;;;;AAKA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA"}}]}