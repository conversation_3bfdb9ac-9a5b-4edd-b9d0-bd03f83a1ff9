import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * POST /api/bills/bulk-operations - Handle bulk bill operations
 * Supports: bulk status updates, bulk discounts, bulk reminders, etc.
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - admin and front-desk can perform bulk operations
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions for bulk operations' },
        { status: 403 }
      );
    }

    const { operation, billIds, data } = await request.json();

    if (!operation || !billIds || !Array.isArray(billIds) || billIds.length === 0) {
      return NextResponse.json(
        { error: 'Operation type and bill IDs are required' },
        { status: 400 }
      );
    }

    const results = {
      successful: [],
      failed: [],
      summary: {
        total: billIds.length,
        success: 0,
        failed: 0,
      }
    };

    switch (operation) {
      case 'updateStatus':
        if (!data.status) {
          return NextResponse.json(
            { error: 'Status is required for status update operation' },
            { status: 400 }
          );
        }

        for (const billId of billIds) {
          try {
            const updatedBill = await makeAuthenticatedPayloadRequest(
              authContext,
              'bills',
              'update',
              {
                id: billId,
                data: {
                  status: data.status,
                  notes: data.notes ? `${data.notes} (Bulk update by ${authContext.user.email})` : undefined,
                }
              }
            );
            results.successful.push({ billId, bill: updatedBill });
            results.summary.success++;
          } catch (error) {
            results.failed.push({ billId, error: error.message });
            results.summary.failed++;
          }
        }
        break;

      case 'applyDiscount':
        if (!data.discountAmount && !data.discountRate) {
          return NextResponse.json(
            { error: 'Discount amount or rate is required' },
            { status: 400 }
          );
        }

        for (const billId of billIds) {
          try {
            // Get current bill
            const bill = await makeAuthenticatedPayloadRequest(
              authContext,
              'bills',
              'findByID',
              { id: billId }
            );

            if (!bill) {
              results.failed.push({ billId, error: 'Bill not found' });
              results.summary.failed++;
              continue;
            }

            let newDiscountAmount = bill.discountAmount || 0;
            
            if (data.discountAmount) {
              newDiscountAmount = data.discountAmount;
            } else if (data.discountRate) {
              newDiscountAmount = (bill.subtotal || 0) * (data.discountRate / 100);
            }

            const newTotalAmount = (bill.subtotal || 0) - newDiscountAmount + (bill.taxAmount || 0);
            const newRemainingAmount = newTotalAmount - (bill.paidAmount || 0);

            const updatedBill = await makeAuthenticatedPayloadRequest(
              authContext,
              'bills',
              'update',
              {
                id: billId,
                data: {
                  discountAmount: newDiscountAmount,
                  totalAmount: newTotalAmount,
                  remainingAmount: newRemainingAmount,
                  notes: `${bill.notes || ''}\n折扣应用: ${data.discountAmount ? `$${data.discountAmount}` : `${data.discountRate}%`} (${authContext.user.email})`.trim(),
                }
              }
            );

            results.successful.push({ billId, bill: updatedBill });
            results.summary.success++;
          } catch (error) {
            results.failed.push({ billId, error: error.message });
            results.summary.failed++;
          }
        }
        break;

      case 'sendReminders':
        // This would integrate with email/SMS service
        for (const billId of billIds) {
          try {
            const bill = await makeAuthenticatedPayloadRequest(
              authContext,
              'bills',
              'findByID',
              {
                id: billId,
                depth: 2,
              }
            );

            if (!bill) {
              results.failed.push({ billId, error: 'Bill not found' });
              results.summary.failed++;
              continue;
            }

            // In a real implementation, this would send email/SMS
            // For now, we'll just log the reminder
            console.log(`Reminder sent for bill ${bill.billNumber} to patient ${bill.patient.fullName}`);

            // Update bill with reminder sent note
            const updatedBill = await makeAuthenticatedPayloadRequest(
              authContext,
              'bills',
              'update',
              {
                id: billId,
                data: {
                  notes: `${bill.notes || ''}\n提醒已发送: ${new Date().toISOString()} (${authContext.user.email})`.trim(),
                }
              }
            );

            results.successful.push({ 
              billId, 
              bill: updatedBill,
              reminderSent: true,
              patientEmail: bill.patient.email,
              patientPhone: bill.patient.phone,
            });
            results.summary.success++;
          } catch (error) {
            results.failed.push({ billId, error: error.message });
            results.summary.failed++;
          }
        }
        break;

      case 'generateReports':
        // Generate summary report for selected bills
        try {
          const bills = await Promise.all(
            billIds.map(id => 
              makeAuthenticatedPayloadRequest(authContext, 'bills', 'findByID', {
                id,
                depth: 2,
              })
            )
          );

          const validBills = bills.filter(Boolean);
          
          const report = {
            totalBills: validBills.length,
            totalAmount: validBills.reduce((sum, bill) => sum + bill.totalAmount, 0),
            totalPaid: validBills.reduce((sum, bill) => sum + (bill.paidAmount || 0), 0),
            totalOutstanding: validBills.reduce((sum, bill) => sum + (bill.remainingAmount || 0), 0),
            statusBreakdown: {},
            typeBreakdown: {},
            patientBreakdown: {},
          };

          validBills.forEach(bill => {
            // Status breakdown
            const status = bill.status || 'unknown';
            if (!report.statusBreakdown[status]) {
              report.statusBreakdown[status] = { count: 0, amount: 0 };
            }
            report.statusBreakdown[status].count++;
            report.statusBreakdown[status].amount += bill.totalAmount;

            // Type breakdown
            const type = bill.billType || 'unknown';
            if (!report.typeBreakdown[type]) {
              report.typeBreakdown[type] = { count: 0, amount: 0 };
            }
            report.typeBreakdown[type].count++;
            report.typeBreakdown[type].amount += bill.totalAmount;

            // Patient breakdown
            const patientName = bill.patient?.fullName || 'Unknown';
            if (!report.patientBreakdown[patientName]) {
              report.patientBreakdown[patientName] = { count: 0, amount: 0, outstanding: 0 };
            }
            report.patientBreakdown[patientName].count++;
            report.patientBreakdown[patientName].amount += bill.totalAmount;
            report.patientBreakdown[patientName].outstanding += bill.remainingAmount || 0;
          });

          results.successful.push({ operation: 'generateReports', report });
          results.summary.success = 1;
        } catch (error) {
          results.failed.push({ operation: 'generateReports', error: error.message });
          results.summary.failed = 1;
        }
        break;

      default:
        return NextResponse.json(
          { error: `Unsupported operation: ${operation}` },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      operation,
      results,
      message: `Bulk operation completed. ${results.summary.success} successful, ${results.summary.failed} failed.`
    });

  } catch (error) {
    console.error('Error in bulk operations:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk operation' },
      { status: 500 }
    );
  }
}
