// Comprehensive toast notification utilities for appointment actions
// Provides consistent messaging in Chinese for all appointment operations

import { toast } from 'sonner';
import { Appointment } from '@/types/clinic';
import { formatDateTime } from './calendar-utils';

export const appointmentNotifications = {
  // Appointment CRUD notifications
  appointment: {
    created: (appointment: Appointment) => {
      const typeLabel = appointment.appointmentType === 'consultation' ? '咨询预约' : '治疗预约';
      toast.success(`${typeLabel}创建成功！`, {
        description: `患者: ${appointment.patient.fullName} | 时间: ${formatDateTime(new Date(appointment.appointmentDate))}`,
        duration: 4000,
      });
    },

    updated: (appointment: Appointment) => {
      const typeLabel = appointment.appointmentType === 'consultation' ? '咨询预约' : '治疗预约';
      toast.success(`${typeLabel}更新成功！`, {
        description: `患者: ${appointment.patient.fullName} | 时间: ${formatDateTime(new Date(appointment.appointmentDate))}`,
        duration: 4000,
      });
    },

    deleted: (patientName: string) => {
      toast.success(`预约删除成功！`, {
        description: `患者: ${patientName}`,
        duration: 4000,
      });
    },

    statusChanged: (appointment: Appointment, oldStatus: string, newStatus: string) => {
      const statusMap = {
        'scheduled': '已预约',
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消',
        'no-show': '未到诊'
      };
      
      const oldStatusText = statusMap[oldStatus as keyof typeof statusMap] || oldStatus;
      const newStatusText = statusMap[newStatus as keyof typeof statusMap] || newStatus;
      
      toast.success(`预约状态更新成功！`, {
        description: `${appointment.patient.fullName}: ${oldStatusText} → ${newStatusText}`,
        duration: 4000,
      });
    },

    batchStatusChanged: (count: number, status: string) => {
      const statusMap = {
        'confirmed': '已确认',
        'completed': '已完成',
        'cancelled': '已取消',
      };
      
      const statusText = statusMap[status as keyof typeof statusMap] || status;
      toast.success(`批量状态更新成功！`, {
        description: `${count} 个预约已更新为 ${statusText}`,
        duration: 4000,
      });
    },
  },

  // Conflict and validation notifications
  conflict: {
    detected: (conflictMessage: string) => {
      toast.warning(`时间冲突检测`, {
        description: conflictMessage,
        duration: 6000,
      });
    },

    resolved: () => {
      toast.success(`时间冲突已解决`, {
        description: '预约时间已调整，无冲突',
        duration: 3000,
      });
    },

    alternativeSuggested: (count: number) => {
      toast.info(`建议替代时间`, {
        description: `为您找到 ${count} 个可选时间段`,
        duration: 5000,
      });
    },
  },

  // Calendar view notifications
  calendar: {
    viewChanged: (view: string) => {
      const viewMap = {
        'month': '月视图',
        'week': '周视图',
        'day': '日视图',
        'agenda': '议程视图'
      };
      
      const viewText = viewMap[view as keyof typeof viewMap] || view;
      toast.info(`切换到${viewText}`, {
        duration: 2000,
      });
    },

    dateNavigated: (date: Date) => {
      toast.info(`导航到 ${formatDateTime(date)}`, {
        duration: 2000,
      });
    },

    eventSelected: (appointment: Appointment) => {
      toast.info(`选中预约`, {
        description: `${appointment.patient.fullName} - ${formatDateTime(new Date(appointment.appointmentDate))}`,
        duration: 3000,
      });
    },
  },

  // Reminder notifications
  reminder: {
    upcoming: (appointment: Appointment, minutesUntil: number) => {
      const timeText = minutesUntil < 60 
        ? `${minutesUntil} 分钟后`
        : `${Math.floor(minutesUntil / 60)} 小时后`;
        
      toast.info(`预约提醒`, {
        description: `${appointment.patient.fullName} 的预约将在 ${timeText} 开始`,
        duration: 8000,
      });
    },

    overdue: (appointment: Appointment) => {
      toast.error(`预约超时`, {
        description: `${appointment.patient.fullName} 的预约时间已过，状态为: ${appointment.status}`,
        duration: 10000,
      });
    },

    reminderSet: (appointment: Appointment, reminderTime: string) => {
      toast.success(`预约提醒已设置`, {
        description: `将在 ${reminderTime} 提醒 ${appointment.patient.fullName} 的预约`,
        duration: 4000,
      });
    },
  },

  // Form validation notifications
  validation: {
    invalidTime: (message: string) => {
      toast.error(`时间验证失败`, {
        description: message,
        duration: 5000,
      });
    },

    missingRequired: (fieldName: string) => {
      toast.error(`必填字段缺失`, {
        description: `请填写: ${fieldName}`,
        duration: 4000,
      });
    },

    invalidDuration: (duration: number) => {
      toast.error(`预约时长无效`, {
        description: `时长 ${duration} 分钟不在有效范围内 (5-480分钟)`,
        duration: 4000,
      });
    },

    pastDate: () => {
      toast.error(`预约时间无效`, {
        description: '预约时间不能早于当前时间超过24小时',
        duration: 4000,
      });
    },
  },

  // Data loading notifications
  loading: {
    fetchingAppointments: () => {
      return toast.loading(`加载预约数据中...`, {
        duration: Infinity,
      });
    },

    savingAppointment: (isEditing: boolean) => {
      return toast.loading(`${isEditing ? '更新' : '创建'}预约中...`, {
        duration: Infinity,
      });
    },

    deletingAppointment: () => {
      return toast.loading(`删除预约中...`, {
        duration: Infinity,
      });
    },

    checkingConflicts: () => {
      return toast.loading(`检查时间冲突中...`, {
        duration: Infinity,
      });
    },
  },

  // Error notifications
  error: {
    fetchFailed: () => {
      toast.error(`获取预约数据失败`, {
        description: '请检查网络连接后重试',
        duration: 5000,
      });
    },

    saveFailed: (isEditing: boolean) => {
      toast.error(`${isEditing ? '更新' : '创建'}预约失败`, {
        description: '请稍后重试或联系管理员',
        duration: 5000,
      });
    },

    deleteFailed: () => {
      toast.error(`删除预约失败`, {
        description: '请稍后重试或联系管理员',
        duration: 5000,
      });
    },

    permissionDenied: (action: string) => {
      toast.error(`权限不足`, {
        description: `您没有权限执行: ${action}`,
        duration: 5000,
      });
    },

    networkError: () => {
      toast.error(`网络连接失败`, {
        description: '请检查网络连接后重试',
        duration: 5000,
      });
    },
  },

  // Success notifications
  success: {
    dataRefreshed: () => {
      toast.success(`预约数据刷新成功`, {
        duration: 2000,
      });
    },

    bulkOperation: (operation: string, count: number) => {
      toast.success(`批量${operation}成功`, {
        description: `已处理 ${count} 个预约`,
        duration: 4000,
      });
    },

    exportCompleted: (format: string) => {
      toast.success(`预约数据导出成功`, {
        description: `已导出为 ${format} 格式`,
        duration: 4000,
      });
    },
  },
};

// Utility function to dismiss all appointment-related toasts
export const dismissAppointmentToasts = () => {
  toast.dismiss();
};

// Utility function to show custom appointment toast with consistent styling
export const showAppointmentToast = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  description?: string,
  duration: number = 4000
) => {
  const toastFunction = toast[type];
  toastFunction(title, {
    description,
    duration,
  });
};

// Format loading message for appointments
export const formatLoadingMessage = (action: 'create' | 'update' | 'delete', entity: string) => {
  const actionMap = {
    create: '创建',
    update: '更新',
    delete: '删除'
  };
  
  const entityMap = {
    appointment: '预约'
  };
  
  return `${actionMap[action]}${entityMap[entity as keyof typeof entityMap] || entity}中...`;
};
