{"version": "3.2.4", "results": [[":src/components/appointments/__tests__/appointments-list.test.tsx", {"duration": 503.22029999999995, "failed": true}], [":src/test/components/patients-page.test.tsx", {"duration": 9.373599999999897, "failed": true}], [":src/components/appointments/__tests__/appointment-filters.test.tsx", {"duration": 269.1800000000003, "failed": true}], [":src/components/appointments/__tests__/appointment-form-dialog.test.tsx", {"duration": 1664.6122000000005, "failed": true}], [":src/test/integration/clinic-workflows.test.tsx", {"duration": 3413.987500000001, "failed": true}], [":src/components/treatments/__tests__/treatment-form-dialog.test.tsx", {"duration": 410.8724000000002, "failed": true}], [":src/components/patients/__tests__/patient-form-dialog.test.tsx", {"duration": 378.0241000000001, "failed": true}], [":src/components/treatments/__tests__/treatments-list.test.tsx", {"duration": 3341.3487000000005, "failed": true}], [":src/app/dashboard/patients/__tests__/page.test.tsx", {"duration": 3528.2339999999995, "failed": true}], [":src/test/integration/api-endpoints.test.tsx", {"duration": 28.83120000000008, "failed": true}], [":src/__tests__/e2e/billing-e2e.spec.ts", {"duration": 0, "failed": true}], [":src/__tests__/billing/billing-integration.test.ts", {"duration": 0, "failed": true}], [":src/__tests__/billing/billing-api.test.ts", {"duration": 0, "failed": true}], [":src/__tests__/appointment-system.test.ts", {"duration": 12.081699999999728, "failed": false}]]}