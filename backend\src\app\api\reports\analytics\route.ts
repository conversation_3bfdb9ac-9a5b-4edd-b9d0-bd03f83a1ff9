import { NextRequest, NextResponse } from 'next/server'
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware'

/**
 * GET /api/reports/analytics - Advanced financial analytics and business insights
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check permissions - only admin and front-desk can view analytics
    if (!['admin', 'front-desk'].includes(authContext.user.role)) {
      return NextResponse.json(
        { error: 'Insufficient permissions to view analytics' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const period = searchParams.get('period') || '30'; // Default to 30 days

    // Calculate date range
    const end = endDate ? new Date(endDate) : new Date();
    const start = startDate ? new Date(startDate) : new Date(Date.now() - parseInt(period) * 24 * 60 * 60 * 1000);

    const dateFilter = {
      createdAt: {
        greater_than_equal: start.toISOString(),
        less_than_equal: end.toISOString(),
      }
    };

    // Fetch all relevant data
    const [bills, payments, deposits, appointments, patients] = await Promise.all([
      makeAuthenticatedPayloadRequest(authContext, 'bills', 'find', {
        where: dateFilter,
        limit: 10000,
        depth: 1,
      }),
      makeAuthenticatedPayloadRequest(authContext, 'payments', 'find', {
        where: dateFilter,
        limit: 10000,
        depth: 1,
      }),
      makeAuthenticatedPayloadRequest(authContext, 'deposits', 'find', {
        where: dateFilter,
        limit: 10000,
        depth: 1,
      }),
      makeAuthenticatedPayloadRequest(authContext, 'appointments', 'find', {
        where: dateFilter,
        limit: 10000,
        depth: 1,
      }),
      makeAuthenticatedPayloadRequest(authContext, 'patients', 'find', {
        where: dateFilter,
        limit: 10000,
      }),
    ]);

    // Revenue Analytics
    const revenueAnalytics = {
      totalRevenue: payments.docs.reduce((sum, payment) => sum + payment.amount, 0),
      averageTransactionValue: payments.docs.length > 0 ? 
        payments.docs.reduce((sum, payment) => sum + payment.amount, 0) / payments.docs.length : 0,
      totalBilled: bills.docs.reduce((sum, bill) => sum + bill.totalAmount, 0),
      totalOutstanding: bills.docs.reduce((sum, bill) => sum + (bill.remainingAmount || 0), 0),
      collectionRate: bills.docs.reduce((sum, bill) => sum + bill.totalAmount, 0) > 0 ?
        (payments.docs.reduce((sum, payment) => sum + payment.amount, 0) / 
         bills.docs.reduce((sum, bill) => sum + bill.totalAmount, 0)) * 100 : 0,
    };

    // Payment Method Analysis
    const paymentMethodStats = {};
    payments.docs.forEach(payment => {
      const method = payment.paymentMethod || 'unknown';
      if (!paymentMethodStats[method]) {
        paymentMethodStats[method] = { count: 0, amount: 0 };
      }
      paymentMethodStats[method].count++;
      paymentMethodStats[method].amount += payment.amount;
    });

    // Bill Status Analysis
    const billStatusStats = {};
    bills.docs.forEach(bill => {
      const status = bill.status || 'unknown';
      if (!billStatusStats[status]) {
        billStatusStats[status] = { count: 0, amount: 0 };
      }
      billStatusStats[status].count++;
      billStatusStats[status].amount += bill.totalAmount;
    });

    // Treatment Type Analysis
    const treatmentStats = {};
    bills.docs.forEach(bill => {
      const type = bill.billType || 'unknown';
      if (!treatmentStats[type]) {
        treatmentStats[type] = { count: 0, amount: 0 };
      }
      treatmentStats[type].count++;
      treatmentStats[type].amount += bill.totalAmount;
    });

    // Deposit Analytics
    const depositAnalytics = {
      totalDeposits: deposits.docs.reduce((sum, deposit) => sum + deposit.amount, 0),
      totalUsedDeposits: deposits.docs.reduce((sum, deposit) => sum + (deposit.usedAmount || 0), 0),
      totalRemainingDeposits: deposits.docs.reduce((sum, deposit) => sum + (deposit.remainingAmount || 0), 0),
      utilizationRate: deposits.docs.reduce((sum, deposit) => sum + deposit.amount, 0) > 0 ?
        (deposits.docs.reduce((sum, deposit) => sum + (deposit.usedAmount || 0), 0) / 
         deposits.docs.reduce((sum, deposit) => sum + deposit.amount, 0)) * 100 : 0,
    };

    // Patient Analytics
    const patientAnalytics = {
      newPatients: patients.docs.filter(p => p.userType === 'patient').length,
      consultationUsers: patients.docs.filter(p => p.userType === 'consultation').length,
      conversionRate: patients.docs.filter(p => p.userType === 'consultation').length > 0 ?
        (patients.docs.filter(p => p.status === 'converted').length / 
         patients.docs.filter(p => p.userType === 'consultation').length) * 100 : 0,
      averageRevenuePerPatient: patients.docs.length > 0 ? revenueAnalytics.totalRevenue / patients.docs.length : 0,
    };

    // Appointment Analytics
    const appointmentAnalytics = {
      totalAppointments: appointments.docs.length,
      consultationAppointments: appointments.docs.filter(a => a.appointmentType === 'consultation').length,
      treatmentAppointments: appointments.docs.filter(a => a.appointmentType === 'treatment').length,
      completedAppointments: appointments.docs.filter(a => a.status === 'completed').length,
      completionRate: appointments.docs.length > 0 ?
        (appointments.docs.filter(a => a.status === 'completed').length / appointments.docs.length) * 100 : 0,
    };

    // Time-based trends (daily breakdown)
    const dailyTrends = {};
    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dateKey = currentDate.toISOString().split('T')[0];
      dailyTrends[dateKey] = {
        revenue: 0,
        bills: 0,
        payments: 0,
        appointments: 0,
      };
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Populate daily trends
    payments.docs.forEach(payment => {
      const dateKey = new Date(payment.createdAt).toISOString().split('T')[0];
      if (dailyTrends[dateKey]) {
        dailyTrends[dateKey].revenue += payment.amount;
        dailyTrends[dateKey].payments++;
      }
    });

    bills.docs.forEach(bill => {
      const dateKey = new Date(bill.createdAt).toISOString().split('T')[0];
      if (dailyTrends[dateKey]) {
        dailyTrends[dateKey].bills++;
      }
    });

    appointments.docs.forEach(appointment => {
      const dateKey = new Date(appointment.createdAt).toISOString().split('T')[0];
      if (dailyTrends[dateKey]) {
        dailyTrends[dateKey].appointments++;
      }
    });

    // Top performing metrics
    const topMetrics = {
      highestRevenueDay: Object.entries(dailyTrends).reduce((max, [date, data]) => 
        data.revenue > (max.revenue || 0) ? { date, ...data } : max, {}),
      mostActiveDay: Object.entries(dailyTrends).reduce((max, [date, data]) => 
        data.appointments > (max.appointments || 0) ? { date, ...data } : max, {}),
    };

    return NextResponse.json({
      period: {
        startDate: start.toISOString(),
        endDate: end.toISOString(),
        days: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)),
      },
      revenue: revenueAnalytics,
      paymentMethods: paymentMethodStats,
      billStatus: billStatusStats,
      treatments: treatmentStats,
      deposits: depositAnalytics,
      patients: patientAnalytics,
      appointments: appointmentAnalytics,
      trends: {
        daily: dailyTrends,
        topMetrics,
      },
      summary: {
        totalTransactions: payments.docs.length,
        totalBills: bills.docs.length,
        totalDeposits: deposits.docs.length,
        totalAppointments: appointments.docs.length,
        totalPatients: patients.docs.length,
      }
    });

  } catch (error) {
    console.error('Error generating analytics:', error);
    return NextResponse.json(
      { error: 'Failed to generate analytics' },
      { status: 500 }
    );
  }
}
