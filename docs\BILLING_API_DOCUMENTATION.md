# 医疗诊所账单系统 API 文档

## 概述

本文档详细描述了医疗诊所账单系统的所有API端点，包括请求格式、响应格式、错误处理和使用示例。

## 基础信息

- **基础URL**: `http://localhost:3000/api` (开发环境)
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`
- **字符编码**: UTF-8
- **货币单位**: USD (美金)

## 认证

所有API请求都需要在请求头中包含有效的认证令牌：

```http
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

## 账单管理 API

### 1. 创建账单

**端点**: `POST /api/bills`

**描述**: 创建新的账单记录

**请求体**:
```json
{
  "patient": "patient-uuid",
  "billType": "treatment",
  "subtotal": 1000.00,
  "discountAmount": 100.00,
  "taxAmount": 50.00,
  "totalAmount": 950.00,
  "dueDate": "2025-02-01T00:00:00.000Z",
  "notes": "面部护理治疗",
  "items": [
    {
      "description": "面部深层清洁",
      "quantity": 1,
      "unitPrice": 800.00,
      "discountRate": 0,
      "totalPrice": 800.00
    }
  ]
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "bill-uuid",
    "billNumber": "BILL-20250109-123456",
    "patient": "patient-uuid",
    "billType": "treatment",
    "subtotal": 1000.00,
    "discountAmount": 100.00,
    "taxAmount": 50.00,
    "totalAmount": 950.00,
    "paidAmount": 0.00,
    "remainingAmount": 950.00,
    "status": "pending",
    "dueDate": "2025-02-01T00:00:00.000Z",
    "createdAt": "2025-01-09T10:00:00.000Z",
    "updatedAt": "2025-01-09T10:00:00.000Z"
  }
}
```

### 2. 获取账单列表

**端点**: `GET /api/bills`

**查询参数**:
- `page` (number): 页码，默认1
- `limit` (number): 每页数量，默认20，最大100
- `status` (string): 账单状态筛选
- `patient` (string): 患者ID筛选
- `startDate` (string): 开始日期
- `endDate` (string): 结束日期
- `search` (string): 搜索关键词

**示例请求**:
```http
GET /api/bills?page=1&limit=20&status=pending&search=张三
```

**响应**:
```json
{
  "docs": [
    {
      "id": "bill-uuid",
      "billNumber": "BILL-20250109-123456",
      "patient": {
        "id": "patient-uuid",
        "fullName": "张三",
        "phone": "13800138000"
      },
      "totalAmount": 950.00,
      "remainingAmount": 950.00,
      "status": "pending",
      "createdAt": "2025-01-09T10:00:00.000Z"
    }
  ],
  "totalDocs": 1,
  "limit": 20,
  "page": 1,
  "totalPages": 1,
  "hasNextPage": false,
  "hasPrevPage": false
}
```

### 3. 获取单个账单

**端点**: `GET /api/bills/{id}`

**响应**:
```json
{
  "success": true,
  "data": {
    "id": "bill-uuid",
    "billNumber": "BILL-20250109-123456",
    "patient": {
      "id": "patient-uuid",
      "fullName": "张三",
      "phone": "13800138000",
      "email": "<EMAIL>"
    },
    "items": [
      {
        "id": "item-uuid",
        "description": "面部深层清洁",
        "quantity": 1,
        "unitPrice": 800.00,
        "totalPrice": 800.00
      }
    ],
    "payments": [
      {
        "id": "payment-uuid",
        "amount": 500.00,
        "paymentMethod": "cash",
        "paymentDate": "2025-01-09T10:30:00.000Z"
      }
    ],
    "totalAmount": 950.00,
    "paidAmount": 500.00,
    "remainingAmount": 450.00,
    "status": "partial"
  }
}
```

### 4. 更新账单

**端点**: `PUT /api/bills/{id}`

**请求体**: 与创建账单相同，但所有字段都是可选的

### 5. 删除账单

**端点**: `DELETE /api/bills/{id}`

**响应**:
```json
{
  "success": true,
  "message": "账单删除成功"
}
```

## 支付管理 API

### 1. 处理支付

**端点**: `POST /api/payments/process-payment`

**描述**: 处理复杂支付场景，支持多种支付方式和押金抵扣

**请求体**:
```json
{
  "billId": "bill-uuid",
  "paymentMethods": [
    {
      "method": "cash",
      "amount": 500.00,
      "transactionId": "TXN-123456789",
      "notes": "现金支付"
    },
    {
      "method": "card",
      "amount": 450.00,
      "transactionId": "TXN-987654321",
      "notes": "银行卡支付"
    }
  ],
  "applyDeposits": [
    {
      "depositId": "deposit-uuid",
      "amount": 200.00
    }
  ],
  "notes": "混合支付方式",
  "generateReceipt": true
}
```

**响应**:
```json
{
  "success": true,
  "message": "支付处理成功。总金额: $1150.00",
  "data": {
    "payments": [
      {
        "id": "payment-uuid-1",
        "amount": 500.00,
        "paymentMethod": "cash",
        "transactionId": "TXN-123456789"
      },
      {
        "id": "payment-uuid-2",
        "amount": 450.00,
        "paymentMethod": "card",
        "transactionId": "TXN-987654321"
      }
    ],
    "depositApplications": [
      {
        "depositId": "deposit-uuid",
        "amount": 200.00,
        "appliedAt": "2025-01-09T10:30:00.000Z"
      }
    ],
    "updatedBill": {
      "id": "bill-uuid",
      "remainingAmount": 0.00,
      "status": "paid"
    },
    "receipts": [
      {
        "id": "receipt-uuid",
        "receiptNumber": "REC-20250109-123456"
      }
    ]
  }
}
```

### 2. 获取支付记录

**端点**: `GET /api/payments`

**查询参数**:
- `page`, `limit`: 分页参数
- `bill` (string): 账单ID筛选
- `patient` (string): 患者ID筛选
- `paymentMethod` (string): 支付方式筛选
- `startDate`, `endDate`: 日期范围

### 3. 生成收据

**端点**: `GET /api/payments/{id}/receipt`

**响应**:
```json
{
  "success": true,
  "data": {
    "receiptNumber": "REC-20250109-123456",
    "payment": {
      "amount": 500.00,
      "paymentMethod": "cash",
      "paymentDate": "2025-01-09T10:30:00.000Z"
    },
    "patient": {
      "fullName": "张三",
      "phone": "13800138000"
    },
    "clinic": {
      "name": "北海医美诊所",
      "address": "北京市朝阳区建国路88号",
      "phone": "010-12345678"
    }
  }
}
```

## 押金管理 API

### 1. 创建押金

**端点**: `POST /api/deposits`

**请求体**:
```json
{
  "patient": "patient-uuid",
  "amount": 2000.00,
  "purpose": "治疗预付款",
  "expiryDate": "2025-12-31T23:59:59.000Z",
  "notes": "年度治疗套餐押金"
}
```

### 2. 押金抵扣账单

**端点**: `POST /api/deposits/apply-to-bill`

**请求体**:
```json
{
  "depositId": "deposit-uuid",
  "billId": "bill-uuid",
  "amount": 500.00
}
```

### 3. 押金退款

**端点**: `POST /api/deposits/refund`

**请求体**:
```json
{
  "depositId": "deposit-uuid",
  "amount": 1000.00,
  "reason": "患者要求退款",
  "refundMethod": "original"
}
```

## 财务报告 API

### 1. 获取分析数据

**端点**: `GET /api/reports/analytics`

**查询参数**:
- `startDate` (string): 开始日期
- `endDate` (string): 结束日期
- `period` (string): 时间段 (daily/weekly/monthly)

**响应**:
```json
{
  "period": {
    "startDate": "2025-01-01T00:00:00.000Z",
    "endDate": "2025-01-31T23:59:59.000Z",
    "days": 31
  },
  "revenue": {
    "totalRevenue": 125000.00,
    "averageTransactionValue": 277.78,
    "totalBilled": 150000.00,
    "totalOutstanding": 25000.00,
    "collectionRate": 83.33
  },
  "paymentMethods": {
    "cash": { "count": 200, "amount": 50000.00 },
    "card": { "count": 150, "amount": 37500.00 },
    "wechat": { "count": 100, "amount": 25000.00 },
    "alipay": { "count": 50, "amount": 12500.00 }
  },
  "summary": {
    "totalTransactions": 500,
    "totalBills": 450,
    "totalDeposits": 50,
    "totalPatients": 300
  }
}
```

### 2. 财务报告

**端点**: `GET /api/reports/financial`

**查询参数**:
- `type` (string): 报告类型 (daily/monthly/yearly)
- `startDate`, `endDate`: 日期范围

## 批量操作 API

### 1. 批量账单操作

**端点**: `POST /api/bills/bulk-operations`

**请求体**:
```json
{
  "operation": "updateStatus",
  "billIds": ["bill-uuid-1", "bill-uuid-2", "bill-uuid-3"],
  "data": {
    "status": "paid",
    "notes": "批量标记为已支付"
  }
}
```

**支持的操作**:
- `updateStatus`: 批量更新状态
- `applyDiscount`: 批量应用折扣
- `sendReminders`: 批量发送提醒
- `generateReports`: 生成批量报告

## 错误处理

### 错误响应格式

```json
{
  "error": "错误描述",
  "code": "ERROR_CODE",
  "details": {
    "field": "具体错误信息"
  },
  "timestamp": "2025-01-09T10:00:00.000Z"
}
```

### 常见错误代码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `429 Too Many Requests`: 请求频率过高
- `500 Internal Server Error`: 服务器内部错误

### 业务错误代码

- `INVALID_AMOUNT`: 金额无效
- `BILL_NOT_FOUND`: 账单不存在
- `INSUFFICIENT_PERMISSIONS`: 权限不足
- `PAYMENT_ALREADY_PROCESSED`: 支付已处理
- `DEPOSIT_EXPIRED`: 押金已过期

## 速率限制

- **一般API**: 每分钟60次请求
- **支付API**: 每小时10次请求
- **批量操作**: 每小时5次请求

## 数据格式

### 日期格式
所有日期使用ISO 8601格式：`YYYY-MM-DDTHH:mm:ss.sssZ`

### 金额格式
所有金额使用数字类型，保留2位小数，单位为美金(USD)

### ID格式
所有ID使用UUID v4格式

## 版本控制

当前API版本：`v1.0.0`

API版本通过URL路径指定：`/api/v1/bills`

## 联系信息

- **技术支持**: <EMAIL>
- **API文档**: https://docs.clinic.com/api
- **更新日志**: https://docs.clinic.com/changelog
