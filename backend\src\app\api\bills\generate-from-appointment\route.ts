import { NextRequest, NextResponse } from 'next/server';
import { authenticateWithPayload, makeAuthenticatedPayloadRequest } from '../../../../lib/payload-auth-middleware';
import { Appointment, Bill } from '../../../../payload-types';

/**
 * POST /api/bills/generate-from-appointment - Generate bill from appointment
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate with Payload CMS
    const authContext = await authenticateWithPayload(request);
    if (!authContext) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const {
      appointmentId,
      billType = 'treatment',
      additionalItems = [],
      applyDiscount = false,
      discountRate = 0,
      discountAmount = 0,
      taxRate = 0,
      notes = '',
      dueDate,
      autoApplyDeposits = false
    } = await request.json();

    if (!appointmentId) {
      return NextResponse.json(
        { error: 'Appointment ID is required' },
        { status: 400 }
      );
    }

    // Get appointment details
    const appointmentResult = await makeAuthenticatedPayloadRequest(
      authContext,
      'appointments',
      'findByID',
      {
        id: appointmentId,
        depth: 2, // Include patient, treatment, and practitioner
      }
    );

    if (!appointmentResult) {
      return NextResponse.json(
        { error: 'Appointment not found' },
        { status: 404 }
      );
    }

    // Type cast to Appointment
    const appointment = appointmentResult as Appointment;

    // Calculate bill amounts
    const subtotal = appointment.price || 0;
    const discountAmount = 0; // Can be customized
    const taxAmount = 0; // Can be customized
    const totalAmount = subtotal + taxAmount - discountAmount;

    // Set default due date (30 days from now)
    const dueDate = new Date();
    dueDate.setDate(dueDate.getDate() + 30);

    // Create bill data
    const billData = {
      patient: typeof appointment.patient === 'number' ? appointment.patient : appointment.patient.id,
      appointment: appointmentId,
      treatment: appointment.treatment ? (typeof appointment.treatment === 'number' ? appointment.treatment : appointment.treatment.id) : null,
      billType,
      status: 'draft',
      subtotal,
      discountAmount,
      taxAmount,
      totalAmount,
      paidAmount: 0,
      description: `${billType === 'consultation' ? '咨询' : '治疗'}账单 - ${typeof appointment.treatment === 'object' && appointment.treatment?.name ? appointment.treatment.name : '服务'}`,
      createdBy: authContext.user.id,
      dueDate: dueDate.toISOString(),
    };

    // Create bill
    const billResult = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'create',
      {
        data: billData,
      }
    );

    // Type cast to Bill
    const bill = billResult as Bill;

    // Create main bill item for the appointment
    const mainBillItem = {
      bill: bill.id,
      itemType: billType === 'consultation' ? 'consultation' : 'treatment',
      itemId: appointment.treatment ? (typeof appointment.treatment === 'number' ? appointment.treatment : appointment.treatment.id) : null,
      itemName: typeof appointment.treatment === 'object' && appointment.treatment?.name ? appointment.treatment.name : '服务',
      description: `${typeof appointment.treatment === 'object' && appointment.treatment?.description ? appointment.treatment.description : ''} - ${new Date(appointment.appointmentDate).toLocaleDateString()}`,
      quantity: 1,
      unitPrice: appointment.price || 0,
      discountRate: 0,
    };

    await makeAuthenticatedPayloadRequest(
      authContext,
      'bill-items',
      'create',
      {
        data: mainBillItem,
      }
    );

    // Create additional bill items if provided
    for (const item of additionalItems) {
      const billItem = {
        bill: bill.id,
        ...item,
      };

      await makeAuthenticatedPayloadRequest(
        authContext,
        'bill-items',
        'create',
        {
          data: billItem,
        }
      );
    }

    // Get the complete bill with items
    const completeBill = await makeAuthenticatedPayloadRequest(
      authContext,
      'bills',
      'findByID',
      {
        id: bill.id,
        depth: 3,
      }
    );

    return NextResponse.json({
      message: 'Bill generated successfully from appointment',
      bill: completeBill,
    });
  } catch (error) {
    console.error('Error generating bill from appointment:', error);
    return NextResponse.json(
      { error: 'Failed to generate bill from appointment' },
      { status: 500 }
    );
  }
}
