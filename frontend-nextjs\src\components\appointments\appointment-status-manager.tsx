'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { 
  IconChevronDown, 
  IconCheck, 
  IconX, 
  IconClock,
  IconCalendarCheck,
  IconUserX
} from '@tabler/icons-react';
import { Appointment } from '@/types/clinic';
import { appointmentsApi } from '@/lib/api';
import { appointmentNotifications } from '@/lib/appointment-notifications';
import { useRole } from '@/lib/role-context';

interface AppointmentStatusManagerProps {
  appointment: Appointment;
  onStatusChange?: (appointment: Appointment, newStatus: string) => void;
  compact?: boolean;
}

const statusConfig = {
  'scheduled': {
    label: '已预约',
    color: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
    icon: IconClock,
    nextStates: ['confirmed', 'cancelled'],
  },
  'confirmed': {
    label: '已确认',
    color: 'bg-green-100 text-green-800 hover:bg-green-200',
    icon: IconCalendarCheck,
    nextStates: ['completed', 'no-show', 'cancelled'],
  },
  'completed': {
    label: '已完成',
    color: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
    icon: IconCheck,
    nextStates: [],
  },
  'cancelled': {
    label: '已取消',
    color: 'bg-red-100 text-red-800 hover:bg-red-200',
    icon: IconX,
    nextStates: ['scheduled'],
  },
  'no-show': {
    label: '未到诊',
    color: 'bg-amber-100 text-amber-800 hover:bg-amber-200',
    icon: IconUserX,
    nextStates: ['scheduled', 'cancelled'],
  },
};

const statusTransitionLabels = {
  'scheduled': '预约',
  'confirmed': '确认',
  'completed': '完成',
  'cancelled': '取消',
  'no-show': '标记未到诊',
};

export function AppointmentStatusManager({
  appointment,
  onStatusChange,
  compact = false,
}: AppointmentStatusManagerProps) {
  const { hasPermission } = useRole();
  const [loading, setLoading] = useState(false);
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean;
    newStatus: string;
    title: string;
    description: string;
  }>({
    open: false,
    newStatus: '',
    title: '',
    description: '',
  });

  const currentStatus = appointment.status;
  const config = statusConfig[currentStatus as keyof typeof statusConfig];
  const StatusIcon = config?.icon || IconClock;

  const canChangeStatus = hasPermission('canUpdateAppointments');

  const handleStatusChange = async (newStatus: string) => {
    if (!canChangeStatus) {
      appointmentNotifications.error.permissionDenied('更改预约状态');
      return;
    }

    // Show confirmation dialog for certain status changes
    if (newStatus === 'cancelled' || newStatus === 'no-show' || newStatus === 'completed') {
      const statusLabel = statusTransitionLabels[newStatus as keyof typeof statusTransitionLabels];
      setConfirmDialog({
        open: true,
        newStatus,
        title: `确认${statusLabel}预约`,
        description: `您确定要将 ${appointment.patient.fullName} 的预约状态更改为"${statusConfig[newStatus as keyof typeof statusConfig].label}"吗？`,
      });
      return;
    }

    // Direct status change for non-destructive actions
    await performStatusChange(newStatus);
  };

  const performStatusChange = async (newStatus: string) => {
    setLoading(true);
    try {
      const updatedAppointment = await appointmentsApi.update(appointment.id, {
        status: newStatus as any,
      });

      appointmentNotifications.appointment.statusChanged(
        updatedAppointment,
        currentStatus,
        newStatus
      );

      onStatusChange?.(updatedAppointment, newStatus);
    } catch (error) {
      console.error('Failed to update appointment status:', error);
      appointmentNotifications.error.saveFailed(true);
    } finally {
      setLoading(false);
      setConfirmDialog({ ...confirmDialog, open: false });
    }
  };

  const handleConfirmStatusChange = () => {
    performStatusChange(confirmDialog.newStatus);
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        <Badge className={config?.color}>
          <StatusIcon className="h-3 w-3 mr-1" />
          {config?.label}
        </Badge>
        
        {canChangeStatus && config?.nextStates.length > 0 && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                disabled={loading}
                className="h-6 w-6 p-0"
              >
                <IconChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {config.nextStates.map((status) => {
                const nextConfig = statusConfig[status as keyof typeof statusConfig];
                const NextIcon = nextConfig.icon;
                return (
                  <DropdownMenuItem
                    key={status}
                    onClick={() => handleStatusChange(status)}
                  >
                    <NextIcon className="h-4 w-4 mr-2" />
                    {statusTransitionLabels[status as keyof typeof statusTransitionLabels]}
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        <AlertDialog open={confirmDialog.open} onOpenChange={(open) => 
          setConfirmDialog({ ...confirmDialog, open })
        }>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
              <AlertDialogDescription>
                {confirmDialog.description}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>取消</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleConfirmStatusChange}
                disabled={loading}
              >
                确认
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-2">
        <Badge className={config?.color}>
          <StatusIcon className="h-4 w-4 mr-2" />
          {config?.label}
        </Badge>
        
        <div className="text-sm text-muted-foreground">
          预约时间: {new Date(appointment.appointmentDate).toLocaleString('zh-CN')}
        </div>
      </div>

      {canChangeStatus && config?.nextStates.length > 0 && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={loading}
            >
              更改状态
              <IconChevronDown className="h-4 w-4 ml-2" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <div className="px-2 py-1.5 text-sm font-medium text-muted-foreground">
              可用操作
            </div>
            <DropdownMenuSeparator />
            {config.nextStates.map((status) => {
              const nextConfig = statusConfig[status as keyof typeof statusConfig];
              const NextIcon = nextConfig.icon;
              return (
                <DropdownMenuItem
                  key={status}
                  onClick={() => handleStatusChange(status)}
                >
                  <NextIcon className="h-4 w-4 mr-2" />
                  {statusTransitionLabels[status as keyof typeof statusTransitionLabels]}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>
      )}

      <AlertDialog open={confirmDialog.open} onOpenChange={(open) => 
        setConfirmDialog({ ...confirmDialog, open })
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{confirmDialog.title}</AlertDialogTitle>
            <AlertDialogDescription>
              {confirmDialog.description}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmStatusChange}
              disabled={loading}
            >
              确认
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
